{"autoplayBgm": true, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "LRPG2_TheWanderingBard_noPerc_noMel_Loop", "pan": 0, "pitch": 100, "volume": 30}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": true, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 20, "note": "<Zoom: 200%>\n<No Weather>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 32, "width": 15, "data": [3204, 3228, 3228, 3228, 3228, 3228, 3212, 3228, 3228, 3228, 3228, 3228, 3228, 3228, 3208, 3224, 4547, 4546, 4546, 4546, 4550, 3232, 4547, 4546, 4546, 4546, 4546, 4546, 4550, 3216, 3224, 4553, 4552, 4552, 4552, 4556, 3232, 4553, 4552, 4552, 4552, 4552, 4552, 4556, 3216, 3224, 6834, 6820, 6820, 6820, 6836, 3232, 5922, 5908, 5908, 5908, 5908, 5908, 5924, 3216, 3224, 6816, 6800, 6800, 6800, 6824, 3232, 5904, 5888, 5888, 5888, 5888, 5888, 5912, 3216, 3224, 6840, 6828, 6812, 6828, 6838, 3232, 5928, 5896, 5888, 5888, 5888, 5888, 5912, 3216, 3206, 3233, 3245, 6832, 3243, 3233, 3231, 3237, 5904, 5888, 5888, 5888, 5888, 5912, 3216, 3224, 4547, 4550, 6844, 4547, 4546, 4550, 3232, 5928, 5916, 5916, 5916, 5900, 5926, 3216, 3224, 4553, 4556, 5978, 4553, 4552, 4556, 3244, 4691, 4690, 4690, 4694, 5920, 4695, 3216, 3224, 5970, 5956, 5939, 5956, 5956, 5972, 4551, 4697, 4696, 4696, 4700, 5932, 4701, 3216, 3224, 5952, 5936, 5936, 5936, 5936, 5960, 4557, 5970, 5956, 5956, 5956, 5956, 5972, 3216, 3224, 5952, 5936, 5936, 5936, 5936, 5942, 5969, 5945, 5936, 5936, 5936, 5936, 5960, 3216, 3224, 5952, 5936, 5936, 5936, 5936, 5960, 3242, 5976, 5944, 5936, 5936, 5936, 5960, 3216, 3224, 5976, 5964, 5948, 5964, 5964, 5974, 3241, 3237, 5952, 5936, 5936, 5936, 5960, 3216, 3224, 4691, 4694, 5968, 4691, 4690, 4694, 4551, 3232, 5976, 5964, 5964, 5964, 5974, 3216, 3224, 4697, 4700, 5980, 4697, 4696, 4700, 4557, 3232, 6882, 6868, 6868, 6868, 6884, 3216, 3224, 5922, 5908, 5908, 5908, 5908, 5908, 5924, 3232, 6864, 6848, 6848, 6848, 6872, 3216, 3224, 5904, 5888, 5888, 5888, 5888, 5888, 5912, 3232, 6864, 6848, 6848, 6848, 6872, 3216, 3224, 5928, 5916, 5916, 5916, 5916, 5916, 5926, 3232, 6888, 6876, 6860, 6876, 6886, 3216, 3202, 3220, 3220, 3220, 3220, 3220, 3220, 3220, 3203, 3220, 3236, 6880, 3234, 3220, 3201, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 390, 390, 390, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 390, 390, 390, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3858, 3844, 3860, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3824, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 17, 0, 0, 17, 0, 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 25, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 418, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 434, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 548, 0, 311, 0, 0, 488, 101, 0, 0, 101, 0, 0, 0, 0, 105, 556, 568, 319, 0, 329, 496, 109, 469, 470, 109, 0, 0, 0, 439, 0, 0, 0, 327, 0, 337, 504, 0, 477, 478, 0, 0, 0, 0, 447, 0, 0, 0, 0, 0, 345, 0, 0, 485, 486, 0, 328, 0, 0, 875, 876, 0, 0, 309, 0, 353, 0, 0, 0, 0, 0, 336, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 384, 0, 0, 0, 344, 0, 0, 0, 0, 0, 0, 0, 0, 0, 417, 392, 419, 572, 0, 352, 0, 0, 567, 0, 0, 0, 0, 572, 0, 307, 308, 0, 0, 0, 0, 0, 0, 575, 0, 0, 0, 0, 580, 0, 315, 316, 0, 0, 0, 328, 0, 0, 0, 417, 418, 419, 0, 0, 0, 323, 324, 0, 0, 0, 336, 0, 0, 0, 425, 426, 427, 0, 0, 0, 0, 0, 0, 0, 0, 344, 0, 0, 0, 0, 0, 0, 0, 0, 0, 306, 0, 0, 0, 0, 352, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 329, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 337, 0, 0, 0, 0, 0, 468, 0, 0, 0, 0, 0, 0, 0, 0, 345, 0, 0, 0, 0, 0, 476, 0, 0, 0, 0, 0, 0, 0, 0, 353, 0, 384, 0, 0, 0, 484, 0, 771, 0, 0, 0, 0, 0, 0, 0, 433, 392, 435, 0, 0, 0, 0, 779, 0, 0, 572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$SingleStair", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 14}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$SingleStair", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 14}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$SingleStair", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 14}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$SingleStair", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 14}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$SingleStair", "direction": 2, "pattern": 2, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 14}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "characters/!$Fireplace_kitchen", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 3}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "characters/!Decoration_static", "direction": 6, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 8}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "characters/!Decoration_static", "direction": 6, "pattern": 0, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 9}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "characters/!Decoration_static", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 15}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 201, "indent": 0, "parameters": [0, 295, 2, 4, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 11, "y": 19}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Black", "Duration:num": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Color = Black"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "RadialLightChangePlayerSettings", "RADIAL LIGHT: Change Player Settings", {"Settings:struct": "{\"General\":\"\",\"enabled:eval\":\"true\",\"Properties\":\"\",\"filename:str\":\"\",\"color:str\":\"#ffffff\",\"radius:num\":\"100\",\"intensity:num\":\"0.05\",\"Optional\":\"\",\"angle:num\":\"0\",\"rotateSpeed:num\":\"+0\",\"blendMode:num\":\"3\",\"opacity:num\":\"64\",\"Offsets\":\"\",\"offsetX:num\":\"+0\",\"offsetY:num\":\"+0\"}", "Behavior:struct": "{\"Blink\":\"\",\"blinkRate:num\":\"0.00\",\"blinkModifier:num\":\"-0.50\",\"Flicker\":\"\",\"flickerRate:num\":\"0.00\",\"flickerModifier:num\":\"-0.50\",\"Flash\":\"\",\"flashRate:num\":\"0.00\",\"flashModifier:num\":\"+0.50\",\"Flare\":\"\",\"flareRate:num\":\"0.00\",\"flareModifier:num\":\"+0.50\",\"Glow\":\"\",\"glowRate:num\":\"0.00\",\"glowSpeed:num\":\"0.10\",\"glowRng:eval\":\"true\",\"Pulse\":\"\",\"pulseRate:num\":\"0.00\",\"pulseSpeed:num\":\"0.10\",\"pulseRng:eval\":\"true\",\"Pattern\":\"\",\"patternName:str\":\"none\",\"pattern:str\":\"\",\"patternDelay:num\":\"6\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Settings = {\"General\":\"\",\"enabled:eval\":\"true\",\"Properties\"…"]}, {"code": 657, "indent": 0, "parameters": ["Behavior = {\"Blink\":\"\",\"blinkRate:num\":\"0.00\",\"blinkModifie…"]}, {"code": 357, "indent": 0, "parameters": ["Wave 5/VisuMZ_2_HorrorEffects", "MapColorCreate", "Map: Color Effect Create", {"type:str": "Greyscale"}]}, {"code": 657, "indent": 0, "parameters": ["Effect Type = Greyscale"]}, {"code": 357, "indent": 0, "parameters": ["Wave 5/VisuMZ_2_HorrorEffects", "MapNoiseCreate", "Map: Noise Create", {"noise:num": "0.1", "animated:eval": "true"}]}, {"code": 657, "indent": 0, "parameters": ["Noise Rate = 0.1"]}, {"code": 657, "indent": 0, "parameters": ["Noise Animated = true"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 0}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Ghost", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 10, "indent": null}, {"code": 9, "indent": null}, {"code": 9, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 5, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 3}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Ghost", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 10, "indent": null}, {"code": 9, "indent": null}, {"code": 9, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 5, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 16}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Ghost", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 10, "indent": null}, {"code": 9, "indent": null}, {"code": 9, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 5, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 3}]}