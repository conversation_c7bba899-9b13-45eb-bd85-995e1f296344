
/*:
 * @target MZ
 * @plugindesc [v1.04] Adds FF6-style airship controls using UltraMode7 — yaw turning, pitch-based altitude, custom parallax effects, and ground trail at low altitude.
 * <AUTHOR> <PERSON><PERSON> Studio
 * 
 * @help MAUI_FF6Airship.js
 *
 * This plugin enhances UltraMode7 by adding Final Fantasy VI-style airship features.
 * 
 * 🕹️ Airship Controls:
 * - Left/Right  = Turn (yaw)
 * - Up/Down     = Adjust altitude
 * - Shift/X     = Fly forward
 * - Enter/A     = Land
 *
 * 🌪️ Ground Trail Effect:
 * - When flying at low altitude (≤120), the airship leaves a dynamic dust trail on the ground
 * - Trail intensity, size, and speed scale dramatically with altitude
 * - Trail points fade out over time and are automatically cleaned up
 *
 * 🔧 Required Plugins (load in this order):
 * - UltraMode7.js by <PERSON> "Blizzard" Mikić
 * - MAUI_FF6Airship.js
 *
 * ✨ NEW: No longer requires SAN_AnalogMovement!
 * This plugin now includes its own custom 360-degree movement system.
 *
 * ⚠️ Note:
 * While the required plugins are free for commercial use, this plugin is also free to use in commercial projects.
 *
 * 🛠️ Usage Instructions:
 * This plugin overrides several UltraMode7 settings while in an airship:
 * - UltraMode7.CHARACTERS_ADJUST_SPRITE_DIRECTION
 * - UltraMode7.FADE_Z_END
 * - UltraMode7.DEFAULT_FAR_CLIP_Z
 * - UltraMode7.DEFAULT_PARALLAX_DISTANCE
 * - UltraMode7.DEFAULT_CAMERA_Y
 * 
 * To activate UltraMode7 on your world map, add the following tag to the map's note field:
 *   <UltraMode7>
 * For night maps adjust the fade effect color:
 *   <UltraMode7_FadeColor:1,1,1>
 * 
 * A sample parallax image (mauiparallax.png) is included. Use it as a template if you'd like to make your own parallax backgrounds.
 *
 * Questions? Join my Discord:
 * https://discord.com/invite/g2ksjJx
 *
 * Version: 1.04
 *
 * 1.01 - 7.20	Erased accidental line update that was making game run at double speed.
 *				Thank you Lexiand for reporting!
 * 1.02 - 7.21	Now when landing the Yaw turnes left or right, depending on which is closer.
 * 1.03 - 7.24  Fixed issue that locked player looking left, Thanks Alasthorn.
 * 1.04 - 12.19 Added ground trail effect when airship flies at low altitude (≤120).
 *
 */


(() => {
	
	// Plugin compatibility and error checking system
	const PLUGIN_NAME = 'MAUI_FF6Airship';
	const REQUIRED_PLUGINS = ['UltraMode7'];

	// OPTIMIZATION: Debug mode flag to reduce console logging overhead
	const DEBUG_MODE = false; // Set to true only when actively debugging

	// Idempotency guard to avoid double-install on accidental re-evals
	if (window && window.__MAUI_FF6AirshipInstalled) {
	  if (DEBUG_MODE) console.warn('MAUI_FF6Airship: already installed, skipping re-install');
	  return;
	}
	if (window) window.__MAUI_FF6AirshipInstalled = true;

	// Check for required plugins
	function checkRequiredPlugins() {
	  const missingPlugins = [];
	  REQUIRED_PLUGINS.forEach(pluginName => {
	    if (!PluginManager.parameters(pluginName)) {
	      missingPlugins.push(pluginName);
	    }
	  });

	  if (missingPlugins.length > 0) {
	    if (DEBUG_MODE) {
	    console.error(`❌ ${PLUGIN_NAME}: Missing required plugins: ${missingPlugins.join(', ')}`);
	    console.error(`❌ ${PLUGIN_NAME}: Plugin will not function correctly without these dependencies.`);
	    }
	    return false;
	  }

	  if (DEBUG_MODE) console.log(`✅ ${PLUGIN_NAME}: All required plugins found`);
	  return true;
	}

	// Initialize plugin compatibility check
	if (!checkRequiredPlugins()) {
	  if (DEBUG_MODE) console.error(`❌ ${PLUGIN_NAME}: Plugin initialization failed due to missing dependencies.`);
	  return;
	}

	//=============================================================================
	// INTEGRATED SAN_ANALOGMOVE - AIRSHIP ONLY VERSION
	//=============================================================================
	// Based on SAN_AnalogMove.js v3.1.5 by Sanshiro
	// Integrated and modified to work only with airship movement

	//-----------------------------------------------------------------------------
	// Vector - Mathematical vector operations for smooth movement
	//-----------------------------------------------------------------------------
	function Vector() {
	    this.initialize.apply(this, arguments);
	}

	Vector.prototype.initialize = function(x, y) {
	    this._x = x || 0.0;
	    this._y = y || 0.0;
	};

	Vector.prototype.x = function() { return this._x; };
	Vector.prototype.y = function() { return this._y; };
	Vector.prototype.setX = function(x) { this._x = x; };
	Vector.prototype.setY = function(y) { this._y = y; };

	Vector.prototype.len = function() {
	    return Math.sqrt(this._x * this._x + this._y * this._y);
	};

	Vector.prototype.dir = function() {
	    return Math.atan2(this._y, this._x);
	};

	Vector.prototype.clone = function() {
	    return new Vector(this._x, this._y);
	};

	Vector.prototype.add2 = function(vec) {
	    return new Vector(this._x + vec.x(), this._y + vec.y());
	};

	Vector.prototype.sub2 = function(vec) {
	    return new Vector(this._x - vec.x(), this._y - vec.y());
	};

	Vector.rect = function(x, y) {
	    return new Vector(x, y);
	};

	Vector.polar = function(len, dir) {
	    return new Vector(len * Math.cos(dir), len * Math.sin(dir));
	};

	//-----------------------------------------------------------------------------
	// AirshipMover - Movement controller for airship
	//-----------------------------------------------------------------------------
	function AirshipMover() {
	    this.initialize.apply(this, arguments);
	}

	AirshipMover.prototype.initialize = function(character) {
	    this._character = character;
	    this._posVec = Vector.rect(character._realX, character._realY);
	    this._velVec = Vector.rect(0.0, 0.0);
	    this._dpf = 12.0 / 60.0; // Dots per frame (movement speed) - increased from 4.0 to 8.0 for faster airship
	};

	AirshipMover.prototype.character = function() {
	    return this._character;
	};

	AirshipMover.prototype.dpf = function() {
      // Base movement speed (dots per frame)
      let speed = this._dpf;
      // Altitude-based speed multiplier: 1.0 at min altitude → 1.5 at max altitude
      try {
        const airship = $gameMap && $gameMap.airship ? $gameMap.airship() : null;
        if (airship && typeof airship._altitude === 'number') {
          const minAlt = AIRSHIP_CONFIG.ALTITUDE.MIN;
          const maxAlt = AIRSHIP_CONFIG.ALTITUDE.MAX;
          const ratio = Math.max(0, Math.min(1, (airship._altitude - minAlt) / Math.max(1, (maxAlt - minAlt))));
          const multiplier = 1.0 + 0.5 * ratio; // up to +50% at max altitude
          speed *= multiplier;
        }
        // Apply boost multiplier when active
        if (BOOST_ENABLED && boostActive) speed *= BOOST_MULTIPLIER;
      } catch (_) {}
      return speed;
	};

	AirshipMover.prototype.update = function() {
	    this.updateVelocity();
	    this.updatePosition();
	    this.updateCharacterPosition();
	};

	AirshipMover.prototype.updateVelocity = function() {
    var velVecLen = 0.0;
    var velVecDir = 0.0;

    // In airship mode, forward movement is triggered by 'up' action.
    // If controls are swapped, 'up' is currently on Shift; otherwise it is on the Up arrow.
    const forwardPressed = (typeof ControlManager !== 'undefined' && ControlManager && ControlManager.controlsSwapped === true)
      ? Input.isPressed('up')
      : Input.isPressed('shift');
    if (forwardPressed) {
        velVecLen = this.dpf();

        // The direction of movement is always "forward" from the airship's perspective.
        // "Forward" corresponds to the 'up' direction in RPG Maker's directional system (dir8 = 8).
        var baseDir = this.dir8ToRad(8);

        // Get current camera yaw and convert to radians
        var cameraYaw = ($gameTemp._yawFacing || 0) * Math.PI / 180;

        // Adjust direction relative to camera yaw to move in the direction the airship is facing
        velVecDir = baseDir - cameraYaw;

    }

    this._velVec = Vector.polar(velVecLen, velVecDir);
};

	AirshipMover.prototype.dir8ToRad = function(dir8) {
	    // Convert RPG Maker dir8 to radians
	    // 1=down-left, 2=down, 3=down-right, 4=left, 6=right, 7=up-left, 8=up, 9=up-right
	    var x = (dir8 % 3 === 0 ? 1.0 : (dir8 % 3 === 1 ? -1.0 : 0.0));
	    var y = (dir8 / 3 <= 1 ? 1.0 : (dir8 / 3 > 2 ? -1.0 : 0.0));
	    return Math.atan2(y, x);
	};

	AirshipMover.prototype.updatePosition = function() {
	    this._posVec = this._posVec.add2(this._velVec);
	};

	AirshipMover.prototype.updateCharacterPosition = function() {
    // Wrap/clamp position at map edges to avoid disappearing beyond boundaries
    const map = $gameMap;
    if (map) {
      const mapW = Math.max(1, map.width());
      const mapH = Math.max(1, map.height());
      let nx = this._posVec.x();
      let ny = this._posVec.y();

      if (map.isLoopHorizontal()) {
        nx = ((nx % mapW) + mapW) % mapW;
      } else {
        nx = Math.min(mapW - 1, Math.max(0, nx));
      }

      if (map.isLoopVertical()) {
        ny = ((ny % mapH) + mapH) % mapH;
      } else {
        ny = Math.min(mapH - 1, Math.max(0, ny));
      }

      this._character._realX = nx;
      this._character._realY = ny;
    } else {
	    this._character._realX = this._posVec.x();
	    this._character._realY = this._posVec.y();
    }
	    this._character._x = Math.round(this._character._realX);
	    this._character._y = Math.round(this._character._realY);
	};

	AirshipMover.prototype.distanceMoved = function() {
	    return this._velVec.len();
	};

	//-----------------------------------------------------------------------------
	// Game_CharacterBase - Add airship analog movement support
	//-----------------------------------------------------------------------------

	// Add mover support to characters
Game_CharacterBase.prototype.initAirshipMover = function() {
    // Only initialize for player when in airship
    if (this === $gamePlayer && $gamePlayer.isInAirship()) {
        
        // Store original player and camera settings
        if (!this._originalSettings) {
            this._originalSettings = {
                moveSpeed: $gamePlayer.moveSpeed(),
                cameraYaw: UltraMode7.getYaw(),
                cameraPitch: UltraMode7.getPitch(),
                cameraDistance: (typeof UltraMode7.getCameraDistance === 'function' ? UltraMode7.getCameraDistance() : undefined),
                fov: (typeof UltraMode7.getFov === 'function' ? UltraMode7.getFov() : undefined),
                farClipZ: (typeof UltraMode7.getFarClipZ === 'function' ? UltraMode7.getFarClipZ() : undefined),
                cameraY: (typeof UltraMode7.getCameraY === 'function' ? UltraMode7.getCameraY() : UltraMode7.DEFAULT_CAMERA_Y)
            };
        }

        this._airshipMover = new AirshipMover(this);
        if (DEBUG_MODE) console.log('✈️ Airship mover initialized for player');
    }
};

	Game_CharacterBase.prototype.hasAirshipMover = function() {
	    return !!this._airshipMover;
	};

	Game_CharacterBase.prototype.airshipMover = function() {
	    return this._airshipMover;
	};

	Game_CharacterBase.prototype.canAirshipAnalogMove = function() {
      // Only allow analog movement for player when in airship and not during get-on/get-off or follower gather
      if (this !== $gamePlayer) return false;
      if (!$gamePlayer.isInAirship()) return false;
      if (!this.hasAirshipMover()) return false;
      if (typeof pitchSetActive !== 'undefined' && pitchSetActive) return false;
      if ($gamePlayer._vehicleGettingOn || $gamePlayer._vehicleGettingOff) return false;
      if ($gamePlayer.areFollowersGathering && $gamePlayer.areFollowersGathering()) return false;
      return true;
	};

	Game_CharacterBase.prototype.updateAirshipAnalogMove = function() {
	    this.airshipMover().update();
	    if (!this.isMoving()) {
	        this.updateStop();
	    } else {
	        this.resetStopCount();
	    }
	    this.updateAnimation();
	};

	// Override update to use airship analog movement when appropriate
	const _Game_CharacterBase_update = Game_CharacterBase.prototype.update;
	Game_CharacterBase.prototype.update = function() {
	    if (this.canAirshipAnalogMove()) {
	            this.updateAirshipAnalogMove();
	        return;
	    }
	    _Game_CharacterBase_update.call(this);
	};

	// Override isMoving to work with airship mover
	const _Game_CharacterBase_isMoving = Game_CharacterBase.prototype.isMoving;
	Game_CharacterBase.prototype.isMoving = function() {
	    if (this.hasAirshipMover()) {
	        return this.airshipMover().distanceMoved() > 0;
	    }
	    return _Game_CharacterBase_isMoving.call(this);
	};

	if (DEBUG_MODE) {
	    console.log('✅ Integrated SAN_AnalogMove (Airship Only) initialized');
	}



  const ultraParams = PluginManager.parameters('UltraMode7');
	
  const mauiPitch = PluginManager.parameters('MAUI_FF6Airship');
  
  // Configuration system for easy customization
  const AIRSHIP_CONFIG = {
    // UltraMode7 overrides
    ULTRA_MODE7: {
      CHARACTERS_ADJUST_SPRITE_DIRECTION: false,
      FADE_Z_END: 4000,
      DEFAULT_FAR_CLIP_Z: 4500,
      DEFAULT_PARALLAX_DISTANCE: 2000
    },
    
    // Camera settings
    CAMERA: {
      DEFAULT_Y: UltraMode7.DEFAULT_CAMERA_Y,
      TARGET_Y: 144,
      DEFAULT_PITCH: parseFloat(ultraParams.DEFAULT_PITCH),
      TARGET_PITCH: parseFloat(55)
    },
    
    // Altitude limits
    ALTITUDE: {
      MIN: 48,
      MAX: 200
    },
    
    // Effect thresholds - INCREASED for more dynamic range
    EFFECTS: {
      TRAIL_LOW_ALTITUDE: 120, // Increased from 60 to 120
      RIPPLE_LOW_ALTITUDE: 100, // Increased from 60 to 100
      DUST_CLOUD_THRESHOLD: 80, // Increased from 50 to 80
      CLOUD_FLYING_ALTITUDE: 150,
      WIND_LINES_ALTITUDE: 120
    },
    
    // Performance settings - OPTIMIZED for better balance
    PERFORMANCE: {
      UPDATE_FREQUENCY: 3, // Increased from 2 to 3 for better performance
      MAX_SPRITES_PER_TYPE: 50, // Reduced from 60 to 50 for better performance
      EMERGENCY_CLEANUP_THRESHOLD: 500 // Reduced from 600 to 500
    }
  };
  
  // Screen overlays creation
  Scene_Map.prototype._createBoostVisuals = function() {
    if (!this._airshipOverlays) return;
    if (!this._boostVignette) {
      const bm = new Bitmap(Graphics.width, Graphics.height);
      const ctx = bm.context; const cx = Graphics.width/2; const cy = Graphics.height/2;
      const grad = ctx.createRadialGradient(cx, cy, Math.min(cx,cy)*0.2, cx, cy, Math.max(cx,cy));
      grad.addColorStop(0.0, 'rgba(0,0,0,0.0)');
      grad.addColorStop(1.0, 'rgba(0,0,0,0.65)');
      ctx.fillStyle = grad; ctx.fillRect(0,0,Graphics.width, Graphics.height);
      this._boostVignette = new Sprite(bm);
      this._boostVignette.alpha = 0.0; // only visible during boost
      this._airshipOverlays.addChild(this._boostVignette);
    }
    if (!this._boostFlare) {
      const bmF = new Bitmap(Graphics.width, Graphics.height);
      const ctxF = bmF.context; const cx = Graphics.width/2; const cy = Graphics.height/2; const r = Math.min(cx,cy)*0.35;
      const gradF = ctxF.createRadialGradient(cx, cy, 0, cx, cy, r);
      gradF.addColorStop(0.0, 'rgba(255,255,255,0.35)');
      gradF.addColorStop(1.0, 'rgba(255,255,255,0.0)');
      ctxF.fillStyle = gradF; ctxF.beginPath(); ctxF.arc(cx, cy, r, 0, Math.PI*2); ctxF.fill();
      this._boostFlare = new Sprite(bmF);
      this._boostFlare.alpha = 0.0; // brief pulse on activation
      this._airshipOverlays.addChild(this._boostFlare);
    }
  };

  Scene_Map.prototype.updateBoostVisuals = function() {
    if (!this._airshipOverlays) return;
    const t = Math.max(0, Math.min(1, boostCharge01||0));
    // Vignette tint via overall alpha modulation using boost color as multiplier on flare only
    const targetVignetteAlpha = (boostActive ? 0.22 : 0.0);
    this._boostVignette && (this._boostVignette.alpha += (targetVignetteAlpha - this._boostVignette.alpha) * 0.12);
    // Flare pulse on activation
    this._boostFlareTimer = this._boostFlareTimer || 0;
    if (boostActive && !this._boostWasActive) {
      this._boostFlareTimer = 12;
      // Emit a stronger screen-space shockwave at the airship position + particle burst + subtle screen shake
      try {
        const airship = $gameMap && $gameMap.airship ? $gameMap.airship() : null;
        if (airship && this._airshipOverlays) {
          const sx = airship.screenX(); const sy = airship.screenY();
          const wave = new PIXI.Graphics();
          wave._life = 18; wave._age = 0; wave._x = sx; wave._y = sy;
          wave.alpha = 1.0; wave.blendMode = PIXI.BLEND_MODES.ADD;
          // Lock wave color to current contrail color
          try { wave._col = getCurrentBoostColorHex(); } catch(_) { wave._col = 0xffffff; }
          this._airshipOverlays.addChild(wave); this._shockwaves.push(wave);
          // Radial spark burst
          try {
            const sparks = 14;
            for (let i=0;i<sparks;i++) {
              const ang = (i / sparks) * Math.PI * 2 + Math.random()*0.2;
              const spd = 2.0 + Math.random()*1.5;
              const vx = Math.cos(ang) * spd;
              const vy = Math.sin(ang) * spd;
              const spr = createPuffSprite('boostSpark', sx, sy, 6 + Math.random()*4, 0.8, 16 + Math.floor(Math.random()*8), vx, vy, 0.06);
              if (spr) { spr.alpha *= 0.9; this._airshipOverlays.addChild(spr); }
            }
          } catch(_) {}
          // Subtle shake
          try { if ($gameScreen && $gameScreen.startShake) $gameScreen.startShake(3, 8, 10); } catch(_) {}
          // Brief time-dilation for extra punch
          try { if ($gameSystem && typeof $gameSystem.setTimeScale === 'function') { $gameSystem.setTimeScale(0.85); this._boostSlowmoTimer = 15; } } catch(_) {}
          // Subtle camera pitch nudge during boost for extra speed feel
          try {
            const airship = $gameMap && $gameMap.airship ? $gameMap.airship() : null;
            const alt = airship && typeof airship._altitude === 'number' ? airship._altitude : 0;
            const basePitch = CameraCalculator && CameraCalculator.calculateDynamicPitch ? CameraCalculator.calculateDynamicPitch(alt) : (UltraMode7 && UltraMode7.getPitch ? UltraMode7.getPitch() : 45);
            const targetPitch = basePitch + 4; // nudge forward
            if (UltraMode7 && UltraMode7.animatePitch) UltraMode7.animatePitch(targetPitch, 18);
            this._boostPitchTimer = 22;
          } catch(_) {}
        }
      } catch(_) {}
    }
    if (this._boostFlareTimer > 0) this._boostFlareTimer--;
    const flareAlpha = Math.max(0, this._boostFlareTimer / 12);
    if (this._boostFlare) {
      this._boostFlare.alpha = Math.pow(flareAlpha, 1.6) * 0.9;
      // Subtle color shift towards boost color
      this._boostFlare.blendColor = this._boostFlare.blendColor || [0,0,0,0];
      const css = boostCssColorForCharge(t);
      // Parse rgba to components quickly
      // we already have components in BOOST_*; approximate by lerp start/mid/end
      // Using a simple mapping by reusing gradient buckets would be heavier here; keep flare white
    }
    this._boostWasActive = !!boostActive;
  };

  // Altitude ambience overlays
  Scene_Map.prototype._createAltitudeAmbience = function() {
    // Removed per user request: do not create thin clouds or color grading
    if (this._thinCloudLayer) { try { if (this._thinCloudLayer.parent) this._thinCloudLayer.parent.removeChild(this._thinCloudLayer); } catch(_) {} this._thinCloudLayer = null; }
    if (this._coolGrade) { try { if (this._coolGrade.parent) this._coolGrade.parent.removeChild(this._coolGrade); } catch(_) {} this._coolGrade = null; }
  };

  Scene_Map.prototype.updateAltitudeAmbience = function() {
    // If no airship, nothing to do
    if (!$gameMap || !$gameMap.airship) return;
    const airship = $gameMap.airship(); if (!airship) return;
    const alt = airship._altitude || 0; const maxAlt = AIRSHIP_CONFIG.ALTITUDE.MAX || 200;
    const ratio = Math.max(0, Math.min(1, alt / Math.max(1, maxAlt)));
    // Fade in thin clouds and cool grade at high altitude
    const targetCloud = Math.pow(Math.max(0, ratio - 0.4) / 0.6, 1.2) * 0.85; // start appearing after 40%
    const targetGrade = Math.pow(Math.max(0, ratio - 0.3) / 0.7, 1.0) * 0.75;
    if (this._thinCloudLayer) this._thinCloudLayer.alpha += (targetCloud - this._thinCloudLayer.alpha) * 0.08;
    if (this._coolGrade) this._coolGrade.alpha += (targetGrade - this._coolGrade.alpha) * 0.08;

    // Update and draw shockwaves
    if (this._shockwaves && this._shockwaves.length) {
      for (let i = this._shockwaves.length - 1; i >= 0; i--) {
        const wave = this._shockwaves[i];
        if (!wave || !wave.parent) { this._shockwaves.splice(i,1); continue; }
        wave.clear();
        wave._age++;
        const k = wave._age / wave._life;
        // expand non-linearly and fade (faster overall)
        const radius = 10 + Math.pow(k, 0.8) * 160;
        const coreAlpha = Math.max(0, 0.9 * (1 - k));
        wave.alpha = coreAlpha;
        // dual-ring: bright inner ring + softer outer glow
        // Slightly darken contrail color for shockwave to add contrast
        const baseCol = (wave._col != null) ? wave._col : getCurrentBoostColorHex();
        const r0 = (baseCol >> 16) & 0xff, g0 = (baseCol >> 8) & 0xff, b0 = baseCol & 0xff;
        const kDark = 0.85; // 15% darker
        const r = Math.max(0, Math.min(255, Math.round(r0 * kDark)));
        const g = Math.max(0, Math.min(255, Math.round(g0 * kDark)));
        const b = Math.max(0, Math.min(255, Math.round(b0 * kDark)));
        const col = (r << 16) | (g << 8) | b;
        wave.lineStyle(3, col, 0.75);
        wave.drawCircle(wave._x, wave._y, radius);
        wave.lineStyle(1, col, 0.35);
        wave.drawCircle(wave._x, wave._y, radius * 1.15);
        // faint fill
        wave.beginFill(col, 0.06);
        wave.drawCircle(wave._x, wave._y, radius * 0.85);
        wave.endFill();
        if (wave._age >= wave._life) { wave.parent.removeChild(wave); this._shockwaves.splice(i,1); }
      }
    }
    // Update slowmo and roll timers
    if (this._boostSlowmoTimer != null) {
      this._boostSlowmoTimer--;
      if (this._boostSlowmoTimer <= 0) {
        try { if ($gameSystem && typeof $gameSystem.setTimeScale === 'function') $gameSystem.setTimeScale(1.0); } catch(_) {}
        this._boostSlowmoTimer = null;
      }
    }
    if (this._boostPitchTimer != null) {
      // Keep boost pitch while boost is active; revert when boost ends
      if (!boostActive) {
        try {
          const airship = $gameMap && $gameMap.airship ? $gameMap.airship() : null;
          const alt = airship && typeof airship._altitude === 'number' ? airship._altitude : 0;
          const basePitch = CameraCalculator && CameraCalculator.calculateDynamicPitch ? CameraCalculator.calculateDynamicPitch(alt) : (UltraMode7 && UltraMode7.getPitch ? UltraMode7.getPitch() : 45);
          if (UltraMode7 && UltraMode7.animatePitch) UltraMode7.animatePitch(basePitch, 12);
        } catch(_) {}
        this._boostPitchTimer = null;
      }
    }
    // Parallax the thin cloud tiling based on player movement so they don't screen-stick
    try {
      if (this._thinCloudLayer && this._thinCloudLayer.origin) {
        const airship = $gameMap && $gameMap.airship ? $gameMap.airship() : null;
        if (airship) {
          const sx = airship.screenX();
          const sy = airship.screenY();
          if (this._thinCloudLastSx == null || this._thinCloudLastSy == null) {
            this._thinCloudLastSx = sx; this._thinCloudLastSy = sy;
          } else {
            const dx = sx - this._thinCloudLastSx;
            const dy = sy - this._thinCloudLastSy;
            // Move clouds opposite to player screen motion with a small parallax factor (far depth)
            const parallax = 0.2 + 0.3 * ratio; // slightly stronger at higher altitude
            this._thinCloudLayer.origin.x -= dx * parallax;
            this._thinCloudLayer.origin.y -= dy * parallax * 0.6;
            this._thinCloudLastSx = sx; this._thinCloudLastSy = sy;
          }
        }
      }
    } catch(_) {}
  };
  
  // Store original UM7 values and desired airship overrides; apply them only while in airship
  const _UM7_ORIG = {
    CHARACTERS_ADJUST_SPRITE_DIRECTION: UltraMode7.CHARACTERS_ADJUST_SPRITE_DIRECTION,
    FADE_Z_END: UltraMode7.FADE_Z_END,
    DEFAULT_FAR_CLIP_Z: UltraMode7.DEFAULT_FAR_CLIP_Z,
    DEFAULT_PARALLAX_DISTANCE: UltraMode7.DEFAULT_PARALLAX_DISTANCE
  };

  const UM7_AIRSHIP_OVERRIDES = {
    CHARACTERS_ADJUST_SPRITE_DIRECTION: AIRSHIP_CONFIG.ULTRA_MODE7.CHARACTERS_ADJUST_SPRITE_DIRECTION,
    FADE_Z_END: AIRSHIP_CONFIG.ULTRA_MODE7.FADE_Z_END,
    DEFAULT_FAR_CLIP_Z: AIRSHIP_CONFIG.ULTRA_MODE7.DEFAULT_FAR_CLIP_Z,
    DEFAULT_PARALLAX_DISTANCE: AIRSHIP_CONFIG.ULTRA_MODE7.DEFAULT_PARALLAX_DISTANCE
  };

  function applyUM7OverridesForAirship() {
    UltraMode7.CHARACTERS_ADJUST_SPRITE_DIRECTION = UM7_AIRSHIP_OVERRIDES.CHARACTERS_ADJUST_SPRITE_DIRECTION;
    UltraMode7.FADE_Z_END = UM7_AIRSHIP_OVERRIDES.FADE_Z_END;
    UltraMode7.DEFAULT_FAR_CLIP_Z = UM7_AIRSHIP_OVERRIDES.DEFAULT_FAR_CLIP_Z;
    UltraMode7.DEFAULT_PARALLAX_DISTANCE = UM7_AIRSHIP_OVERRIDES.DEFAULT_PARALLAX_DISTANCE;
  }

  function restoreUM7Defaults() {
    UltraMode7.CHARACTERS_ADJUST_SPRITE_DIRECTION = _UM7_ORIG.CHARACTERS_ADJUST_SPRITE_DIRECTION;
    UltraMode7.FADE_Z_END = _UM7_ORIG.FADE_Z_END;
    UltraMode7.DEFAULT_FAR_CLIP_Z = _UM7_ORIG.DEFAULT_FAR_CLIP_Z;
    UltraMode7.DEFAULT_PARALLAX_DISTANCE = _UM7_ORIG.DEFAULT_PARALLAX_DISTANCE;
  }
  
  // Plugin Parameters (optional seam controls)
  const PLUGIN_PARAMS = PluginManager.parameters('MAUI_FF6Airship') || {};
  const SEAM_EXTEND_TILES = Number(PLUGIN_PARAMS.SeamExtendTiles || 16);
  const SEAM_GUARD_PADDING = Number(PLUGIN_PARAMS.SeamGuardPadding || 120);
  const SEAM_OVERLAY_ENABLED = String(PLUGIN_PARAMS.SeamOverlayEnabled || 'false') === 'true';
  // Simple HUD params
  const HUD_ENABLED = String(PLUGIN_PARAMS.HudEnabled || 'true') === 'true';
  const HUD_FONT_SIZE = Number(PLUGIN_PARAMS.HudFontSize || 14);
  const HUD_OFFSET_X = Number(PLUGIN_PARAMS.HudOffsetX || 0);
  const HUD_OFFSET_Y = Number(PLUGIN_PARAMS.HudOffsetY || 38);
  const HUD_SHOW_SPEED = String(PLUGIN_PARAMS.HudShowSpeed || 'true') === 'true';
  const HUD_SHOW_ALTITUDE = String(PLUGIN_PARAMS.HudShowAltitude || 'true') === 'true';
  const HUD_TILE_SIZE = Number(PLUGIN_PARAMS.HudTileSize || 48);
  const HUD_SPEED_UNITS = String(PLUGIN_PARAMS.HudSpeedUnits || 'tiles/s'); // 'tiles/s' or 'mph'
  const HUD_TILE_METERS = Number(PLUGIN_PARAMS.HudTileMeters || 5); // meters per tile for mph calc
  // Background removed per request
  // Analog gauge params
  const HUD_ANALOG_ENABLED = true; // Replace text with analog gauges
  const HUD_MAX_MPH = Number(PLUGIN_PARAMS.HudMaxMph || 120);
  const HUD_TILES_MAX = Number(PLUGIN_PARAMS.HudTilesMax || 28.8);
  const HUD_COMPASS_DIAM = Number(PLUGIN_PARAMS.HudCompassDiam || 44);
  const HUD_SPEEDO_DIAM = Number(PLUGIN_PARAMS.HudSpeedoDiam || 44);
  const HUD_NEEDLE_COLOR = String(PLUGIN_PARAMS.HudNeedleColor || '#ff4444');
  const HUD_TICK_COLOR = String(PLUGIN_PARAMS.HudTickColor || '#ffffff');
  const HUD_TEXT_COLOR = String(PLUGIN_PARAMS.HudTextColor || '#ffffff');
  const HUD_HIGH_CONTRAST = String(PLUGIN_PARAMS.HudHighContrast || 'true') === 'true';
  const HUD_LABEL_SIZE_DELTA = Number(PLUGIN_PARAMS.HudLabelSizeDelta || 2);
  const HUD_NEEDLE_WIDTH = Number(PLUGIN_PARAMS.HudNeedleWidth || 3);
  const HUD_MODE = String(PLUGIN_PARAMS.HudMode || 'bottom-center'); // 'bottom-center' | 'sprite' | 'corner'
  const HUD_CORNER = String(PLUGIN_PARAMS.HudCorner || 'top-left');
  const HUD_SCALE = Number(PLUGIN_PARAMS.HudScale || 1.25);
  // Bottom-center layout margin from bottom of the screen
  const HUD_BOTTOM_MARGIN = Number(PLUGIN_PARAMS.HudBottomMargin || 32);
  // Bottom-center layout horizontal padding so dials don't clip
  const HUD_SIDE_PAD = Number(PLUGIN_PARAMS.HudSidePad || 12);
  // Supersample factor for crisper gauges at larger sizes
  const HUD_SUPERSAMPLE = Math.max(1, Math.min(3, Number(PLUGIN_PARAMS.HudSupersample || (HUD_SCALE >= 1.2 ? 2 : 1))));
  const PERF_PARAM_DECIMATE = Number(PLUGIN_PARAMS.PerfDecimateFactor || 2); // redraw/effect step every N frames when low FPS
  const PERF_PARAM_LOW_FPS = Number(PLUGIN_PARAMS.PerfLowFps || 50); // FPS threshold for decimation
  // HUD layout tuning
  const HUD_DIAL_GAP_X = Number(PLUGIN_PARAMS.HudDialGapX || 16);
  const HUD_DIAL_GAP_Y = Number(PLUGIN_PARAMS.HudDialGapY || 4);
  const HUD_BOOST_OFFSET_Y = Number(PLUGIN_PARAMS.HudBoostOffsetY || 4);
  const HUD_ROUND_CAPS = String(PLUGIN_PARAMS.HudRoundCaps || 'true') === 'true';
  const HUD_HEADING_LABEL = String(PLUGIN_PARAMS.HudHeadingLabel || 'true') === 'true';
  const HUD_HEADING_GRANULARITY = Number(PLUGIN_PARAMS.HudHeadingGranularity || 8); // 8 or 16
  const HUD_AUTO_FADE = String(PLUGIN_PARAMS.HudAutoFade || 'true') === 'true';
  const HUD_FADE_IN_MS = Number(PLUGIN_PARAMS.HudFadeInMs || 150);
  const HUD_FADE_OUT_MS = Number(PLUGIN_PARAMS.HudFadeOutMs || 400);
  const HUD_IDLE_SPEED = Number(PLUGIN_PARAMS.HudIdleSpeed || 0.1); // tiles/s threshold
  const HUD_ALWAYS_VISIBLE_DURING_BOOST = String(PLUGIN_PARAMS.HudAlwaysVisibleDuringBoost || 'true') === 'true';
  // Boost system params
  const BOOST_ENABLED = true;
  const BOOST_MULTIPLIER = Number(PLUGIN_PARAMS.BoostMultiplier || 1.6);
  const BOOST_HOLD_ACTION = String(PLUGIN_PARAMS.BoostHoldAction || 'pageup');
  const BOOST_FILL_RATE = Number(PLUGIN_PARAMS.BoostFillRate || 0.08);        // per second while moving
  const BOOST_STOP_DRAIN_RATE = Number(PLUGIN_PARAMS.BoostStopDrainRate || 0.05); // per second while stopped
  const BOOST_ACTIVE_DRAIN_RATE = Number(PLUGIN_PARAMS.BoostActiveDrainRate || 0.2); // per second during active boost (reduced for longer boost)
  const HUD_BOOST_DIAM = Number(PLUGIN_PARAMS.HudBoostDiam || 36);
  const HUD_BOOST_DIRECTION = String(PLUGIN_PARAMS.HudBoostDirection || 'cw'); // 'cw' or 'ccw'
  const HUD_BOOST_COLOR_START = String(PLUGIN_PARAMS.HudBoostColorStart || '#ff4040');
  const HUD_BOOST_COLOR_MID = String(PLUGIN_PARAMS.HudBoostColorMid || '#ffff40');
  const HUD_BOOST_COLOR_END = String(PLUGIN_PARAMS.HudBoostColorEnd || '#40ff40');
  const HUD_BOOST_SHOW_PERCENT = String(PLUGIN_PARAMS.HudBoostShowPercent || 'false') === 'true';
  const HUD_BOOST_PROGRESS_RATIO = Number(PLUGIN_PARAMS.HudBoostProgressRatio || 0.22); // thickness as fraction of radius (thinner default)
  const BOOST_READY_SE = String(PLUGIN_PARAMS.BoostReadySe || '');
  const BOOST_READY_SE_VOL = Number(PLUGIN_PARAMS.BoostReadySeVol || 60);
  const BOOST_ACTIVE_SE = String(PLUGIN_PARAMS.BoostActiveSe || '');
  const BOOST_ACTIVE_SE_VOL = Number(PLUGIN_PARAMS.BoostActiveSeVol || 60);
  // Boost camera zoom params (extra zoom while boosting)
  const BOOST_ZOOM_FOV_DELTA = Number(PLUGIN_PARAMS.BoostZoomFovDelta || 2.5); // degrees added to base FOV during boost (zoom out)
  const BOOST_ZOOM_DIST_MULT = Number(PLUGIN_PARAMS.BoostZoomDistMult || 1.03); // camera distance multiplier during boost (zoom out)
  const BOOST_ZOOM_SMOOTH_FRAMES = Number(PLUGIN_PARAMS.BoostZoomSmoothFrames || 10);

  // Precompute boost color RGB and helpers
  function hexToRgbFast(hex) {
    const s = hex.replace('#','');
    const full = s.length === 3 ? s.split('').map(c => c + c).join('') : s;
    const n = parseInt(full, 16);
    return { r: (n >> 16) & 255, g: (n >> 8) & 255, b: n & 255 };
  }
  function rgbaString(r,g,b,a){ return `rgba(${Math.round(r)},${Math.round(g)},${Math.round(b)},${a})`; }
  const BOOST_RGB_START = hexToRgbFast(HUD_BOOST_COLOR_START);
  const BOOST_RGB_MID = hexToRgbFast(HUD_BOOST_COLOR_MID);
  const BOOST_RGB_END = hexToRgbFast(HUD_BOOST_COLOR_END);
  function boostCssColorForCharge(t){
    const tt = Math.max(0, Math.min(1, t));
    let rMix, gMix, bMix;
    if (tt <= 0.5) { const k = tt*2; rMix = BOOST_RGB_START.r + (BOOST_RGB_MID.r-BOOST_RGB_START.r)*k; gMix = BOOST_RGB_START.g + (BOOST_RGB_MID.g-BOOST_RGB_START.g)*k; bMix = BOOST_RGB_START.b + (BOOST_RGB_MID.b-BOOST_RGB_START.b)*k; }
    else { const k = (tt-0.5)*2; rMix = BOOST_RGB_MID.r + (BOOST_RGB_END.r-BOOST_RGB_MID.r)*k; gMix = BOOST_RGB_MID.g + (BOOST_RGB_END.g-BOOST_RGB_MID.g)*k; bMix = BOOST_RGB_MID.b + (BOOST_RGB_END.b-BOOST_RGB_MID.b)*k; }
    return rgbaString(rMix,gMix,bMix,1);
  }

  // Convert RGB components to PIXI hex color number
  function rgbToHexNum(r, g, b) {
    const rr = Math.max(0, Math.min(255, Math.round(r))) & 0xff;
    const gg = Math.max(0, Math.min(255, Math.round(g))) & 0xff;
    const bb = Math.max(0, Math.min(255, Math.round(b))) & 0xff;
    return (rr << 16) | (gg << 8) | bb;
  }

  // Interpolate the boost gauge gradient and return a hex color number
  function boostColorHexForCharge(t) {
    const tt = Math.max(0, Math.min(1, t));
    let rMix, gMix, bMix;
    if (tt <= 0.5) {
      const k = tt * 2;
      rMix = BOOST_RGB_START.r + (BOOST_RGB_MID.r - BOOST_RGB_START.r) * k;
      gMix = BOOST_RGB_START.g + (BOOST_RGB_MID.g - BOOST_RGB_START.g) * k;
      bMix = BOOST_RGB_START.b + (BOOST_RGB_MID.b - BOOST_RGB_START.b) * k;
    } else {
      const k = (tt - 0.5) * 2;
      rMix = BOOST_RGB_MID.r + (BOOST_RGB_END.r - BOOST_RGB_MID.r) * k;
      gMix = BOOST_RGB_MID.g + (BOOST_RGB_END.g - BOOST_RGB_MID.g) * k;
      bMix = BOOST_RGB_MID.b + (BOOST_RGB_END.b - BOOST_RGB_MID.b) * k;
    }
    return rgbToHexNum(rMix, gMix, bMix);
  }

  // Current gauge color based on charge level (0..1)
  function getCurrentBoostColorHex() {
    try {
      return boostColorHexForCharge(typeof boostCharge01 === 'number' ? boostCharge01 : 0);
    } catch (_) {
      return rgbToHexNum(255, 255, 255);
    }
  }

  // Fast sine lookup to reduce Math.sin in hot paths
  const SIN_LUT_SIZE = 256;
  const SIN_LUT = new Float32Array(SIN_LUT_SIZE);
  const TWO_PI = Math.PI * 2;
  for (let i = 0; i < SIN_LUT_SIZE; i++) {
    SIN_LUT[i] = Math.sin((i / SIN_LUT_SIZE) * TWO_PI);
  }
  function fastSin(rad) {
    let t = rad % TWO_PI; if (t < 0) t += TWO_PI;
    const idx = (t / TWO_PI) * SIN_LUT_SIZE;
    const i = idx | 0; const f = idx - i;
    const a = SIN_LUT[i & (SIN_LUT_SIZE - 1)];
    const b = SIN_LUT[(i + 1) & (SIN_LUT_SIZE - 1)];
    return a + (b - a) * f;
  }
  
  const defaultCameraY = AIRSHIP_CONFIG.CAMERA.DEFAULT_Y;
  const targetCameraY = AIRSHIP_CONFIG.CAMERA.TARGET_Y;
  const targetPitch = AIRSHIP_CONFIG.CAMERA.TARGET_PITCH;
  // Safer default pitch: fall back to UltraMode7.DEFAULT_PITCH or 60 if parameter missing
  const defaultPitchSafe = Number.isFinite(AIRSHIP_CONFIG.CAMERA.DEFAULT_PITCH)
    ? AIRSHIP_CONFIG.CAMERA.DEFAULT_PITCH
    : (typeof UltraMode7.DEFAULT_PITCH === 'number' ? UltraMode7.DEFAULT_PITCH : 60);
  
  // Dynamic camera configuration
  const CAMERA_CONFIG = {
    // Pitch adjustment based on altitude
    PITCH: {
      MIN: 35, // Pitch when at minimum altitude
      MAX: 80  // Pitch when at maximum altitude
    },
    
    // FOV adjustment based on altitude
    FOV: {
      MIN: 45, // Narrow FOV when low (more zoomed in)
      MAX: 65  // Wide FOV when high (more zoomed out)
    },
    
    // Camera distance adjustment based on altitude
    DISTANCE: {
      MIN: 600, // Further camera when low (see more of world)
      MAX: 350  // Closer camera when high (more focused view)
    },
    
    // Far clip distance adjustment based on altitude
    FAR_CLIP: {
      MIN: 2000, // Much longer view distance when low
      MAX: 3000  // Maximum view distance when high
    },
    
      // Banking effect
  BANKING: {
    MAX_ROLL: 2 // Maximum 2 degrees of roll
  },
  
      // Airship sprite banking/tilting
    AIRSHIP_BANKING: {
      ENABLED: true,
      MAX_TILT: 25, // Maximum tilt in degrees
      TILT_SPEED: 0.1, // How fast the airship tilts
      RECOVERY_SPEED: 0.05, // How fast it returns to level
      // Tilt based on movement direction
      DIRECTION_TILT: {
        ENABLED: true,
        FORWARD_TILT: 5, // Tilt forward when moving up
        BACKWARD_TILT: -3, // Tilt backward when moving down
        SIDE_TILT: 15 // Tilt sideways when turning
      },
      
      // Advanced banking effects
      ADVANCED: {
        ENABLED: true,
        // G-force simulation
        G_FORCE: {
          ENABLED: true,
          MAX_G_FORCE: 3.0,
          COMPRESSION_FACTOR: 0.8
        },
        
        // Aerodynamic effects
        AERODYNAMIC: {
          ENABLED: true,
          WIND_RESISTANCE: 0.1,
          TURBULENCE: 0.05
        },
        
        // Dramatic banking for sharp turns
        DRAMATIC_TURNS: {
          ENABLED: true,
          SHARP_TURN_THRESHOLD: 45, // Degrees
          DRAMATIC_TILT_MULTIPLIER: 2.0
        },
        
              // Directional drift toward screen edges
      DIRECTIONAL_DRIFT: {
        ENABLED: true,
        DRIFT_SPEED: 0.02, // How fast it drifts toward edges
        MAX_DRIFT_DISTANCE: 100, // Maximum pixels from center
        RECOVERY_SPEED: 0.01, // How fast it returns to center
        SCREEN_EDGE_THRESHOLD: 0.3, // When to start drifting (30% from center)
        MOMENTUM_DRIFT: {
          ENABLED: true,
          MOMENTUM_DECAY: 0.95, // How quickly momentum fades
          MOMENTUM_MULTIPLIER: 1.5 // How much momentum affects drift
        }
      }
      }
    },
    
    // NEW: Advanced camera effects
    ADVANCED_EFFECTS: {
      // Cinematic camera shake based on speed/altitude
      SHAKE: {
        ENABLED: true,
        INTENSITY_MULTIPLIER: 0.3,
        FREQUENCY: 0.1
      },
      
      // Dynamic depth of field (blur background at high altitude)
      DEPTH_OF_FIELD: {
        ENABLED: true,
        MAX_BLUR: 0.8,
        FOCUS_DISTANCE: 1000
      },
      
      // Camera zoom effects
      ZOOM: {
        ENABLED: true,
        MIN_ZOOM: 0.6, // More extreme zoom out (was 0.8)
        MAX_ZOOM: 2.5, // Much more extreme zoom in (was 1.2)
        ZOOM_SPEED: 0.03, // Slightly faster zoom transitions (was 0.02)
        // NEW: Extreme altitude zoom settings
        EXTREME_ALTITUDE: {
          ENABLED: true,
          TRIGGER_ALTITUDE: 0.8, // Trigger at 80% of max altitude
          MAX_ZOOM: 4.0, // Extreme zoom at very high altitude
          ZOOM_CURVE: 2.5 // Exponential curve for more dramatic effect
        }
      },
      
      // Motion blur for high-speed movement
      MOTION_BLUR: {
        ENABLED: true,
        INTENSITY: 0.4,
        THRESHOLD_SPEED: 5.0
      },
      
      // Cinematic letterboxing at dramatic moments
      LETTERBOX: {
        ENABLED: true,
        HEIGHT: 0.1, // 10% of screen height
        TRANSITION_SPEED: 0.05
      },
      
      // Cinematic camera movements
      CINEMATIC: {
        ENABLED: true,
        ORBIT_SPEED: 0.02,
        ORBIT_RADIUS: 50,
        DRAMATIC_ANGLE_CHANGES: true,
        SLOW_MOTION_THRESHOLD: 0.3
      },
      
      // Dolly zoom effect (Hitchcock effect)
      DOLLY_ZOOM: {
        ENABLED: true,
        TRIGGER_ALTITUDE: 160, // Lower trigger altitude for earlier effect (was 180)
        ZOOM_SPEED: 0.04, // Faster dolly zoom (was 0.03)
        MAX_ZOOM: 3.5, // Much more extreme dolly zoom (was 2.0)
        // NEW: Extreme dolly zoom at maximum altitude
        EXTREME_DOLLY: {
          ENABLED: true,
          TRIGGER_ALTITUDE: 0.95, // Trigger at 95% of max altitude
          MAX_ZOOM: 5.0, // Extreme dolly zoom at maximum altitude
          ZOOM_CURVE: 3.0 // Very dramatic exponential curve
        }
      },
      
      // Advanced cinematic effects
      ADVANCED_CINEMATIC: {
        ENABLED: true,
        // Dutch angle effect (tilted camera)
        DUTCH_ANGLE: {
          ENABLED: true,
          MAX_TILT: 25, // Maximum tilt in degrees
          TRIGGER_SPEED: 2.0
        },
        
        // Camera whip pan (quick direction changes)
        WHIP_PAN: {
          ENABLED: true,
          THRESHOLD_ANGLE_CHANGE: 45, // Degrees
          PAN_SPEED: 0.15
        },
        
        // Breathing camera (subtle movement)
        BREATHING: {
          ENABLED: true,
          AMPLITUDE: 2.0,
          FREQUENCY: 0.05
        },
        
        // Dramatic slow motion
        SLOW_MOTION: {
          ENABLED: true,
          TRIGGER_SPEED: 4.0,
          SLOW_FACTOR: 0.3
        },
        
        // Camera roll effects
        ROLL: {
          ENABLED: true,
          MAX_ROLL: 15,
          ROLL_SPEED: 0.02
        },
        
        // Impossible camera effects
        IMPOSSIBLE: {
          ENABLED: true,
          // Matrix-style bullet time
          BULLET_TIME: {
            ENABLED: true,
            TRIGGER_SPEED: 5.0,
            TIME_SCALE: 0.1
          },
          
          // Camera teleportation
          TELEPORT: {
            ENABLED: true,
            TRIGGER_ALTITUDE: 190,
            TELEPORT_DISTANCE: 100
          },
          
                  // Gravity-defying camera
        GRAVITY_DEFY: {
          ENABLED: true,
          INVERT_Y: true
        },
        
        // Camera personality system (removed)
        PERSONALITY: { ENABLED: false }
        }
      }
    }
  };
  
  // Removed unused lastYawDirection and parallax rotation controls (no-op)
  
  let wasFlying = false;
  let pitchSetActive = false; // true during takeoff camera transition
  let pitchSetFrames = 0;     // frames remaining for takeoff transition
  let pitchSetInit = false;   // ensures we only enqueue the 60f animation once
  let pitchResetActive = false;
  
      // Airship banking/tilting system
    const AirshipBanking = {
      currentTilt: { x: 0, y: 0, z: 0 }, // X=roll, Y=pitch, Z=yaw
      targetTilt: { x: 0, y: 0, z: 0 },
      lastDirection: 0,
      lastAltitude: 0,
      
      // Advanced banking variables
      gForceEffect: 0,
      turbulenceOffset: { x: 0, y: 0 },
      dramaticTurnActive: false,
      
      // Directional drift variables
      driftOffset: { x: 0, y: 0 },
      targetDrift: { x: 0, y: 0 },
      screenCenter: { x: 0, y: 0 },
      momentumDrift: { x: 0, y: 0 },
      lastDirection: 0,
    
    // Initialize banking
    init: function() {
      this.currentTilt = { x: 0, y: 0, z: 0 };
      this.targetTilt = { x: 0, y: 0, z: 0 };
      this.lastDirection = 0;
      this.lastAltitude = 0;
      this.gForceEffect = 0;
      this.turbulenceOffset = { x: 0, y: 0 };
      this.dramaticTurnActive = false;
      this.driftOffset = { x: 0, y: 0 };
      this.targetDrift = { x: 0, y: 0 };
      this.screenCenter = { x: Graphics.width / 2, y: Graphics.height / 2 };
      this.momentumDrift = { x: 0, y: 0 };
      this.lastDirection = 0;
    },
    
    // Update airship tilt based on movement
    updateTilt: function(airship) {
      if (!CAMERA_CONFIG.AIRSHIP_BANKING.ENABLED) return;
      
      // Derive kinematics from screen position deltas
      const sx = airship.screenX();
      const sy = airship.screenY();
      const dx = sx - kinLastScreenX;
      const dy = sy - kinLastScreenY;
      const speed = Math.hypot(dx, dy);
      const direction = Math.atan2(dy, dx);
      const altitude = airship._altitude || 0;
      
      // Calculate direction change for banking
      const directionChange = direction - this.lastDirection;
      const normalizedDirectionChange = Math.atan2(Math.sin(directionChange), Math.cos(directionChange));
      
      // Set target tilt based on movement
      this.targetTilt.x = 0; // Roll (sideways tilt)
      this.targetTilt.y = 0; // Pitch (forward/backward tilt)
      this.targetTilt.z = 0; // Yaw (rotation)
      
      // Banking based on direction changes
      if (Math.abs(normalizedDirectionChange) > 0.1) {
        this.targetTilt.x = normalizedDirectionChange * CAMERA_CONFIG.AIRSHIP_BANKING.DIRECTION_TILT.SIDE_TILT;
      }
      
      // Forward/backward tilt based on vertical movement
      if (dy < -0.5) {
        // Moving up - tilt forward
        this.targetTilt.y = CAMERA_CONFIG.AIRSHIP_BANKING.DIRECTION_TILT.FORWARD_TILT;
      } else if (dy > 0.5) {
        // Moving down - tilt backward
        this.targetTilt.y = CAMERA_CONFIG.AIRSHIP_BANKING.DIRECTION_TILT.BACKWARD_TILT;
      }
      
      // Altitude-based tilt (higher altitude = more dramatic)
      const altitudeFactor = Math.min(altitude / AIRSHIP_CONFIG.ALTITUDE.MAX, 1.0);
      this.targetTilt.x *= (1 + altitudeFactor * 0.5);
      this.targetTilt.y *= (1 + altitudeFactor * 0.3);
      
      // Advanced banking effects
      if (CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.ENABLED) {
        this.updateAdvancedBanking(airship, speed, direction, altitude);
      }
      
      // Clamp to maximum tilt
      const maxTilt = CAMERA_CONFIG.AIRSHIP_BANKING.MAX_TILT;
      this.targetTilt.x = Math.max(-maxTilt, Math.min(maxTilt, this.targetTilt.x));
      this.targetTilt.y = Math.max(-maxTilt, Math.min(maxTilt, this.targetTilt.y));
      
      // Smoothly interpolate current tilt to target
      const tiltSpeed = CAMERA_CONFIG.AIRSHIP_BANKING.TILT_SPEED;
      const recoverySpeed = CAMERA_CONFIG.AIRSHIP_BANKING.RECOVERY_SPEED;
      
      if (speed > 0.5) {
        // Moving - apply tilt
        this.currentTilt.x += (this.targetTilt.x - this.currentTilt.x) * tiltSpeed;
        this.currentTilt.y += (this.targetTilt.y - this.currentTilt.y) * tiltSpeed;
      } else {
        // Not moving - recover to level
        this.currentTilt.x *= (1 - recoverySpeed);
        this.currentTilt.y *= (1 - recoverySpeed);
      }
      
      // Apply tilt to airship sprite
      this.applyTiltToSprite(airship);
      
      this.lastDirection = direction;
      this.lastAltitude = altitude;
    },
    
    // Update advanced banking effects
    updateAdvancedBanking: function(airship, speed, direction, altitude) {
      // G-force simulation
      if (CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.G_FORCE.ENABLED) {
        this.updateGForce(airship, speed, direction);
      }
      
      // Aerodynamic effects
      if (CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.AERODYNAMIC.ENABLED) {
        this.updateAerodynamics(speed, altitude);
      }
      
      // Dramatic turns
      if (CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DRAMATIC_TURNS.ENABLED) {
        this.updateDramaticTurns(direction);
      }
      
      // Directional drift
      if (CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DIRECTIONAL_DRIFT.ENABLED) {
        this.updateDirectionalDrift(airship, speed, direction);
      }
    },
    
    // Update G-force effects
    updateGForce: function(airship, speed, direction) {
      const maxGForce = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.G_FORCE.MAX_G_FORCE;
      const compressionFactor = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.G_FORCE.COMPRESSION_FACTOR;
      
      // Calculate G-force based on speed and direction changes
      this.gForceEffect = Math.min(maxGForce, speed * 0.5);
      
      // Apply compression effect (squash the sprite)
      if (this.gForceEffect > 0.5) {
        const compression = 1.0 - (this.gForceEffect * compressionFactor);
        if (airship && airship.sprite) {
          airship.sprite.scale.y = compression;
        }
      }
    },
    
    // Update aerodynamic effects
    updateAerodynamics: function(speed, altitude) {
      const windResistance = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.AERODYNAMIC.WIND_RESISTANCE;
      const turbulence = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.AERODYNAMIC.TURBULENCE;
      
      // Wind resistance effect
      if (speed > 2.0) {
        const resistance = speed * windResistance;
        const t = (Graphics.frameCount||0);
        this.targetTilt.x += Math.sin(t * 0.01) * resistance;
      }
      
      // Turbulence at high altitude
      if (altitude > AIRSHIP_CONFIG.ALTITUDE.MAX * 0.7) {
        const t2 = (Graphics.frameCount||0);
        this.turbulenceOffset.x = Math.sin(t2 * 0.02) * turbulence;
        this.turbulenceOffset.y = Math.cos(t2 * 0.015) * turbulence;
      } else {
        this.turbulenceOffset.x *= 0.95;
        this.turbulenceOffset.y *= 0.95;
      }
    },
    
    // Update dramatic turns
    updateDramaticTurns: function(direction) {
      const threshold = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DRAMATIC_TURNS.SHARP_TURN_THRESHOLD * (Math.PI / 180);
      const multiplier = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DRAMATIC_TURNS.DRAMATIC_TILT_MULTIPLIER;
      
      const directionChange = Math.abs(direction - this.lastDirection);
      
      if (directionChange > threshold) {
        this.dramaticTurnActive = true;
        this.targetTilt.x *= multiplier;
        this.targetTilt.y *= multiplier;
      } else {
        this.dramaticTurnActive = false;
      }
    },
    
    // Update directional drift toward screen edges
    updateDirectionalDrift: function(airship, speed, direction) {
      const driftConfig = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DIRECTIONAL_DRIFT;
      const driftSpeed = driftConfig.DRIFT_SPEED;
      const maxDriftDistance = driftConfig.MAX_DRIFT_DISTANCE;
      const recoverySpeed = driftConfig.RECOVERY_SPEED;
      const edgeThreshold = driftConfig.SCREEN_EDGE_THRESHOLD;
      
      // Calculate current position relative to screen center
      const currentX = airship.x;
      const currentY = airship.y;
      const screenWidth = Graphics.width;
      const screenHeight = Graphics.height;
      
      // Calculate how far from center the airship is (0 = center, 1 = edge)
      const distanceFromCenterX = Math.abs(currentX - this.screenCenter.x) / (screenWidth / 2);
      const distanceFromCenterY = Math.abs(currentY - this.screenCenter.y) / (screenHeight / 2);
      
      // Determine target drift based on movement direction
      this.targetDrift.x = 0;
      this.targetDrift.y = 0;
      
      if (speed > 0.5) { // Only drift when moving
              // Calculate direction components
      const dirX = Math.cos(direction);
      const dirY = Math.sin(direction);
      
      // Update momentum based on direction changes
      if (CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DIRECTIONAL_DRIFT.MOMENTUM_DRIFT.ENABLED) {
        const directionChange = Math.abs(direction - this.lastDirection);
        if (directionChange > 0.1) { // Significant direction change
          // Add momentum in the old direction
          const oldDirX = Math.cos(this.lastDirection);
          const oldDirY = Math.sin(this.lastDirection);
          const momentumMultiplier = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DIRECTIONAL_DRIFT.MOMENTUM_DRIFT.MOMENTUM_MULTIPLIER;
          
          this.momentumDrift.x += oldDirX * maxDriftDistance * momentumMultiplier;
          this.momentumDrift.y += oldDirY * maxDriftDistance * momentumMultiplier;
        }
        
        // Decay momentum
        const momentumDecay = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DIRECTIONAL_DRIFT.MOMENTUM_DRIFT.MOMENTUM_DECAY;
        this.momentumDrift.x *= momentumDecay;
        this.momentumDrift.y *= momentumDecay;
      }
      
      // Determine which screen edge to drift toward
      if (dirX > 0.3) { // Moving right
        this.targetDrift.x = maxDriftDistance;
      } else if (dirX < -0.3) { // Moving left
        this.targetDrift.x = -maxDriftDistance;
      }
      
      if (dirY > 0.3) { // Moving down
        this.targetDrift.y = maxDriftDistance;
      } else if (dirY < -0.3) { // Moving up
        this.targetDrift.y = -maxDriftDistance;
      }
        
        // Apply speed multiplier for more dramatic effect at higher speeds
        const speedMultiplier = Math.min(speed / 3.0, 2.0);
        this.targetDrift.x *= speedMultiplier;
        this.targetDrift.y *= speedMultiplier;
      }
      
      // Smoothly interpolate current drift to target
      if (speed > 0.5) {
        // Moving - drift toward target (including momentum)
        const totalTargetX = this.targetDrift.x + this.momentumDrift.x;
        const totalTargetY = this.targetDrift.y + this.momentumDrift.y;
        
        this.driftOffset.x += (totalTargetX - this.driftOffset.x) * driftSpeed;
        this.driftOffset.y += (totalTargetY - this.driftOffset.y) * driftSpeed;
      } else {
        // Not moving - return to center
        this.driftOffset.x *= (1 - recoverySpeed);
        this.driftOffset.y *= (1 - recoverySpeed);
      }
      
      // Clamp drift to maximum distance
      this.driftOffset.x = Math.max(-maxDriftDistance, Math.min(maxDriftDistance, this.driftOffset.x));
      this.driftOffset.y = Math.max(-maxDriftDistance, Math.min(maxDriftDistance, this.driftOffset.y));
      
      // Update last direction for momentum calculations
      this.lastDirection = direction;
    },
    
    // Apply tilt to the airship sprite
    applyTiltToSprite: function(airship) {
      if (!airship || !airship.sprite) return;
      
      const sprite = airship.sprite;
      
      // Cache the sprite's neutral position once to avoid cumulative drift
      if (!sprite._mauiBasePos) {
        sprite._mauiBasePos = { x: sprite.x, y: sprite.y };
      } else {
        // Refresh base each frame to track engine-driven repositioning
        const bx = airship.screenX();
        const by = airship.screenY();
        // Guard against wildly invalid values when near map loop seams
        if (!isNaN(bx) && !isNaN(by) && bx > -200 && bx < Graphics.width + 200 && by > -200 && by < Graphics.height + 200) {
          sprite._mauiBasePos.x = bx;
          sprite._mauiBasePos.y = by;
        }
      }
      
      // Convert degrees to radians
      const rollRad = this.currentTilt.x * (Math.PI / 180);
      const pitchRad = this.currentTilt.y * (Math.PI / 180);
      
      // Rotation (roll)
      sprite.rotation = rollRad;
      
      // Pitch via scale
      const pitchScale = 1.0 + Math.sin(pitchRad) * 0.1;
      sprite.scale.y = pitchScale;
      
      // Compute absolute offsets for this frame (no accumulation)
      let offsetX = 0;
      let offsetY = Math.sin(pitchRad) * 5;
      
      if (CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.ENABLED) {
        // Add turbulence and drift as absolute frame offsets
        offsetX += this.turbulenceOffset.x + this.driftOffset.x;
        offsetY += this.turbulenceOffset.y + this.driftOffset.y;
        
        // G-force compression
        if (this.gForceEffect > 0.5 && !this.dramaticTurnActive) {
          const compression = 1.0 - (this.gForceEffect * 0.1);
          sprite.scale.y *= compression;
        }
        
        // Dramatic turn styling
        if (this.dramaticTurnActive) {
          sprite.scale.x = 1.2;
          sprite.alpha = 0.9;
        } else {
          sprite.scale.x = 1.0;
          sprite.alpha = 1.0;
        }
      }
      
      // Apply absolute position based on neutral base + offsets
      sprite.x = sprite._mauiBasePos.x + offsetX;
      sprite.y = sprite._mauiBasePos.y + offsetY;
    },
    
    // Reset all tilts
    reset: function() {
      this.init();
    }
  };
  
  // NEW: Advanced camera effects system
  const AdvancedCameraEffects = {
    // Camera shake variables
    shakeOffset: { x: 0, y: 0 },
    shakeTimer: 0,
    shakeIntensity: 0,
    
    // Depth of field variables
    dofBlur: 0,
    dofTarget: 0,
    
    // Zoom variables removed
    
    // Motion blur variables
    motionBlurIntensity: 0,
    lastPosition: { x: 0, y: 0 },
    
    // Letterbox variables
    letterboxHeight: 0,
    letterboxTarget: 0,
    
    // Cinematic camera variables
    orbitAngle: 0,
    orbitRadius: 0,
    dramaticAngle: 0,
    slowMotionFactor: 1.0,
    
    // Dolly zoom variables
    dollyZoomActive: false,
    dollyZoomIntensity: 0,
    
    // Advanced cinematic variables
    dutchAngle: 0,
    whipPanActive: false,
    whipPanAngle: 0,
    breathingOffset: { x: 0, y: 0 },
    breathingTimer: 0,
    slowMotionActive: false,
    cameraRoll: 0,
    lastDirection: 0,
    
    // Impossible camera variables
    bulletTimeActive: false,
    teleportActive: false,
    teleportTimer: 0,
    gravityDefyActive: false,
    
    // Initialize effects
    init: function() {
      this.shakeOffset = { x: 0, y: 0 };
      this.shakeTimer = 0;
      this.shakeIntensity = 0;
      this.dofBlur = 0;
      this.dofTarget = 0;
      // Zoom tracking removed
      this.motionBlurIntensity = 0;
      this.lastPosition = { x: 0, y: 0 };
      this.letterboxHeight = 0;
      this.letterboxTarget = 0;
      this.orbitAngle = 0;
      this.orbitRadius = 0;
      this.dramaticAngle = 0;
      this.slowMotionFactor = 1.0;
      this.dollyZoomActive = false;
      this.dollyZoomIntensity = 0;
      this.dutchAngle = 0;
      this.whipPanActive = false;
      this.whipPanAngle = 0;
      this.breathingOffset = { x: 0, y: 0 };
      this.breathingTimer = 0;
      this.slowMotionActive = false;
      this.cameraRoll = 0;
      this.lastDirection = 0;
      this.bulletTimeActive = false;
      this.teleportActive = false;
      this.teleportTimer = 0;
      this.gravityDefyActive = false;
    },
    
    // Update camera shake based on airship movement
    updateShake: function(airship, altitude) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.SHAKE.ENABLED) return;
      
      const sx1 = airship.screenX();
      const sy1 = airship.screenY();
      const dx1 = sx1 - kinLastScreenX;
      const dy1 = sy1 - kinLastScreenY;
      const speed = Math.hypot(dx1, dy1);
      const altitudeFactor = Math.min(altitude / AIRSHIP_CONFIG.ALTITUDE.MAX, 1.0);
      
      // Calculate shake intensity based on speed and altitude
      this.shakeIntensity = speed * altitudeFactor * CAMERA_CONFIG.ADVANCED_EFFECTS.SHAKE.INTENSITY_MULTIPLIER;
      
      if (this.shakeIntensity > 0.1) {
        this.shakeTimer += CAMERA_CONFIG.ADVANCED_EFFECTS.SHAKE.FREQUENCY;
        this.shakeOffset.x = Math.sin(this.shakeTimer) * this.shakeIntensity;
        this.shakeOffset.y = Math.cos(this.shakeTimer * 1.3) * this.shakeIntensity;
      } else {
        this.shakeOffset.x *= 0.9;
        this.shakeOffset.y *= 0.9;
      }
    },
    
    // Depth of field not implemented (removed)
    updateDepthOfField: function() { return; },
    
    // Zoom not implemented (removed)
    updateZoom: function() { return; },
    
    // Motion blur not implemented (removed)
    updateMotionBlur: function() { return; },
    
    // Letterbox not implemented (removed)
    updateLetterbox: function() { return; },
    
    // Update cinematic camera movements
    updateCinematic: function(airship, altitude) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.CINEMATIC.ENABLED) return;
      
      const sx4 = airship.screenX();
      const sy4 = airship.screenY();
      const dx4 = sx4 - kinLastScreenX;
      const dy4 = sy4 - kinLastScreenY;
      const speed = Math.hypot(dx4, dy4);
      const altitudeRatio = altitude / AIRSHIP_CONFIG.ALTITUDE.MAX;
      
      // Orbital camera movement around airship
      this.orbitAngle += CAMERA_CONFIG.ADVANCED_EFFECTS.CINEMATIC.ORBIT_SPEED;
      this.orbitRadius = CAMERA_CONFIG.ADVANCED_EFFECTS.CINEMATIC.ORBIT_RADIUS * (1 + altitudeRatio * 0.5);
      
      // Dramatic angle changes during high-speed moments
      if (CAMERA_CONFIG.ADVANCED_EFFECTS.CINEMATIC.DRAMATIC_ANGLE_CHANGES) {
        if (speed > 3.0) {
          this.dramaticAngle = Math.sin(this.orbitAngle * 2) * 15; // 15-degree dramatic tilt
        } else {
          this.dramaticAngle *= 0.95; // Smoothly return to normal
        }
      }
      
      // Slow motion not implemented (removed)
        this.slowMotionFactor = 1.0;
    },
    
    // Dolly zoom not implemented (removed)
    updateDollyZoom: function() { return; },
    
    // Update Dutch angle effect (tilted camera)
    updateDutchAngle: function(airship) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.DUTCH_ANGLE.ENABLED) return;
      
      const speed = Math.sqrt(airship._velocityX ** 2 + airship._velocityY ** 2);
      const triggerSpeed = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.DUTCH_ANGLE.TRIGGER_SPEED;
      const maxTilt = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.DUTCH_ANGLE.MAX_TILT;
      
      if (speed > triggerSpeed) {
        // Calculate direction-based tilt
      const sx5 = airship.screenX();
      const sy5 = airship.screenY();
      const dx5 = sx5 - kinLastScreenX;
      const dy5 = sy5 - kinLastScreenY;
      const direction = Math.atan2(dy5, dx5);
        this.dutchAngle = Math.sin(direction) * maxTilt;
      } else {
        this.dutchAngle *= 0.95; // Smooth return to normal
      }
    },
    
    // Update whip pan effect (quick direction changes)
    updateWhipPan: function(airship) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.WHIP_PAN.ENABLED) return;
      
      const sx6 = airship.screenX();
      const sy6 = airship.screenY();
      const dx6 = sx6 - kinLastScreenX;
      const dy6 = sy6 - kinLastScreenY;
      const currentDirection = Math.atan2(dy6, dx6);
      const angleChange = Math.abs(currentDirection - this.lastDirection);
      const threshold = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.WHIP_PAN.THRESHOLD_ANGLE_CHANGE * (Math.PI / 180);
      
      if (angleChange > threshold) {
        this.whipPanActive = true;
        this.whipPanAngle = currentDirection;
      } else {
        this.whipPanActive = false;
      }
      
      this.lastDirection = currentDirection;
    },
    
    // Update breathing camera effect
    updateBreathing: function() {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.BREATHING.ENABLED) return;
      
      this.breathingTimer += CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.BREATHING.FREQUENCY;
      const amplitude = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.BREATHING.AMPLITUDE;
      
      this.breathingOffset.x = Math.sin(this.breathingTimer) * amplitude;
      this.breathingOffset.y = Math.cos(this.breathingTimer * 1.3) * amplitude;
    },
    
    // Update dramatic slow motion
    updateSlowMotion: function(airship) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.SLOW_MOTION.ENABLED) return;
      
      const sx7 = airship.screenX();
      const sy7 = airship.screenY();
      const dx7 = sx7 - kinLastScreenX;
      const dy7 = sy7 - kinLastScreenY;
      const speed = Math.hypot(dx7, dy7);
      const triggerSpeed = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.SLOW_MOTION.TRIGGER_SPEED;
      const slowFactor = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.SLOW_MOTION.SLOW_FACTOR;
      
      if (speed > triggerSpeed) {
        this.slowMotionActive = true;
        this.slowMotionFactor = slowFactor;
      } else {
        this.slowMotionActive = false;
        this.slowMotionFactor = 1.0;
      }
    },
    
    // Update camera roll effects
    updateCameraRoll: function(airship) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.ROLL.ENABLED) return;
      
      const sx8 = airship.screenX();
      const sy8 = airship.screenY();
      const dx8 = sx8 - kinLastScreenX;
      const dy8 = sy8 - kinLastScreenY;
      const speed = Math.hypot(dx8, dy8);
      const maxRoll = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.ROLL.MAX_ROLL;
      const rollSpeed = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.ROLL.ROLL_SPEED;
      
      if (speed > 2.0) {
        this.cameraRoll += rollSpeed;
        if (this.cameraRoll > maxRoll) this.cameraRoll = maxRoll;
      } else {
        this.cameraRoll *= 0.95;
      }
    },
    
    // Bullet time not implemented (removed)
    updateBulletTime: function() { this.bulletTimeActive = false; },
    
    // Update camera teleportation effect
    updateTeleport: function(altitude) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.IMPOSSIBLE.TELEPORT.ENABLED) return;
      
      const triggerAltitude = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.IMPOSSIBLE.TELEPORT.TRIGGER_ALTITUDE;
      const teleportDistance = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.IMPOSSIBLE.TELEPORT.TELEPORT_DISTANCE;
      
      if (altitude >= triggerAltitude && !this.teleportActive) {
        this.teleportActive = true;
        this.teleportTimer = 30; // 30 frames of teleport effect
      }
      
      if (this.teleportActive && this.teleportTimer > 0) {
        this.teleportTimer--;
        // Create teleport effect by moving camera rapidly
        if (UltraMode7 && UltraMode7.camera) {
          const teleportOffset = Math.sin(this.teleportTimer * 0.5) * teleportDistance;
          UltraMode7.camera.x += teleportOffset;
          UltraMode7.camera.y += teleportOffset;
        }
      } else {
        this.teleportActive = false;
      }
    },
    
    // Update gravity-defying camera effect
    updateGravityDefy: function(altitude) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.IMPOSSIBLE.GRAVITY_DEFY.ENABLED) return;
      
      const altitudeRatio = altitude / AIRSHIP_CONFIG.ALTITUDE.MAX;
      
      if (altitudeRatio > 0.9) { // At very high altitude
        this.gravityDefyActive = true;
        // Invert Y movement for gravity-defying effect
        if (UltraMode7 && UltraMode7.camera) {
          UltraMode7.camera.scale.y = -1; // Flip vertically
        }
      } else {
        this.gravityDefyActive = false;
        if (UltraMode7 && UltraMode7.camera) {
          UltraMode7.camera.scale.y = 1; // Return to normal
        }
      }
    },
    
    // Apply all camera effects using absolute (non-accumulating) transforms each frame
    applyEffects: function() {
      if (!(UltraMode7 && UltraMode7.camera)) return;
      // Suppress any camera transforms during an unlandable landing attempt
      try {
        if ($gamePlayer && $gamePlayer._vehicleGettingOff && $gameMap && !$gameMap.isAirshipLandOk($gamePlayer.x, $gamePlayer.y)) {
      return;
        }
      } catch (_) {}
      const cam = UltraMode7.camera;

      // Start from neutral baseline each frame
      let baseX = 0;
      let baseY = 0;
      let baseRot = 0;
      let baseScale = 1;

      // Compose position offsets
      let offsetX = 0;
      let offsetY = 0;
      if (this.shakeOffset.x || this.shakeOffset.y) {
        offsetX += this.shakeOffset.x;
        offsetY += this.shakeOffset.y;
      }
      if (this.orbitRadius > 0) {
        const orbitX = Math.cos(this.orbitAngle) * this.orbitRadius;
        const orbitY = Math.sin(this.orbitAngle) * this.orbitRadius;
        offsetX += orbitX * 0.1;
        offsetY += orbitY * 0.1;
      }
      if (this.whipPanActive) {
        offsetX += Math.sin(this.whipPanAngle) * 20;
      }
      if (this.breathingOffset.x || this.breathingOffset.y) {
        offsetX += this.breathingOffset.x;
        offsetY += this.breathingOffset.y;
      }

      // Compose rotation
      let rotationDeg = 0;
      if (this.dramaticAngle) rotationDeg += this.dramaticAngle;
      if (this.dutchAngle) rotationDeg += this.dutchAngle;
      if (this.cameraRoll) rotationDeg += this.cameraRoll;

      // Compose zoom: prefer UM7 FOV/cameraDistance logic; avoid double scaling
      // Only use raw scale if FOV/cameraDistance aren’t being animated this frame.
      let scale = 1;
      if (this.dollyZoomActive && this.dollyZoomIntensity > 0) {
        // Compute a desired effective scale, but we won't apply it directly to avoid conflict
        // with UltraMode7 FOV/cameraDistance; leave scale at 1 here.
      } else {
        // No scale applied
      }

      // Apply composed transforms
      cam.x = baseX + offsetX;
      cam.y = baseY + offsetY;
      cam.rotation = baseRot + (rotationDeg * (Math.PI / 180));
      cam.scale.set(baseScale * scale, baseScale * scale);

      // Letterboxing would be an overlay; left as a tracked value
    },
    
    // Reset all effects
    reset: function() {
      this.init();
    }
  };
  
  // Camera calculation functions using configuration - OPTIMIZED with caching
  const CameraCalculator = {
    // Cache for altitude calculations to avoid redundant Math operations
    _cachedAltitude: null,
    _cachedAltitudeRatio: null,
    _lastAltitude: null,
    
    // Get cached altitude calculations
    _getCachedAltitude: function(altitude) {
      if (this._lastAltitude !== altitude) {
        this._lastAltitude = altitude;
        this._cachedAltitude = Math.max(AIRSHIP_CONFIG.ALTITUDE.MIN, Math.min(AIRSHIP_CONFIG.ALTITUDE.MAX, altitude));
        this._cachedAltitudeRatio = (this._cachedAltitude - AIRSHIP_CONFIG.ALTITUDE.MIN) / (AIRSHIP_CONFIG.ALTITUDE.MAX - AIRSHIP_CONFIG.ALTITUDE.MIN);
      }
      return { altitude: this._cachedAltitude, ratio: this._cachedAltitudeRatio };
    },
    
    // Calculate dynamic pitch based on altitude
    calculateDynamicPitch: function(altitude) {
      const { ratio } = this._getCachedAltitude(altitude);
      return CAMERA_CONFIG.PITCH.MIN + (ratio * (CAMERA_CONFIG.PITCH.MAX - CAMERA_CONFIG.PITCH.MIN));
    },
    
    // Calculate dynamic FOV based on altitude
    calculateDynamicFov: function(altitude) {
      const { ratio } = this._getCachedAltitude(altitude);
      return CAMERA_CONFIG.FOV.MIN + (ratio * (CAMERA_CONFIG.FOV.MAX - CAMERA_CONFIG.FOV.MIN));
    },
    
    // Calculate dynamic camera distance based on altitude
    calculateDynamicCameraDistance: function(altitude) {
      const { ratio } = this._getCachedAltitude(altitude);
      return CAMERA_CONFIG.DISTANCE.MIN + (ratio * (CAMERA_CONFIG.DISTANCE.MAX - CAMERA_CONFIG.DISTANCE.MIN));
    },
    
    // Calculate dynamic far clip distance based on altitude
    calculateDynamicFarClip: function(altitude) {
      const { ratio } = this._getCachedAltitude(altitude);
      return CAMERA_CONFIG.FAR_CLIP.MIN + (ratio * (CAMERA_CONFIG.FAR_CLIP.MAX - CAMERA_CONFIG.FAR_CLIP.MIN));
    },
    
    // Calculate minimal banking roll based on yaw direction
    calculateBankingRoll: function(currentYaw, previousYaw) {
      if (previousYaw === 0) return 0;
      
      // Calculate yaw change direction
      const yawChange = currentYaw - previousYaw;
      
      // Normalize yaw change to -180 to 180 range
      let normalizedChange = yawChange;
      if (normalizedChange > 180) normalizedChange -= 360;
      if (normalizedChange < -180) normalizedChange += 360;
      
      // Apply minimal roll based on yaw direction
      return (normalizedChange / 180) * CAMERA_CONFIG.BANKING.MAX_ROLL;
    }
  };
  
  // Effect systems using configuration - ENHANCED for more effects
  const EffectSystems = {
  // Ground trail system
    groundTrail: {
      sprites: [],
      creationTimer: 0,
      lastAirshipX: 0,
      lastAirshipY: 0,
      config: {
        lowAltitudeThreshold: AIRSHIP_CONFIG.EFFECTS.TRAIL_LOW_ALTITUDE,
        maxPoints: 25, // Increased from 12 to 25
        fadeTime: 60
      }
    },
    
    // Water ripple system
    waterRipple: {
      sprites: [],
      creationTimer: 0,
      config: {
        lowAltitudeThreshold: AIRSHIP_CONFIG.EFFECTS.RIPPLE_LOW_ALTITUDE
      }
    },
    
    // Wind lines system
    windLines: {
      sprites: [],
      creationTimer: 0,
      config: {
        speedThreshold: 0.1,
        altitudeThreshold: AIRSHIP_CONFIG.EFFECTS.WIND_LINES_ALTITUDE
      }
    },
    
    // Cloud flying system
    cloudFlying: {
      sprites: [],
      backgroundSprites: [],
      foregroundSprites: [],
      creationTimer: 0,
      config: {
        altitudeThreshold: AIRSHIP_CONFIG.EFFECTS.CLOUD_FLYING_ALTITUDE,
        speed: 3.0,
        densityMultiplier: 3.0 // Increased from 1.5 to 3.0
      }
    },
    
    // Dust cloud system
    dustCloud: {
      sprites: [],
      config: {
        threshold: AIRSHIP_CONFIG.EFFECTS.DUST_CLOUD_THRESHOLD
      }
  },
  // High-altitude contrails
  contrail: {
    sprites: [],
    creationTimer: 0,
    config: {
      altitudeThreshold: (AIRSHIP_CONFIG.EFFECTS.CLOUD_FLYING_ALTITUDE || 30) + 10,
      baseInterval: 3
    }
  },
  // Wingtip vortices
  vortices: {
    sprites: [],
    creationTimer: 0,
    config: {
      turnThresholdDegPerSec: 60,
      baseInterval: 2
      }
    }
  };
  
  // Legacy array references for backward compatibility
  let groundTrailSprites = EffectSystems.groundTrail.sprites;
  let waterRippleSprites = EffectSystems.waterRipple.sprites;
  let windLinesSprites = EffectSystems.windLines.sprites;
  let backgroundCloudSprites = EffectSystems.cloudFlying.backgroundSprites;
  let foregroundCloudSprites = EffectSystems.cloudFlying.foregroundSprites;
  let dustCloudSprites = EffectSystems.dustCloud.sprites;
  let contrailSprites = EffectSystems.contrail.sprites;
  let vortexSprites = EffectSystems.vortices.sprites;
  
  let trailCreationTimer = EffectSystems.groundTrail.creationTimer;
  let rippleCreationTimer = EffectSystems.waterRipple.creationTimer;
  let windLinesCreationTimer = EffectSystems.windLines.creationTimer;
  let cloudCreationTimer = EffectSystems.cloudFlying.creationTimer;
  let contrailCreationTimer = EffectSystems.contrail.creationTimer;
  let vortexCreationTimer = EffectSystems.vortices.creationTimer;
  let contrailRibbonL = null;
  let contrailRibbonR = null;
  let contrailGhostL = null;
  let contrailGhostR = null;
  let lastAirshipX = EffectSystems.groundTrail.lastAirshipX;
  let lastAirshipY = EffectSystems.groundTrail.lastAirshipY;
  // Kinematics tracking for camera effects (screen-space deltas per frame)
  let kinLastScreenX = 0;
  let kinLastScreenY = 0;
  
  // Performance optimization variables
  let frameCounter = 0;
  const UPDATE_FREQUENCY = AIRSHIP_CONFIG.PERFORMANCE.UPDATE_FREQUENCY;
  let isAirshipActive = false; // Track if airship effects are currently active
  // Lateral movement tracking (ignores altitude-only changes)
  let isAirshipLateralMoving = false;
  let lastRealX = null;
  let lastRealY = null;
  // Suppress any camera/yaw/roll adjustments for a few frames after an unlandable landing attempt
  let unlandableLandingSuppressFrames = 0;
  // Boost state
  let boostCharge01 = 0.0; // 0..1
  let boostActive = false;
  let boostManualMode = false; // true if boost engaged via hold action
  // Boost UX state
  let boostReadyFlashFrames = 0; // frames remaining for READY flash halo
  let boostReadyPlayed = false;   // whether ready SE played for current cycle
  let boostActivePlayed = false;  // whether active SE played for current cycle

  // Hard guard: suppress UltraMode7 pitch animations during unlandable landing attempts
  (function() {
    try {
      if (window.UltraMode7) {
        // Common predicate
        const shouldSuppress = () => {
          try {
            if (unlandableLandingSuppressFrames > 0) return true;
            if ($gamePlayer && $gamePlayer.isInAirship && $gamePlayer.isInAirship() && $gameMap && Input.isTriggered('ok') && !$gameMap.isAirshipLandOk($gamePlayer.x, $gamePlayer.y)) return true;
          } catch(_) {}
          return false;
        };

        // Pitch (animate)
        if (typeof UltraMode7.animatePitch === 'function') {
        const _um7AnimatePitch = UltraMode7.animatePitch.bind(UltraMode7);
        UltraMode7.animatePitch = function(value, frames) {
          if (shouldSuppress()) return;
          return _um7AnimatePitch(value, frames);
        };
        }

        // Pitch (set)
        if (typeof UltraMode7.setPitch === 'function') {
          const _um7SetPitch = UltraMode7.setPitch.bind(UltraMode7);
          UltraMode7.setPitch = function(value) {
            if (shouldSuppress()) return;
            return _um7SetPitch(value);
          };
        }

        // FOV
        if (typeof UltraMode7.animateFov === 'function') {
          const _um7AnimateFov = UltraMode7.animateFov.bind(UltraMode7);
          UltraMode7.animateFov = function(value, frames) {
            if (shouldSuppress()) return;
            return _um7AnimateFov(value, frames);
          };
        }

        // Camera distance
        if (typeof UltraMode7.animateCameraDistance === 'function') {
          const _um7AnimateDist = UltraMode7.animateCameraDistance.bind(UltraMode7);
          UltraMode7.animateCameraDistance = function(value, frames) {
            if (shouldSuppress()) return;
            return _um7AnimateDist(value, frames);
          };
        }

        // Far clip
        if (typeof UltraMode7.animateFarClipZ === 'function') {
          const _um7AnimateFar = UltraMode7.animateFarClipZ.bind(UltraMode7);
          UltraMode7.animateFarClipZ = function(value, frames) {
            if (shouldSuppress()) return;
            return _um7AnimateFar(value, frames);
          };
        }
      }
    } catch(_) {}
  })();
  
  // Visual quality management system
  const VisualQualityManager = {
    // Check if device can handle high-quality effects
    canHandleHighQuality: function() {
      // Simple performance check based on frame rate
      return Graphics.frameRate > 45; // If maintaining good FPS, enable high quality
    },
    
    // Get appropriate effect density based on performance
    getEffectDensity: function() {
      return this.canHandleHighQuality() ? 1.0 : 0.5;
    },
    
    // Get appropriate sprite quality based on performance
    getSpriteQuality: function() {
      return this.canHandleHighQuality() ? 'high' : 'low';
    },
    
    // Apply quality settings to sprites
    applyQualitySettings: function(sprite, quality = 'high') {
      if (quality === 'low') {
        // Reduce visual complexity for better performance
        sprite.scale.set(sprite.scale.x * 0.8, sprite.scale.y * 0.8);
        sprite.opacity = Math.min(sprite.opacity, 180);
      }
    }
  };
  
  // Sprite cleanup system
  const AIRSHIP_EFFECTS = {
    groundTrail: groundTrailSprites,
    waterRipple: waterRippleSprites,
    windLines: windLinesSprites,
    backgroundCloud: backgroundCloudSprites,
    foregroundCloud: foregroundCloudSprites,
    dustCloud: dustCloudSprites,
    contrail: contrailSprites,
    vortices: vortexSprites
  };
  
  // OPTIMIZATION: Bitmap/Object Pooling System
  const BitmapPool = {
    _pools: {},
    _maxPoolSize: 20,
    _usageStats: {}, // Track usage for leak detection
    _totalCreated: 0,
    _totalReturned: 0,
    
    // Get a bitmap from pool or create new one
    getBitmap: function(key, width, height) {
      if (!this._pools[key]) {
        this._pools[key] = [];
        this._usageStats[key] = { created: 0, returned: 0, active: 0 };
      }
      
      if (this._pools[key].length > 0) {
        const bitmap = this._pools[key].pop();
        bitmap.resize(width, height);
        bitmap.clear();
        this._usageStats[key].active++;
        this._totalCreated++;
        return bitmap;
      }
      
      // Create new bitmap
      const bitmap = new Bitmap(width, height);
      this._usageStats[key].created++;
      this._usageStats[key].active++;
      this._totalCreated++;
      return bitmap;
    },
    
    // Return bitmap to pool
    returnBitmap: function(key, bitmap) {
      if (!this._pools[key]) {
        this._pools[key] = [];
        this._usageStats[key] = { created: 0, returned: 0, active: 0 };
      }
      
      if (this._pools[key].length < this._maxPoolSize) {
        this._pools[key].push(bitmap);
        this._usageStats[key].returned++;
        this._usageStats[key].active--;
        this._totalReturned++;
      } else {
        // Pool is full, destroy the bitmap
        if (bitmap && bitmap.destroy) {
          bitmap.destroy();
        }
        this._usageStats[key].active--;
      }
    },
    
    // Clear all pools
    clear: function() {
      // Destroy all bitmaps in pools
      Object.keys(this._pools).forEach(key => {
        this._pools[key].forEach(bitmap => {
          if (bitmap && bitmap.destroy) {
            bitmap.destroy();
          }
        });
      });
      this._pools = {};
      this._usageStats = {};
      this._totalCreated = 0;
      this._totalReturned = 0;
    },
    
    // Check for memory leaks
    checkForLeaks: function() {
      const leaks = [];
      Object.keys(this._usageStats).forEach(key => {
        const stats = this._usageStats[key];
        const leakRatio = stats.active / Math.max(stats.created, 1);
        if (leakRatio > 0.8) { // If more than 80% of created bitmaps are still active
          leaks.push({
            key: key,
            created: stats.created,
            returned: stats.returned,
            active: stats.active,
            leakRatio: leakRatio
          });
        }
      });
      
      if (leaks.length > 0) {
        console.warn('🚨 BitmapPool memory leak detected:', leaks);
        return leaks;
      }
      
      return null;
    },
    
    // Get pool statistics
    getStats: function() {
      return {
        totalCreated: this._totalCreated,
        totalReturned: this._totalReturned,
        activeBitmaps: this._totalCreated - this._totalReturned,
        pools: Object.keys(this._pools).length,
        usageStats: this._usageStats
      };
    }
  };
  
  ///get off vehicle stuff

const _getOffVehicle = Game_Player.prototype.getOffVehicle;
const _getOnVehicle = Game_Player.prototype.getOnVehicle;

  // Hide airship shadow by overriding shadowOpacity
  const _Game_Vehicle_shadowOpacity = Game_Vehicle.prototype.shadowOpacity;
  Game_Vehicle.prototype.shadowOpacity = function() {
    if (this.isAirship() && $gamePlayer && $gamePlayer.isInAirship()) {
      return 0; // Hide shadow when player is in airship
    }
    return _Game_Vehicle_shadowOpacity.call(this);
  };

  // 1. Block left/right input in airship
  const _Input_isPressed = Input.isPressed;
  
  Input.isPressed = function(key) {
    if (!Input || typeof _Input_isPressed !== 'function') return false;
    const gathering = ($gamePlayer && $gamePlayer.areFollowersGathering && $gamePlayer.areFollowersGathering());
    // During boarding camera transition or follower gathering, suppress movement-related keys
    if ((typeof pitchSetActive !== 'undefined' && pitchSetActive) || gathering) {
      if (key === 'left' || key === 'right' || key === 'up' || key === 'down' || key === 'shift') {
      return false;
    }
    }
    return _Input_isPressed.call(this, key);
  };
  
  // 2. Control management system - REWRITTEN for safety
  const ControlManager = {
    // Track control state
    controlsSwapped: false,
    originalKeyMapper: null,
    originalGamepadMapper: null,
    
    // Default RPG Maker MZ input mappings (from rmmz_core.js)
    defaultKeyMapper: {
      9: "tab", // tab
      13: "ok", // enter
      16: "shift", // shift
      17: "control", // control
      18: "control", // alt
      27: "escape", // escape
      32: "ok", // space
      33: "pageup", // pageup
      34: "pagedown", // pagedown
      37: "left", // left arrow
      38: "up", // up arrow
      39: "right", // right arrow
      40: "down", // down arrow
      45: "escape", // insert
      81: "pageup", // Q
      87: "pagedown", // W
      88: "escape", // X
      90: "ok", // Z
      96: "escape", // numpad 0
      98: "down", // numpad 2
      100: "left", // numpad 4
      102: "right", // numpad 6
      104: "up", // numpad 8
      120: "debug" // F9
    },
    
    defaultGamepadMapper: {
      0: "ok", // A
      1: "cancel", // B
      2: "shift", // X
      3: "menu", // Y
      4: "pageup", // LB
      5: "pagedown", // RB
      12: "up", // D-pad up
      13: "down", // D-pad down
      14: "left", // D-pad left
      15: "right" // D-pad right
    },
    
    // Initialize the control manager
    initialize: function() {
      // Store the current mappings (which should be the defaults)
      this.originalKeyMapper = { ...Input.keyMapper };
      this.originalGamepadMapper = { ...Input.gamepadMapper };
      console.log('🎮 ControlManager initialized with default mappings');
    },
    
    // Deprecated: we do not mutate key/gamepad mappings anymore to avoid F5 issues
    swapControls: function() {
      this.controlsSwapped = false;
      return; // no-op
    },
    
    // Deprecated: nothing to restore since we do not change mappings
    restoreControls: function() {
      this.controlsSwapped = false;
      return; // no-op
    },
    
    // Deprecated: left for compatibility, but does not touch mappings anymore
    forceRestoreControls: function() {
      this.controlsSwapped = false;
      return; // no-op
    },
    
    // Check if controls need to be updated
    // NOTE: We no longer mutate Input.keyMapper at runtime.
    // Forward/altitude logic reads both Shift and Up directly and decides behavior without swapping.
    updateControls: function() {
      this.controlsSwapped = false;
      return; // no-op to avoid breaking F5 reloads
    },
    
    // Validate that controls are in a good state
    validateControls: function() {
      // Don't interfere with user's custom key bindings from VisuMZ_1_OptionsCore
      // Just check that the mappings exist and are valid
      const hasUpKey = Input.keyMapper[38] !== undefined;
      const hasShiftKey = Input.keyMapper[16] !== undefined;
      
      if (!hasUpKey || !hasShiftKey) {
        console.warn('⚠️ Missing key mappings - this should not happen');
        return false;
      }
      
      return true;
    }
  };
  
  // Initialize ControlManager when plugin loads
  ControlManager.initialize();
  
  // Expose ControlManager globally for testing
  window.ControlManager = ControlManager;
  
  // Add safety check on game start (but don't force defaults)
  const _Scene_Boot_start = Scene_Boot.prototype.start;
  Scene_Boot.prototype.start = function() {
    _Scene_Boot_start.call(this);
    
    // Just validate that mappings exist (don't force defaults)
    setTimeout(() => {
      if (ControlManager && ControlManager.validateControls) {
        ControlManager.validateControls();
      }
    }, 1000);
  };
  
  // 3. Yaw, facing, and pitch logic
  const _SceneMap_update = Scene_Map.prototype.update;
  function _maui_update_coreWrapper(scene) {
    // Error handling: ensure required objects exist
    if (!$gamePlayer || !$gameMap || !window.UltraMode7) return;
    if (typeof UltraMode7.getYaw !== 'function' || typeof UltraMode7.getPitch !== 'function') return;
    if (!$gamePlayer.isInAirship()) return;
    if (!$gameTemp) return;
    if ($gameTemp._yawFacing === undefined) $gameTemp._yawFacing = UltraMode7.getYaw();
    try {
      const current = UltraMode7.getYaw();
      const target = $gameTemp._yawFacing;
      if (Math.abs(((target - current + 540) % 360) - 180) > 0.05) UltraMode7.setYaw(target);
    } catch(_) { UltraMode7.setYaw($gameTemp._yawFacing); }
    const isSwapped = (typeof ControlManager !== 'undefined' && ControlManager && ControlManager.controlsSwapped === true);
    const climbPressed = isSwapped ? _Input_isPressed.call(Input, 'shift') : _Input_isPressed.call(Input, 'up');
    if (!pitchSetActive && unlandableLandingSuppressFrames === 0 && climbPressed) {
      const airship = $gameMap.airship(); if (!airship || typeof airship._altitude !== 'number') return;
      if (airship._altitude < AIRSHIP_CONFIG.ALTITUDE.MAX) {
        AltitudeManager.updateAltitude(airship, airship._altitude + 1);
        const currentAltitude = $gameMap.airship()._altitude;
        const dynamicPitch = CameraCalculator.calculateDynamicPitch(currentAltitude);
        const dynamicFov = CameraCalculator.calculateDynamicFov(currentAltitude);
        const dynamicCameraDistance = CameraCalculator.calculateDynamicCameraDistance(currentAltitude);
        const dynamicFarClip = CameraCalculator.calculateDynamicFarClip(currentAltitude);
        const eps = 0.05;
        if (unlandableLandingSuppressFrames === 0) {
          if (Math.abs(UltraMode7.getPitch() - dynamicPitch) > eps) UltraMode7.animatePitch(dynamicPitch, 10);
          if (typeof UltraMode7.getFov === 'function' && Math.abs(UltraMode7.getFov() - dynamicFov) > eps) UltraMode7.animateFov(dynamicFov, 10);
          if (typeof UltraMode7.getCameraDistance === 'function' && Math.abs(UltraMode7.getCameraDistance() - dynamicCameraDistance) > eps) UltraMode7.animateCameraDistance(dynamicCameraDistance, 10);
          if (typeof UltraMode7.getFarClipZ === 'function' && Math.abs(UltraMode7.getFarClipZ() - dynamicFarClip) > 1) UltraMode7.animateFarClipZ(dynamicFarClip, 10);
        }
      }
    }
    const descendPressed = isSwapped ? _Input_isPressed.call(Input, 'altitude_down') : _Input_isPressed.call(Input, 'down');
    if (!pitchSetActive && unlandableLandingSuppressFrames === 0 && descendPressed) {
      const airship = $gameMap.airship(); if (!airship || typeof airship._altitude !== 'number') return;
      if (airship._altitude > AIRSHIP_CONFIG.ALTITUDE.MIN) {
        AltitudeManager.updateAltitude(airship, airship._altitude - 1.5);
        const currentAltitude = $gameMap.airship()._altitude;
        const dynamicPitch = CameraCalculator.calculateDynamicPitch(currentAltitude);
        const dynamicFov = CameraCalculator.calculateDynamicFov(currentAltitude);
        const dynamicCameraDistance = CameraCalculator.calculateDynamicCameraDistance(currentAltitude);
        const dynamicFarClip = CameraCalculator.calculateDynamicFarClip(currentAltitude);
        const eps = 0.05;
        if (unlandableLandingSuppressFrames === 0) {
          if (Math.abs(UltraMode7.getPitch() - dynamicPitch) > eps) UltraMode7.animatePitch(dynamicPitch, 10);
          if (typeof UltraMode7.getFov === 'function' && Math.abs(UltraMode7.getFov() - dynamicFov) > eps) UltraMode7.animateFov(dynamicFov, 10);
          if (typeof UltraMode7.getCameraDistance === 'function' && Math.abs(UltraMode7.getCameraDistance() - dynamicCameraDistance) > eps) UltraMode7.animateCameraDistance(dynamicCameraDistance, 10);
          if (typeof UltraMode7.getFarClipZ === 'function' && Math.abs(UltraMode7.getFarClipZ() - dynamicFarClip) > 1) UltraMode7.animateFarClipZ(dynamicFarClip, 10);
        }
      }
    }
  }
  Scene_Map.prototype.update = function() {
	
	/// code for swapping controls while on airship	
	  _SceneMap_update.call(this);

	  // Error handling: ensure required objects exist
	  if (!$gamePlayer || !$gameMap || !window.UltraMode7) {
	    console.warn('⚠️ Required objects not available for airship controls');
	    return;
	  }

	  // SAFETY: Validate controls are in good state
	  if (ControlManager && ControlManager.validateControls) {
	    ControlManager.validateControls();
	  }

	  // Update control mappings using ControlManager
	  ControlManager.updateControls();
	
    if (!$gamePlayer || !window.UltraMode7) return;

    // Error handling: ensure UltraMode7 methods exist
    if (typeof UltraMode7.getYaw !== 'function' || typeof UltraMode7.getPitch !== 'function') {
      console.warn('⚠️ UltraMode7 methods not available');
      return;
    }

    const currentYaw = UltraMode7.getYaw();
    const currentPitch = UltraMode7.getPitch();
    // Prevent camera changes when confirm pressed over unlandable tiles (debounced)
    let suppressCameraAdjust = false;
    try {
      if ($gamePlayer && $gamePlayer.isInAirship() && $gameMap) {
        const x = $gamePlayer.x, y = $gamePlayer.y;
        if (Input.isTriggered('ok') && !$gameMap.isAirshipLandOk(x, y)) {
          unlandableLandingSuppressFrames = 6; // ~100ms at 60fps
        }
      }
      suppressCameraAdjust = unlandableLandingSuppressFrames > 0;
    } catch (_) {
      suppressCameraAdjust = false;
    }
	let yawZero = false
	

    // Yaw & facing logic
    
		
if ($gamePlayer.isInAirship()) {
  // Error handling: ensure $gameTemp exists
  if (!$gameTemp) {
    console.warn('⚠️ $gameTemp not available');
    return;
  }
  
  if ($gameTemp._yawFacing === undefined) {
    $gameTemp._yawFacing = UltraMode7.getYaw();
  }
  
  

  // Only allow yaw to be updated from $gameTemp while NOT landing
  if (!pitchResetActive) {
    // Epsilon guard: avoid redundant yaw writes
      _maui_update_coreWrapper(this);
	
   const isSwapped = (typeof ControlManager !== 'undefined' && ControlManager && ControlManager.controlsSwapped === true);
   const climbPressed = isSwapped ? _Input_isPressed.call(Input, 'shift') : _Input_isPressed.call(Input, 'up');
   if (!pitchSetActive && unlandableLandingSuppressFrames === 0 && climbPressed) {
      // Error handling: ensure airship exists and has altitude property
      const airship = $gameMap.airship();
      if (!airship || typeof airship._altitude !== 'number') {
        console.warn('⚠️ Airship or altitude not available');
        return;
      }
      
      if (airship._altitude < AIRSHIP_CONFIG.ALTITUDE.MAX) {
        AltitudeManager.updateAltitude(airship, airship._altitude + 1);
        // Update all dynamic camera parameters in real-time when altitude changes
        const currentAltitude = $gameMap.airship()._altitude;
        const dynamicPitch = CameraCalculator.calculateDynamicPitch(currentAltitude);
        const dynamicFov = CameraCalculator.calculateDynamicFov(currentAltitude);
        const dynamicCameraDistance = CameraCalculator.calculateDynamicCameraDistance(currentAltitude);
        const dynamicFarClip = CameraCalculator.calculateDynamicFarClip(currentAltitude);
        
        if (unlandableLandingSuppressFrames === 0) {
          const eps = 0.05;
          if (Math.abs(UltraMode7.getPitch() - dynamicPitch) > eps) UltraMode7.animatePitch(dynamicPitch, 10);
          if (typeof UltraMode7.getFov === 'function' && Math.abs(UltraMode7.getFov() - dynamicFov) > eps) UltraMode7.animateFov(dynamicFov, 10);
          if (typeof UltraMode7.getCameraDistance === 'function' && Math.abs(UltraMode7.getCameraDistance() - dynamicCameraDistance) > eps) UltraMode7.animateCameraDistance(dynamicCameraDistance, 10);
          if (typeof UltraMode7.getFarClipZ === 'function' && Math.abs(UltraMode7.getFarClipZ() - dynamicFarClip) > 1) UltraMode7.animateFarClipZ(dynamicFarClip, 10);
        }
      }
	}  
	
   const descendPressed = isSwapped ? _Input_isPressed.call(Input, 'altitude_down') : _Input_isPressed.call(Input, 'down');
   if (!pitchSetActive && unlandableLandingSuppressFrames === 0 && descendPressed) {
      // Error handling: ensure airship exists and has altitude property
      const airship = $gameMap.airship();
      if (!airship || typeof airship._altitude !== 'number') {
        console.warn('⚠️ Airship or altitude not available');
        return;
      }
      
      if (airship._altitude > AIRSHIP_CONFIG.ALTITUDE.MIN) {
        AltitudeManager.updateAltitude(airship, airship._altitude - 1.5);
        // Update all dynamic camera parameters in real-time when altitude changes
        const currentAltitude = $gameMap.airship()._altitude;
        const dynamicPitch = CameraCalculator.calculateDynamicPitch(currentAltitude);
        const dynamicFov = CameraCalculator.calculateDynamicFov(currentAltitude);
        const dynamicCameraDistance = CameraCalculator.calculateDynamicCameraDistance(currentAltitude);
        const dynamicFarClip = CameraCalculator.calculateDynamicFarClip(currentAltitude);
        
        if (unlandableLandingSuppressFrames === 0) {
          const eps = 0.05;
          if (Math.abs(UltraMode7.getPitch() - dynamicPitch) > eps) UltraMode7.animatePitch(dynamicPitch, 10);
          if (typeof UltraMode7.getFov === 'function' && Math.abs(UltraMode7.getFov() - dynamicFov) > eps) UltraMode7.animateFov(dynamicFov, 10);
          if (typeof UltraMode7.getCameraDistance === 'function' && Math.abs(UltraMode7.getCameraDistance() - dynamicCameraDistance) > eps) UltraMode7.animateCameraDistance(dynamicCameraDistance, 10);
          if (typeof UltraMode7.getFarClipZ === 'function' && Math.abs(UltraMode7.getFarClipZ() - dynamicFarClip) > 1) UltraMode7.animateFarClipZ(dynamicFarClip, 10);
        }
      }
	}  
	



// left right (now works with our SAN_AnalogMovement compatibility layer)
    if (!pitchSetActive && !($gamePlayer._vehicleGettingOff && !$gameMap.isAirshipLandOk($gamePlayer.x, $gamePlayer.y)) && _Input_isPressed.call(Input, 'left')) {
      $gameTemp._yawFacing = ($gameTemp._yawFacing + 2) % 360;
      // Apply banking roll when turning (always tilt)
      UltraMode7.animateRoll(-2, 15); // Tilt left when turning left
    }

    if (!pitchSetActive && !($gamePlayer._vehicleGettingOff && !$gameMap.isAirshipLandOk($gamePlayer.x, $gamePlayer.y)) && _Input_isPressed.call(Input, 'right')) {
      $gameTemp._yawFacing = ($gameTemp._yawFacing + 358) % 360;
      // Apply banking roll when turning (always tilt)
      UltraMode7.animateRoll(2, 15);  // Tilt right when turning right
  }

			// Reset roll when not turning (like original); skip during landing attempt
		if (!pitchSetActive && !($gamePlayer._vehicleGettingOff && !$gameMap.isAirshipLandOk($gamePlayer.x, $gamePlayer.y)) && !_Input_isPressed.call(Input, 'left') && !_Input_isPressed.call(Input, 'right')) {
            UltraMode7.animateRoll(0, 15);
    }
		
  // Parallax roll disabled (was a no-op)

		

  if (!Spriteset_Map._mauiWrappedParallax) {
    Spriteset_Map._mauiWrappedParallax = true;

    const _updateParallax = Spriteset_Map.prototype.updateParallax;

    Spriteset_Map.prototype.updateParallax = function () {
      _updateParallax.call(this);

      const parallax = this._parallax;
      if (parallax && parallax instanceof TilingSprite) {
        // Expand the tiling area (bigger than the screen so rotation doesn't show edges)
        parallax.move(0, 0, Graphics.width * 2.1, Graphics.height * 2.1);

        // Center the tiling pattern visually
        parallax.anchor.set(0.5, 0.05);
        parallax.x = Graphics.width / 2;

        // Removed pitch-based parallax movement to keep it locked to the scene
        // const pitchParallax = (-Graphics.height / 3) * (1 - (UltraMode7.getPitch() - 45) / 10);
        // parallax.y = pitchParallax;

        parallax.scale.x = 0.5;
        parallax.scale.y = 0.5;

        // Parallax rotation disabled

        if (parallax.bitmap) {
          parallax.origin.x = -(UltraMode7.getYaw() * (parallax.bitmap.width / 180)) % parallax.bitmap.width;
        }
      }
    };
  }



  }
}

	  
  if (pitchSetActive || ($gamePlayer && $gamePlayer.areFollowersGathering && $gamePlayer.areFollowersGathering())) {
	  Game_Player.prototype.setDirection = function() { this._direction = 8; };
      Game_Follower.prototype.setDirection = function() { this._direction = 8; };	
      // Fire the 60f animation once AFTER followers gathered, then start countdown
      const followersGathered = ($gamePlayer && $gamePlayer.areFollowersGathered && $gamePlayer.areFollowersGathered());
      if (!pitchSetInit && followersGathered) {
        UltraMode7.animateCameraY(targetCameraY, 60);
        const currentAltitude = $gameMap.airship()._altitude || AIRSHIP_CONFIG.ALTITUDE.MIN;
        const dynamicPitch = CameraCalculator.calculateDynamicPitch(currentAltitude);
        const dynamicFov = CameraCalculator.calculateDynamicFov(currentAltitude);
        const dynamicCameraDistance = CameraCalculator.calculateDynamicCameraDistance(currentAltitude);
        const dynamicFarClip = CameraCalculator.calculateDynamicFarClip(currentAltitude);
        // Epsilon guarded boarding-settle animations to avoid redundant writes
        const eps = 0.05;
        if (Math.abs(UltraMode7.getPitch() - dynamicPitch) > eps) UltraMode7.animatePitch(dynamicPitch, 60);
        if (typeof UltraMode7.getFov === 'function' && Math.abs(UltraMode7.getFov() - dynamicFov) > eps) UltraMode7.animateFov(dynamicFov, 60);
        if (typeof UltraMode7.getCameraDistance === 'function' && Math.abs(UltraMode7.getCameraDistance() - dynamicCameraDistance) > eps) UltraMode7.animateCameraDistance(dynamicCameraDistance, 60);
        if (typeof UltraMode7.getFarClipZ === 'function' && Math.abs(UltraMode7.getFarClipZ() - dynamicFarClip) > 1) UltraMode7.animateFarClipZ(dynamicFarClip, 60);
        pitchSetInit = true;
        pitchSetFrames = 60;
      }
      if (pitchSetInit && pitchSetFrames > 0) pitchSetFrames--;
      if (pitchSetInit && pitchSetFrames <= 0) {
		pitchSetActive = false;
      }
    } 

	if (pitchResetActive) {
		   

		
		if (!$gamePlayer.isInAirship()){

		$gameTemp._yawFacing = 0;
		pitchResetActive = false;
		}

    }
	


Game_Player.prototype.getOnVehicle = function() {
  const result = _getOnVehicle.call(this);
  if (this.vehicle() && this.vehicle().isAirship()) {
    console.log("🚀 Airship takeoff: setting pitch to flying value.");

    // Initialize altitude for airship
    const airship = this.vehicle();
    AltitudeManager.initializeAltitude(airship);

    // Initialize integrated analog movement system (but gate actual control until followers gathered)
    if ($gamePlayer) {
        $gamePlayer.initAirshipMover();
    }
    console.log("✈️ Integrated airship analog movement initialized");

    // Initialize camera/effects systems and apply UM7 airship overrides
    if (typeof applyUM7OverridesForAirship === 'function') applyUM7OverridesForAirship();
    if (AirshipBanking && AirshipBanking.init) AirshipBanking.init();
    if (AdvancedCameraEffects && AdvancedCameraEffects.init) AdvancedCameraEffects.init();
    // Defer kinematics baseline to the first scene update to avoid transient positions
    kinLastScreenX = 0;
    kinLastScreenY = 0;

    // Ensure yaw baseline set immediately to avoid first-frame drift
    if (typeof $gameTemp !== 'undefined' && $gameTemp && $gameTemp._yawFacing === undefined) {
      $gameTemp._yawFacing = UltraMode7.getYaw();
    }

    pitchSetActive = true;
    pitchSetFrames = 0; // start counting only after followers gathered and settle enqueued
    pitchSetInit = false;
    wasFlying = true;
  }
  return result;
};

Game_Player.prototype.getOffVehicle = function() {
    const wasInAirship = this.isInAirship();
    const result = _getOffVehicle.call(this);
    // Defer cleanup to updateVehicleGetOff when landing truly completes
    if (wasInAirship) {
        // Restore original player and camera settings
        if (this._originalSettings) {
            this.setMoveSpeed(this._originalSettings.moveSpeed);
            UltraMode7.setYaw(this._originalSettings.cameraYaw);
            UltraMode7.setPitch(this._originalSettings.cameraPitch);
            this._originalSettings = null;
        }

        // Restore controls
        if (ControlManager && ControlManager.restoreControls) {
            ControlManager.restoreControls();
        }

        const x = this.x;
        const y = this.y;
        if ($gameMap.isAirshipLandOk(x, y)) {
            // Clean up integrated analog movement system
            if ($gamePlayer && $gamePlayer._airshipMover) {
                delete $gamePlayer._airshipMover;
            }
            console.log("🛬 Integrated airship analog movement deactivated");

            delete Game_Player.prototype.setDirection;
            delete Game_Follower.prototype.setDirection;

            Input._currentState['up'] = false;
            
            const landAnimationSpeed = 60; // turns airship back to world map settings
            UltraMode7.animateRoll(0,landAnimationSpeed);
            
            if (UltraMode7.getYaw() > 180) {
              UltraMode7.animateYaw(360, landAnimationSpeed);
              setTimeout(() => {
              UltraMode7.setYaw(0);
              }, (landAnimationSpeed) * 16.67);
            } else {
              UltraMode7.animateYaw(0, landAnimationSpeed);
            }
            
            UltraMode7.animateCameraY(AIRSHIP_CONFIG.CAMERA.DEFAULT_Y, landAnimationSpeed);
            // Restore camera distance/FOV/far clip if we saved them; otherwise honor map note tag defaults
            try {
              if (this._originalSettings) {
                if (typeof UltraMode7.animateCameraDistance === 'function' && typeof this._originalSettings.cameraDistance === 'number') {
                  UltraMode7.animateCameraDistance(this._originalSettings.cameraDistance, landAnimationSpeed);
                }
                if (typeof UltraMode7.animateFov === 'function' && typeof this._originalSettings.fov === 'number') {
                  UltraMode7.animateFov(this._originalSettings.fov, landAnimationSpeed);
                }
                if (typeof UltraMode7.animateFarClipZ === 'function' && typeof this._originalSettings.farClipZ === 'number') {
                  UltraMode7.animateFarClipZ(this._originalSettings.farClipZ, landAnimationSpeed);
                }
              } else if (window.$dataMap && $dataMap && $dataMap.meta) {
                // Use map note tags if present: <UltraMode7_CameraDistance:x>
                const meta = $dataMap.meta;
                if (typeof UltraMode7.animateCameraDistance === 'function' && meta.UltraMode7_CameraDistance) {
                  UltraMode7.animateCameraDistance(Number(meta.UltraMode7_CameraDistance), landAnimationSpeed);
                }
                if (typeof UltraMode7.animateFov === 'function' && meta.UltraMode7_Fov) {
                  UltraMode7.animateFov(Number(meta.UltraMode7_Fov), landAnimationSpeed);
                }
                if (typeof UltraMode7.animateFarClipZ === 'function' && meta.UltraMode7_FarClipZ) {
                  UltraMode7.animateFarClipZ(Number(meta.UltraMode7_FarClipZ), landAnimationSpeed);
                }
              }
              UltraMode7.animatePitch(
                Number.isFinite(AIRSHIP_CONFIG.CAMERA.DEFAULT_PITCH)
                  ? AIRSHIP_CONFIG.CAMERA.DEFAULT_PITCH
                  : (typeof UltraMode7.DEFAULT_PITCH === 'number' ? UltraMode7.DEFAULT_PITCH : 60),
                landAnimationSpeed
              );
            } catch(_) {}
             
            $gamePlayer.setDirection(4);        
            $gamePlayer._realDirection = 4;
        
            pitchResetActive = true;
            console.log("✅ Tile OK for landing — pitch reset active.");
        } else {
            console.log("❌ Tile not landable — skipping pitch reset.");
        }
        
        // Clear effects
        const cleanupArray = (array) => {
            if (!array) return;
            while (array.length > 0) {
                const sprite = array.shift();
                if (sprite && sprite.parent && !sprite._destroyed) {
                    sprite.destroy();
                }
            }
        };
        Object.values(AIRSHIP_EFFECTS).forEach(cleanupArray);
    }

    return result;
};



	
  };
  
  
  // 🆕 4. Altitude tracking and visual system
  const AltitudeManager = {
    // Initialize altitude for airship
    initializeAltitude: function(airship) {
      if (!airship || typeof airship._altitude !== 'number') {
        airship._altitude = AIRSHIP_CONFIG.ALTITUDE.MIN;
        console.log('📊 Airship altitude initialized to', airship._altitude);
      }
    },
    
    // Validate altitude value
    validateAltitude: function(altitude) {
      return Math.max(AIRSHIP_CONFIG.ALTITUDE.MIN, 
                     Math.min(AIRSHIP_CONFIG.ALTITUDE.MAX, altitude));
    },
    
    // Update altitude with validation
    updateAltitude: function(airship, newAltitude) {
      if (!airship) return;
      
      const validatedAltitude = this.validateAltitude(newAltitude);
      airship._altitude = validatedAltitude;
      
      return validatedAltitude;
    },
    
    // Get current altitude safely
    getCurrentAltitude: function(airship) {
      if (!airship || typeof airship._altitude !== 'number') {
        return AIRSHIP_CONFIG.ALTITUDE.MIN;
      }
      return airship._altitude;
    }
  };
  
  // Sprite draw override to apply altitude visually
  (function () {
    const _updateMode7Transform = Sprite_Character.prototype.updateMode7Transform;
    Sprite_Character.prototype.updateMode7Transform = function () {
      _updateMode7Transform.call(this);

      if (this._character === $gameMap.airship()) {
        const altitude = AltitudeManager.getCurrentAltitude(this._character);
        this.y -= altitude;
      }
    };
  })();
  
  // Ground Trail Sprite Class
  class GroundTrailSprite extends Sprite {
    constructor(x, y, airshipDirection) {
      super();
      this.x = x;
      this.y = y;
      this.opacity = 255;
      this.fadeTimer = EffectSystems.groundTrail.config.fadeTime;
      this.driftSpeed = 3.0 + (Math.random() - 0.5) * 2; // Random speed variation
      this.driftDirection = airshipDirection; // Direction the airship was moving
      this.horizontalDrift = (Math.random() - 0.5) * 2.5; // Fixed horizontal drift per particle
      this._destroyed = false; // Track destruction state
      this._bitmapKey = 'trail'; // Track bitmap key for cleanup
      this.createTrailBitmap();
    }
    
    createTrailBitmap() {
      const size = 2 + Math.floor(Math.random() * 2); // Reduced size between 2-3
      const bitmap = BitmapPool.getBitmap('trail', size, size);
      
      // OPTIMIZATION: Pre-calculate distance values to avoid repeated Math.sqrt
      const center = size / 2;
      const maxDistanceSquared = center * center; // Square of max distance
      
      // Create a circular white particle
      for (let i = 0; i < size; i++) {
        for (let j = 0; j < size; j++) {
          const distanceSquared = (i - center) ** 2 + (j - center) ** 2;
          if (distanceSquared <= maxDistanceSquared) {
            bitmap.fillRect(i, j, 1, 1, '#FFFFFF');
          }
        }
      }
      
      this.bitmap = bitmap;
      this.anchor.set(0.5, 0.5);
    }
    
    update() {
      super.update();
      this.fadeTimer--;
      this.opacity = Math.max(0, (this.fadeTimer / EffectSystems.groundTrail.config.fadeTime) * 255);
      
      // Always add downward drift to simulate gravity
      this.y += this.driftSpeed * 0.8;
      
      // Add fixed horizontal drift for this particle
      this.x += this.horizontalDrift;
      
      if (this.fadeTimer <= 0) {
        this._destroyed = true;
        this.destroy();
      }
    }
    
    // FIX: Proper cleanup to prevent memory leaks
    destroy(options) {
      // Return bitmap to pool before destroying sprite
      if (this.bitmap && this._bitmapKey) {
        BitmapPool.returnBitmap(this._bitmapKey, this.bitmap);
        this.bitmap = null;
      }
      super.destroy(options);
    }
  }
  
  // Dust Cloud Sprite Class
  class DustCloudSprite extends Sprite {
    constructor(x, y, airshipDirection) {
      super();
      this.x = x;
      this.y = y;
      this.opacity = 255;
      this.fadeTimer = EffectSystems.groundTrail.config.fadeTime;
      this.driftSpeed = 3.0 + (Math.random() - 0.5) * 2; // Random speed variation
      this.driftDirection = airshipDirection; // Direction the airship was moving
      this.horizontalDrift = (Math.random() - 0.5) * 2.5; // Fixed horizontal drift per particle
      this._destroyed = false; // Track destruction state
      this._bitmapKey = 'dust'; // Track bitmap key for cleanup
      this.createDustBitmap();
    }
    
    createDustBitmap() {
      const size = 2 + Math.floor(Math.random() * 2); // Reduced size between 2-3
      const bitmap = BitmapPool.getBitmap('dust', size, size);
      
      // OPTIMIZATION: Pre-calculate distance values to avoid repeated Math.sqrt
      const center = size / 2;
      const maxDistanceSquared = center * center; // Square of max distance
      
      // Create a circular brown particle (exactly like white trail but brown)
      for (let i = 0; i < size; i++) {
        for (let j = 0; j < size; j++) {
          const distanceSquared = (i - center) ** 2 + (j - center) ** 2;
          if (distanceSquared <= maxDistanceSquared) {
            bitmap.fillRect(i, j, 1, 1, '#8B7355'); // Brown dust color
          }
        }
      }
      
      this.bitmap = bitmap;
      this.anchor.set(0.5, 0.5);
    }
    
    update() {
      super.update();
      this.fadeTimer--;
      this.opacity = Math.max(0, (this.fadeTimer / EffectSystems.groundTrail.config.fadeTime) * 255);
      
      // Always add downward drift to simulate gravity
      this.y += this.driftSpeed * 0.8;
      
      // Add fixed horizontal drift for this particle
      this.x += this.horizontalDrift;
      
      if (this.fadeTimer <= 0) {
        this._destroyed = true;
        this.destroy();
      }
    }
    
    // FIX: Proper cleanup to prevent memory leaks
    destroy(options) {
      // Return bitmap to pool before destroying sprite
      if (this.bitmap && this._bitmapKey) {
        BitmapPool.returnBitmap(this._bitmapKey, this.bitmap);
        this.bitmap = null;
      }
      super.destroy(options);
    }
  }
  
  // Removed GrassDisturbanceSprite (unused)
  
  // Wind Lines Sprite Class
  class WindLinesSprite extends Sprite {
    constructor(x, y, direction) {
      super();
      this.x = x;
      this.y = y;
      this.direction = direction; // 0-360 degrees
      this.opacity = 200; // Higher opacity
      this.fadeTimer = 25; // Shorter lifetime for better performance
      this.length = 40 + Math.random() * 30; // Shorter lines for better performance
      this.width = 1 + Math.random() * 1; // Thinner lines
      this._destroyed = false; // Track destruction state
      this._bitmapKey = 'windLine'; // Track bitmap key for cleanup
      this.createWindLineBitmap();
    }
    
    createWindLineBitmap() {
      const bitmap = BitmapPool.getBitmap('windLine', this.length, this.width);
      const ctx = bitmap.context;
      
      // Create a wind line effect
      ctx.fillStyle = '#FFFFFF'; // White wind lines
      ctx.globalAlpha = 1.0; // Full opacity
      
      // Draw the wind line
      ctx.fillRect(0, 0, this.length, this.width);
      
      this.bitmap = bitmap;
      this.anchor.set(0, 0.5); // Anchor at left center
      this.rotation = this.direction * (Math.PI / 180); // Rotate to radial direction
    }
    
    update() {
      super.update();
      this.fadeTimer--;
      this.opacity = Math.max(0, (this.fadeTimer / 25) * 200); // Use shorter timer
      
      // Move outward from center
      const speed = 2.5 + Math.random() * 1.5; // Slightly slower for better performance
      const angle = this.direction * (Math.PI / 180);
      this.x += Math.cos(angle) * speed;
      this.y += Math.sin(angle) * speed;
      
      if (this.fadeTimer <= 0) {
        this._destroyed = true;
        this.destroy();
      }
    }
    
    // FIX: Proper cleanup to prevent memory leaks
    destroy(options) {
      // Return bitmap to pool before destroying sprite
      if (this.bitmap && this._bitmapKey) {
        BitmapPool.returnBitmap(this._bitmapKey, this.bitmap);
        this.bitmap = null;
      }
      super.destroy(options);
    }
  }
  
  // Inner Wind Lines Sprite Class (more transparent, closer to center)
  class InnerWindLinesSprite extends Sprite {
    constructor(x, y, direction) {
      super();
      this.x = x;
      this.y = y;
      this.direction = direction; // 0-360 degrees
      this.opacity = 60; // Even lower opacity for better performance
      this.fadeTimer = 20; // Shorter lifetime
      this.length = 30 + Math.random() * 20; // Shorter lines for better performance
      this.width = 1; // Fixed thin width
      this._destroyed = false; // Track destruction state
      this._bitmapKey = 'windLine'; // Track bitmap key for cleanup
      this.createWindLineBitmap();
    }
    
    createWindLineBitmap() {
      const bitmap = BitmapPool.getBitmap('windLine', this.length, this.width);
      const ctx = bitmap.context;
      
      // Create a wind line effect
      ctx.fillStyle = '#FFFFFF'; // White wind lines
      ctx.globalAlpha = 0.5; // Lower alpha for transparency
      
      // Draw the wind line
      ctx.fillRect(0, 0, this.length, this.width);
      
      this.bitmap = bitmap;
      this.anchor.set(0, 0.5); // Anchor at left center
      this.rotation = this.direction * (Math.PI / 180); // Rotate to radial direction
    }
    
    update() {
      super.update();
      this.fadeTimer--;
      this.opacity = Math.max(0, (this.fadeTimer / 20) * 60); // Use lower opacity
      
      // Move outward from center
      const speed = 1.5 + Math.random() * 1.5; // Slower for better performance
      const angle = this.direction * (Math.PI / 180);
      this.x += Math.cos(angle) * speed;
      this.y += Math.sin(angle) * speed;
      
      if (this.fadeTimer <= 0) {
        this._destroyed = true;
        this.destroy();
      }
    }
    
    // FIX: Proper cleanup to prevent memory leaks
    destroy(options) {
      // Return bitmap to pool before destroying sprite
      if (this.bitmap && this._bitmapKey) {
        BitmapPool.returnBitmap(this._bitmapKey, this.bitmap);
        this.bitmap = null;
      }
      super.destroy(options);
    }
  }
  
  // Water Ripple Sprite Class
  class WaterRippleSprite extends Sprite {
    constructor(x, y) {
      super();
      this.x = x;
      this.y = y;
      this.opacity = 255; // Increased opacity
      this.fadeTimer = 60; // Half the lifetime
      this.expansionRate = 1.5; // Reduced expansion rate
      this.driftSpeed = 4.0 + (Math.random() - 0.5) * 2; // Faster downward drift
      this._destroyed = false; // Track destruction state
      this.createRippleBitmap();
    }
    
    createRippleBitmap() {
      const size = 48; // Larger size for better detail
      const bitmap = new Bitmap(size, size);
      const ctx = bitmap.context;
      
      // Create multiple concentric ripples for more natural look
      const rippleCount = 3;
      const baseRadius = size / 4;
      
      for (let i = 0; i < rippleCount; i++) {
        const radius = baseRadius + (i * 2);
        const alpha = 0.8 - (i * 0.2); // Fade outer rings
        
      ctx.strokeStyle = '#87CEEB'; // Sky blue water color
        ctx.lineWidth = 1.5; // Thinner lines
        ctx.globalAlpha = alpha;
      
        // Draw multiple concentric circles
      ctx.beginPath();
        ctx.arc(size/2, size/2, radius, 0, Math.PI * 2);
      ctx.stroke();
      }
      
      // Add a subtle inner highlight
      ctx.fillStyle = '#FFFFFF';
      ctx.globalAlpha = 0.3;
      ctx.beginPath();
      ctx.arc(size/2, size/2, baseRadius - 2, 0, Math.PI * 2);
      ctx.fill();
      
      this.bitmap = bitmap;
      this.anchor.set(0.5, 0.5);
      this.scale.set(0.3, 0.3); // Start with slightly larger scale
    }
    
    update() {
      super.update();
      this.fadeTimer--;
      
      // More natural fade curve (faster at start, slower at end)
      const fadeProgress = this.fadeTimer / 60;
      this.opacity = Math.max(0, Math.pow(fadeProgress, 1.5) * 255);
      
      // Expand the ripple with easing (slower expansion over time)
      const expansionProgress = 1 - fadeProgress;
      const expansionEasing = Math.pow(expansionProgress, 0.7);
      this.scale.x = 0.3 + (this.expansionRate * expansionEasing);
      this.scale.y = 0.3 + (this.expansionRate * expansionEasing);
      
      // Add subtle downward drift
      this.y += this.driftSpeed * 0.5; // Reduced drift speed
      
      // Add slight rotation for more natural movement
      this.rotation += 0.02;
      
      if (this.fadeTimer <= 0) {
        this._destroyed = true;
        this.destroy();
      }
    }
  }
  
  // Removed WaterSpraySprite (unused)
  
  // Cloud Sprite Class for high-altitude cloud flying effect
  class CloudSprite extends Sprite {
    constructor(x, y, size, opacity, direction, speedMultiplier = 1.0) {
      super();
      this.x = x;
      this.y = y;
      this.initialSize = size;
      this.size = size;
      this.opacity = opacity;
      this.direction = direction; // Direction toward center
      this.fadeTimer = 120; // Longer lifetime for clouds
      this.speed = (10.0 + Math.random() * 10.0) * speedMultiplier; // Speed with multiplier
      this.airshipX = Graphics.width / 2; // Will be updated each frame
      this.airshipY = Graphics.height / 2; // Will be updated each frame
      this._destroyed = false; // Track destruction state
      this._lastX = x; // Track previous position for motion blur prevention
      this._lastY = y;
      this.createCloudBitmap();
      
      // Apply quality settings
      VisualQualityManager.applyQualitySettings(this, VisualQualityManager.getSpriteQuality());
    }
    
    createCloudBitmap() {
      const bitmap = new Bitmap(this.size, this.size);
      const ctx = bitmap.context;
      
      // Create a soft, fluffy cloud effect
      ctx.fillStyle = '#FFFFFF';
      ctx.globalAlpha = this.opacity / 255;
      
      // Draw multiple overlapping circles to create horizontally oval cloud shape
      const centerX = this.size / 2;
      const centerY = this.size / 2;
      const radius = this.size / 3;
      
      // Main cloud body (horizontally stretched)
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
      ctx.fill();
      
      // Additional cloud puffs (more horizontal spread)
      ctx.beginPath();
      ctx.arc(centerX - radius * 0.8, centerY, radius * 0.6, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.beginPath();
      ctx.arc(centerX + radius * 0.8, centerY, radius * 0.6, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.beginPath();
      ctx.arc(centerX - radius * 1.2, centerY, radius * 0.5, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.beginPath();
      ctx.arc(centerX + radius * 1.2, centerY, radius * 0.5, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.beginPath();
      ctx.arc(centerX, centerY - radius * 0.2, radius * 0.4, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.beginPath();
      ctx.arc(centerX, centerY + radius * 0.2, radius * 0.4, 0, Math.PI * 2);
      ctx.fill();
      
      this.bitmap = bitmap;
      this.anchor.set(0.5, 0.5); // Center anchor
      
      // Set initial scale to 0 (clouds start invisible and grow from nothing)
      this.scale.set(0, 0);
    }
    
    update() {
      super.update();
      this.fadeTimer--;
      this.opacity = Math.max(0, (this.fadeTimer / 120) * this.opacity);
      
      // Update airship position for this cloud
      const airship = $gameMap.airship();
      if (airship) {
        this.airshipX = airship.screenX();
        this.airshipY = airship.screenY();
      }
      
      // Store previous position for motion blur prevention
      this._lastX = this.x;
      this._lastY = this.y;
      
      // Move clouds in radial flow pattern with smooth interpolation
      const targetX = this.x + Math.cos(this.direction) * this.speed;
      const targetY = this.y + Math.sin(this.direction) * this.speed;
      
      // Smooth movement to prevent visual glitches
      this.x = this.x + (targetX - this.x) * 0.8;
      this.y = this.y + (targetY - this.y) * 0.8;
      
      // Calculate distance from horizon for scaling (clouds get larger as they move away from horizon)
      const horizonY = Graphics.height * 0.7;
      const distanceFromHorizon = horizonY - this.y; // Positive = further from horizon
      const maxDistance = Graphics.height * 0.3; // Maximum distance from horizon
      const scaleFactor = 1 + (distanceFromHorizon / maxDistance) * -2 // Scale as moving away from horizon
      
      // Scale the cloud as it flows down (gets larger)
      this.scale.set(scaleFactor, scaleFactor);
      
      // Destroy if too far down or off-screen
      if (this.y > Graphics.height + SEAM_GUARD_PADDING || this.x < -SEAM_GUARD_PADDING || this.x > Graphics.width + SEAM_GUARD_PADDING || 
          this.y < -SEAM_GUARD_PADDING) {
        this._destroyed = true;
        this.destroy();
      }
    }
  }
  
  // Ground trail management with proper z-index layering
  const _SceneMap_createDisplayObjects = Scene_Map.prototype.createDisplayObjects;
  Scene_Map.prototype.createDisplayObjects = function() {
    _SceneMap_createDisplayObjects.call(this);
    
    // Create layered sprite system for proper rendering order
    this._airshipEffectLayers = {
      // Background effects (water ripples, ground trails)
      background: new Sprite(),
      // Mid-level effects (dust clouds, wind lines)
      midground: new Sprite(),
      // Foreground effects (clouds)
      foreground: new Sprite()
    };
    
    // Set proper z-indices to avoid conflicts
    this._airshipEffectLayers.background.z = 1;  // Below characters
    this._airshipEffectLayers.midground.z = 3;   // Above characters
    this._airshipEffectLayers.foreground.z = 5;  // Above UI elements
    
    // Add layers to scene
    Object.values(this._airshipEffectLayers).forEach(layer => {
      this.addChild(layer);
    });
    
    // Legacy layer references for backward compatibility
    this._trailLayer = this._airshipEffectLayers.background;
    this._effectsLayer = this._airshipEffectLayers.midground;

    // Global overlay container for screen-space effects below HUD
    if (!this._airshipOverlays) {
      this._airshipOverlays = new Sprite();
      this._airshipOverlays.z = 9400; // Below HUD (9500)
      this.addChild(this._airshipOverlays);
    }

    // Create boost visuals (vignette + flare) once
    this._createBoostVisuals();
    // Create altitude ambience overlays once
    this._createAltitudeAmbience();
    // Initialize shockwave container and cached mappings
    this._shockwaves = [];
    this._lbAction = (Input && Input.gamepadMapper) ? Input.gamepadMapper[4] : null;
    this._randSeed = ((Graphics.frameCount || 0) * 1664525 + 1013904223) >>> 0;
    if (!this._boostTickAngles) { this._boostTickAngles = []; for (let i=0;i<12;i++){ const t=i/12; const a=-Math.PI/2 + t*2*Math.PI; this._boostTickAngles.push({c:Math.cos(a), s:Math.sin(a)}); } }
  };
  
  // Extend the existing Scene_Map update function to include trail updates
  const _SceneMap_update_original = Scene_Map.prototype.update;
  Scene_Map.prototype.update = function() {
    _SceneMap_update_original.call(this);
    
    // Performance optimization: only update effects every few frames
    frameCounter++;
    
    // Update ground trail and low-altitude effects while in airship; input influences density
    if ($gamePlayer && $gamePlayer.isInAirship() && !pitchSetActive) {
      // Track airship state for cleanup
      if (!isAirshipActive) {
        isAirshipActive = true;
        if (DEBUG_MODE) console.log('🚀 Airship effects activated (up key pressed)');
      }
      
      // Update effects with frame skipping for performance
      if (!pitchSetActive && frameCounter % UPDATE_FREQUENCY === 0) {
        // Apply dynamic quality settings based on performance
        const quality = VisualQualityManager.getSpriteQuality();
        const density = VisualQualityManager.getEffectDensity();
        
        // Determine true lateral movement (ignores altitude-only changes)
        const airship = $gameMap.airship();
        if (airship) {
          if (lastRealX === null || lastRealY === null) {
            lastRealX = airship._realX;
            lastRealY = airship._realY;
          }
          const dx = Math.abs((airship._realX || 0) - lastRealX);
          const dy = Math.abs((airship._realY || 0) - lastRealY);
          // Threshold avoids noise; tune as needed
          isAirshipLateralMoving = dx > 0.01 || dy > 0.01;
          lastRealX = airship._realX;
          lastRealY = airship._realY;
          // Boost charge logic
          const dt = UPDATE_FREQUENCY / (Graphics.frameRate || 60);
          if (BOOST_ENABLED) {
            // Manual boost while PageUp held (requires charge > 0)
            // Detect boost-hold via configurable action and cached LB mapping
            const lbAction = this._lbAction;
            const manualBoostHeld = !!(
              Input.isPressed(BOOST_HOLD_ACTION) ||
              (lbAction && Input.isPressed(lbAction))
            );
            if (!boostActive && manualBoostHeld && boostCharge01 > 0) {
              boostActive = true; boostManualMode = true; // engage while held
              if (!boostActivePlayed && BOOST_ACTIVE_SE) {
                AudioManager.playSe({name: BOOST_ACTIVE_SE, pan: 0, pitch: 100, volume: BOOST_ACTIVE_SE_VOL});
                boostActivePlayed = true;
              }
            }
            // If in manual mode but button released, end boost immediately
            if (boostActive && boostManualMode && !manualBoostHeld) {
              boostActive = false; boostManualMode = false; boostActivePlayed = false;
            }
            if (boostActive) {
              const drain = Math.min(BOOST_ACTIVE_DRAIN_RATE * dt, boostCharge01);
              boostCharge01 -= drain;
              if (boostCharge01 <= 0) { boostCharge01 = 0; boostActive = false; boostActivePlayed = false; boostReadyPlayed = false; }
            } else {
              if (isAirshipLateralMoving) {
                boostCharge01 = Math.min(1, boostCharge01 + BOOST_FILL_RATE * dt);
              } else {
                boostCharge01 = Math.max(0, boostCharge01 - BOOST_STOP_DRAIN_RATE * dt);
              }
              // Auto-boost removed: player must hold the boost action to engage
              // ready state feedback when just filled
              if (!boostActive && boostCharge01 >= 1 && !boostReadyPlayed) {
                boostReadyFlashFrames = 12;
                if (BOOST_READY_SE) AudioManager.playSe({name: BOOST_READY_SE, pan: 0, pitch: 100, volume: BOOST_READY_SE_VOL});
                boostReadyPlayed = true;
              }
            }
          }
        } else {
          isAirshipLateralMoving = false;
        }

        // Update effects with quality consideration; only spawn particles when laterally moving
        if (isAirshipLateralMoving) {
        this.updateGroundTrail(density);
        this.updateLowAltitudeEffects(density);
        this.updateCloudFlying(density);
          this.updateContrails(density);
          this.updateVortices(density);
        }

        // Update boost/altitude overlays
        this.updateBoostVisuals();
        this.updateAltitudeAmbience();
        
        // NEW: Update advanced camera effects
        if ($gameMap && $gameMap.airship()) {
          const airship = $gameMap.airship();
          const altitude = airship._altitude || 0;
            // Update kinematics baseline for this frame
            if (kinLastScreenX === 0 && kinLastScreenY === 0) {
              kinLastScreenX = airship.screenX();
              kinLastScreenY = airship.screenY();
            }
          
          // Update airship banking/tilting
          if (unlandableLandingSuppressFrames === 0) {
          AirshipBanking.updateTilt(airship);
          }
          
          AdvancedCameraEffects.updateShake(airship, altitude);
          AdvancedCameraEffects.updateDepthOfField(altitude);
          AdvancedCameraEffects.updateZoom(airship, altitude);
          AdvancedCameraEffects.updateMotionBlur(airship);
          AdvancedCameraEffects.updateLetterbox(airship, altitude);
          AdvancedCameraEffects.updateCinematic(airship, altitude);
          AdvancedCameraEffects.updateDollyZoom(altitude);
          AdvancedCameraEffects.updateDutchAngle(airship);
          AdvancedCameraEffects.updateWhipPan(airship);
          AdvancedCameraEffects.updateBreathing();
          AdvancedCameraEffects.updateSlowMotion(airship);
          AdvancedCameraEffects.updateCameraRoll(airship);
          AdvancedCameraEffects.updateBulletTime(airship);
          AdvancedCameraEffects.updateTeleport(altitude);
          AdvancedCameraEffects.updateGravityDefy(altitude);
            // After computing deltas, store current position for next frame
            kinLastScreenX = airship.screenX();
            kinLastScreenY = airship.screenY();
            if (unlandableLandingSuppressFrames === 0) {
          AdvancedCameraEffects.applyEffects();
            }
        }
      }
      
      // Always run cleanup (but less frequently)
      if (frameCounter % (UPDATE_FREQUENCY * 3) === 0) {
        this.cleanupDestroyedSprites();
      }
    } else {
      // Airship is not active, ensure cleanup
      if (isAirshipActive) {
        isAirshipActive = false;
        this.cleanupAllAirshipEffects();
        if (DEBUG_MODE) console.log('🛬 Airship effects deactivated');
      }
    }

    // Safety net: after landing, ensure controls and direction overrides are restored even if input was mashed
    if ($gamePlayer && !$gamePlayer.isInAirship()) {
      try {
        // Restore controls if still swapped
        if (ControlManager && ControlManager.controlsSwapped) {
          ControlManager.forceRestoreControls();
        }
        // Remove any lingering direction overrides from takeoff phase
        if (Game_Player.prototype.setDirection && Game_Player.prototype.setDirection.toString().indexOf('this._direction = 8') !== -1) {
          delete Game_Player.prototype.setDirection;
        }
        if (Game_Follower.prototype.setDirection && Game_Follower.prototype.setDirection.toString().indexOf('this._direction = 8') !== -1) {
          delete Game_Follower.prototype.setDirection;
        }
      } catch(_) {}
    }
    // Countdown suppress frames
    if (unlandableLandingSuppressFrames > 0) unlandableLandingSuppressFrames--;
  };
  
  // Comprehensive sprite cleanup system - OPTIMIZED with efficient array operations
  Scene_Map.prototype.cleanupDestroyedSprites = function() {
    // FIX: Properly destroy sprites instead of just removing references
    const cleanupArray = (array) => {
      const validSprites = [];
      for (let i = 0; i < array.length; i++) {
        const sprite = array[i];
        if (sprite && sprite.parent && !sprite._destroyed) {
          validSprites.push(sprite);
        } else if (sprite && sprite._destroyed) {
          // FIX: Properly destroy sprites that are marked as destroyed
          if (sprite.parent) {
            sprite.parent.removeChild(sprite);
          }
          if (sprite.destroy) {
            sprite.destroy();
          }
        }
      }
      return validSprites;
    };
    
    // Clean up all sprite arrays by removing destroyed sprites
    groundTrailSprites = cleanupArray(groundTrailSprites);
    waterRippleSprites = cleanupArray(waterRippleSprites);
    windLinesSprites = cleanupArray(windLinesSprites);
    backgroundCloudSprites = cleanupArray(backgroundCloudSprites);
    foregroundCloudSprites = cleanupArray(foregroundCloudSprites);
    dustCloudSprites = cleanupArray(dustCloudSprites);
    
    // Emergency cleanup if arrays grow too large
    const maxSpritesPerType = AIRSHIP_CONFIG.PERFORMANCE.MAX_SPRITES_PER_TYPE;
    if (groundTrailSprites.length > maxSpritesPerType) {
      if (DEBUG_MODE) console.warn('⚠️ Too many ground trail sprites, forcing cleanup');
      this.forceCleanupSprites(groundTrailSprites, maxSpritesPerType);
    }
    if (waterRippleSprites.length > maxSpritesPerType) {
      if (DEBUG_MODE) console.warn('⚠️ Too many water ripple sprites, forcing cleanup');
      this.forceCleanupSprites(waterRippleSprites, maxSpritesPerType);
    }
    if (windLinesSprites.length > maxSpritesPerType) {
      if (DEBUG_MODE) console.warn('⚠️ Too many wind line sprites, forcing cleanup');
      this.forceCleanupSprites(windLinesSprites, maxSpritesPerType);
    }
    if (contrailSprites.length > maxSpritesPerType) {
      if (DEBUG_MODE) console.warn('⚠️ Too many contrail sprites, forcing cleanup');
      this.forceCleanupSprites(contrailSprites, maxSpritesPerType);
    }
    if (vortexSprites.length > maxSpritesPerType) {
      if (DEBUG_MODE) console.warn('⚠️ Too many vortex sprites, forcing cleanup');
      this.forceCleanupSprites(vortexSprites, maxSpritesPerType);
    }
    if (backgroundCloudSprites.length > maxSpritesPerType) {
      if (DEBUG_MODE) console.warn('⚠️ Too many background cloud sprites, forcing cleanup');
      this.forceCleanupSprites(backgroundCloudSprites, maxSpritesPerType);
    }
    if (foregroundCloudSprites.length > maxSpritesPerType) {
      if (DEBUG_MODE) console.warn('⚠️ Too many foreground cloud sprites, forcing cleanup');
      this.forceCleanupSprites(foregroundCloudSprites, maxSpritesPerType);
    }
    if (dustCloudSprites.length > maxSpritesPerType) {
      if (DEBUG_MODE) console.warn('⚠️ Too many dust cloud sprites, forcing cleanup');
      this.forceCleanupSprites(dustCloudSprites, maxSpritesPerType);
    }
  };
  
  // Force cleanup when sprite arrays grow too large
  Scene_Map.prototype.forceCleanupSprites = function(spriteArray, maxCount) {
    // Remove oldest sprites first
    const spritesToRemove = spriteArray.splice(0, spriteArray.length - maxCount);
    spritesToRemove.forEach(sprite => {
      if (sprite && sprite.parent) {
        sprite.parent.removeChild(sprite);
        sprite._destroyed = true;
        // FIX: Properly destroy sprites to prevent memory leaks
        if (sprite.destroy) {
          sprite.destroy();
        }
      }
    });
  };
  
  // FIX: Add timer cleanup to prevent memory leaks
  let airshipIntegrityTimer = null;
  
  // Comprehensive cleanup when airship effects are deactivated
  Scene_Map.prototype.cleanupAllAirshipEffects = function() {
    if (DEBUG_MODE) console.log('🧹 Cleaning up all airship effects');
    
    // FIX: Clear the integrity check timer
    if (airshipIntegrityTimer) {
      clearInterval(airshipIntegrityTimer);
      airshipIntegrityTimer = null;
    }
    
    // Instead of immediately destroying particles, let them fade naturally
    // Only clean up if there are too many particles for performance
    const maxSpritesPerType = AIRSHIP_CONFIG.PERFORMANCE.MAX_SPRITES_PER_TYPE;
    
    const allSpriteArrays = [
      { name: 'groundTrail', array: groundTrailSprites },
      { name: 'waterRipple', array: waterRippleSprites },
      { name: 'windLines', array: windLinesSprites },
      { name: 'backgroundCloud', array: backgroundCloudSprites },
      { name: 'foregroundCloud', array: foregroundCloudSprites },
      { name: 'dustCloud', array: dustCloudSprites },
      { name: 'contrail', array: contrailSprites },
      { name: 'vortex', array: vortexSprites }
    ];
    
    allSpriteArrays.forEach(({ name, array }) => {
      // Only force cleanup if array is too large for performance
      if (array.length > maxSpritesPerType * 2) {
      let cleanedCount = 0;
        // Remove oldest sprites first, but keep some for natural fade
        const spritesToRemove = array.splice(0, array.length - maxSpritesPerType);
        spritesToRemove.forEach(sprite => {
        if (sprite && sprite.parent) {
          sprite.parent.removeChild(sprite);
          sprite._destroyed = true;
          // FIX: Properly destroy sprites to prevent memory leaks
          if (sprite.destroy) {
            sprite.destroy();
          }
          cleanedCount++;
        }
      });
                              if (DEBUG_MODE) console.log(`🧹 Force cleaned up ${cleanedCount} ${name} sprites (performance)`);
                    } else {
                      if (DEBUG_MODE) console.log(`🧹 Allowing ${array.length} ${name} sprites to fade naturally`);
                    }
    });
    
    // Reset timers but don't clear arrays
    trailCreationTimer = 0;
    rippleCreationTimer = 0;
    windLinesCreationTimer = 0;
    cloudCreationTimer = 0;
    lastAirshipX = 0;
    lastAirshipY = 0;
  };
  
  Scene_Map.prototype.updateGroundTrail = function(density = 1.0) {
    // Performance check: skip if not in airship
    if (!$gamePlayer || !$gamePlayer.isInAirship()) return;
    
    const airship = $gameMap.airship();
    if (!airship || !this._airshipEffectLayers) return;
    
    // Null safety checks
    if (!airship._altitude || typeof airship._altitude !== 'number') return;
    
    const altitude = airship._altitude || 0;
    const airshipX = Math.floor(airship.x);
    const airshipY = Math.floor(airship.y);
    
    // Check if airship is over water
    const isOverWater = $gameMap.isWaterTile ? $gameMap.isWaterTile(airshipX, airshipY) : 
                       ($gameMap.isBoatPassable(airshipX, airshipY) || $gameMap.isShipPassable(airshipX, airshipY));
    
    // Only create trail at low altitude AND when moving AND over water
    // But allow existing particles to continue their natural fade cycle
    if (altitude <= AIRSHIP_CONFIG.EFFECTS.TRAIL_LOW_ALTITUDE && 
        isAirshipLateralMoving === true && 
        isOverWater) {
      
      trailCreationTimer++;
      
      // Create trail with dynamic timing based on altitude - more frequent at lower altitudes
      const altitudeBasedTimer = Math.max(1, Math.floor(8 - (altitude / 15))); // 1-8 frames based on altitude
      if (trailCreationTimer >= altitudeBasedTimer + Math.floor(Math.random() * 3)) {
        trailCreationTimer = 0;
        
        // Get airship position in screen coordinates with proper offset calculation
        const x = airship.screenX();
        const y = airship.screenY() + 44; // Offset to match shadow position + 20px down
        
        // Validate coordinates to prevent visual glitches
        if (isNaN(x) || isNaN(y) || x < -SEAM_GUARD_PADDING || x > Graphics.width + SEAM_GUARD_PADDING || y < -SEAM_GUARD_PADDING || y > Graphics.height + SEAM_GUARD_PADDING) {
          console.warn('⚠️ Invalid airship coordinates detected, skipping trail creation');
          return;
        }
        
        // Determine airship movement direction
        let airshipDirection = 0;
        if (lastAirshipX !== 0 && lastAirshipY !== 0) {
          const deltaX = x - lastAirshipX;
          const deltaY = y - lastAirshipY;
          
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            airshipDirection = deltaX > 0 ? 6 : 4; // Right or Left
          } else {
            airshipDirection = deltaY > 0 ? 2 : 8; // Down or Up
          }
        }
        
        // Add more randomness to make it look more chaotic
        const randomX = x + (Math.random() - 0.5) * 12;
        const randomY = y + (Math.random() - 0.5) * 6;
        
        // Create more trail points at lower altitudes for more dramatic effect
        const baseTrailCount = altitude <= 40 ? 8 : (altitude <= 80 ? 5 : (altitude <= 120 ? 3 : 1)); // Optimized scaling
        const trailCount = Math.floor(baseTrailCount * density);
        
        // Enhanced altitude-based particle scaling
        const altitudeIntensity = Math.max(0.1, 1.0 - (altitude / AIRSHIP_CONFIG.EFFECTS.TRAIL_LOW_ALTITUDE)); // More intense at lower altitudes
        const particleSizeMultiplier = 1.0 + (altitudeIntensity * 1.0); // Particles 1-2x larger at low altitude
        const particleSpeedMultiplier = 1.0 + (altitudeIntensity * 1.0); // Particles move 1-2x faster at low altitude
        
        for (let i = 0; i < trailCount; i++) {
          try {
            const trailSprite = new GroundTrailSprite(randomX, randomY, airshipDirection);
            
            // Apply altitude-based scaling to particle properties
            if (trailSprite) {
              // Scale particle size based on altitude
              trailSprite.scale.set(
                trailSprite.scale.x * particleSizeMultiplier,
                trailSprite.scale.y * particleSizeMultiplier
              );
              
              // Adjust particle speed based on altitude
              trailSprite.driftSpeed *= particleSpeedMultiplier;
              trailSprite.horizontalDrift *= particleSpeedMultiplier;
              
              // Adjust opacity based on altitude (more opaque at lower altitudes)
              trailSprite.opacity = Math.min(255, trailSprite.opacity * (1.0 + altitudeIntensity * 0.5));
              
              // Adjust fade time based on altitude (longer fade at lower altitudes)
              trailSprite.fadeTimer = Math.floor(trailSprite.fadeTimer * (1.0 + altitudeIntensity * 0.3));
            }
            
            if (this._airshipEffectLayers.background && trailSprite) {
              this._airshipEffectLayers.background.addChild(trailSprite);
              groundTrailSprites.push(trailSprite);
            }
          } catch (error) {
            console.error('❌ Error creating trail sprite:', error);
          }
        }
        
        // Update last position
        lastAirshipX = x;
        lastAirshipY = y;
        
        // Debug: log trail creation
        console.log(`🌪️ Trail created at altitude ${altitude}, position (${randomX.toFixed(1)}, ${randomY.toFixed(1)})`);
        
        // Limit number of trail points (reduced from 25 to 15)
        if (groundTrailSprites.length > 15) {
          const oldTrail = groundTrailSprites.shift();
          if (oldTrail && oldTrail.parent) {
            oldTrail.parent.removeChild(oldTrail);
          }
        }
      }
    } else {
      // Reset timer when not creating trails
      trailCreationTimer = 0;
      // Reset position tracking when not moving
      lastAirshipX = 0;
      lastAirshipY = 0;
    }
    
    // Create water ripples at very low altitude over water
    {
      const movingOverWater = isOverWater && (isAirshipLateralMoving === true);
      if (altitude <= AIRSHIP_CONFIG.EFFECTS.RIPPLE_LOW_ALTITUDE && movingOverWater) {
      rippleCreationTimer++;
      
      // Create ripples with dynamic timing based on altitude - more frequent at lower altitudes
        const rippleAltitudeBasedTimer = Math.max(2, Math.floor(10 - (altitude / 10))); // 2-10 frames based on altitude
      if (rippleCreationTimer >= rippleAltitudeBasedTimer + Math.floor(Math.random() * 4)) {
        rippleCreationTimer = 0;
        
        const rippleX = airship.screenX(); // No random X variation
        const rippleY = airship.screenY() + 80; // Position ripples on water surface
        
        try {
          const waterRipple = new WaterRippleSprite(rippleX, rippleY);
          
          // Enhanced altitude-based water ripple scaling
          if (waterRipple) {
            const rippleAltitudeIntensity = Math.max(0.1, 1.0 - (altitude / AIRSHIP_CONFIG.EFFECTS.RIPPLE_LOW_ALTITUDE)); // More intense at very low altitudes
            const rippleSizeMultiplier = 1.0 + (rippleAltitudeIntensity * 1.5); // Ripples 1-2.5x larger at low altitude
            const rippleSpeedMultiplier = 1.0 + (rippleAltitudeIntensity * 1.2); // Ripples expand 1-2.2x faster at low altitude
            
            // Scale ripple size based on altitude
            waterRipple.scale.set(
              waterRipple.scale.x * rippleSizeMultiplier,
              waterRipple.scale.y * rippleSizeMultiplier
            );
            
            // Adjust ripple expansion speed based on altitude
            waterRipple.expansionRate *= rippleSpeedMultiplier;
            
            // Adjust ripple opacity based on altitude (more opaque at lower altitudes)
            waterRipple.opacity = Math.min(255, waterRipple.opacity * (1.0 + rippleAltitudeIntensity * 0.6));
            
            // Adjust ripple fade time based on altitude (longer fade at lower altitudes)
            waterRipple.fadeTimer = Math.floor(waterRipple.fadeTimer * (1.0 + rippleAltitudeIntensity * 0.4));
          }
          
          if (this._airshipEffectLayers.background && waterRipple) {
            this._airshipEffectLayers.background.addChild(waterRipple);
            waterRippleSprites.push(waterRipple);
          }

          // Add small water "skimming" spray droplets in front of the ripple for more impact
          try {
            if (this._airshipEffectLayers.midground && typeof createPuffSprite === 'function') {
              const sprayCount = 2 + Math.floor(Math.random() * 2);
              for (let i=0;i<sprayCount;i++) {
                const dx = (Math.random() - 0.5) * 6;
                const dy = - (1 + Math.random() * 2);
                const s = createPuffSprite('waterSplash', rippleX + dx, rippleY - 6, 6 + Math.random()*4, 0.55, 18 + Math.floor(Math.random()*10), dx*0.08, dy*0.2, 0.08);
                if (s) {
                  s.alpha *= 0.7;
                  this._airshipEffectLayers.midground.addChild(s);
                }
              }
            }
          } catch(_) {}
        } catch (error) {
          console.error('❌ Error creating water ripple sprite:', error);
        }
        
        // Limit number of ripple sprites - optimized limit
        if (waterRippleSprites.length > 20) {
          const oldRipple = waterRippleSprites.shift();
          if (oldRipple && oldRipple.parent) {
            oldRipple.parent.removeChild(oldRipple);
          }
        }
      }
      } else {
      // Reset ripple timer when not creating ripples
      rippleCreationTimer = 0;
      }
    }
    
    // Stop spawning when over land; allow existing ripples to fade naturally
    if (!isOverWater) {
      rippleCreationTimer = 0;
    }
    
    // Clean up destroyed sprites (in-place)
    (function pruneInPlace(arr){
      let w = 0;
      for (let r = 0; r < arr.length; r++) { const s = arr[r]; if (s && s.parent) arr[w++] = s; }
      arr.length = w;
    })(groundTrailSprites);
    (function pruneInPlace(arr){
      let w = 0;
      for (let r = 0; r < arr.length; r++) { const s = arr[r]; if (s && s.parent && !s._destroyed) arr[w++] = s; }
      arr.length = w;
    })(waterRippleSprites);
    
    // Create wind lines at screen edges when moving - only above certain altitude
    const forwardPressed2 = (typeof ControlManager !== 'undefined' && ControlManager && ControlManager.controlsSwapped === true)
      ? Input.isPressed('shift')
      : Input.isPressed('up');
    if (forwardPressed2 && airship._altitude >= AIRSHIP_CONFIG.EFFECTS.WIND_LINES_ALTITUDE) {
      windLinesCreationTimer++;
      
      // Create wind lines with dynamic timing based on altitude - more frequent at higher altitudes
      const windAltitudeBasedTimer = Math.max(1, Math.floor(4 - ((altitude - AIRSHIP_CONFIG.EFFECTS.WIND_LINES_ALTITUDE) / 20))); // 1-4 frames based on altitude
      if (windLinesCreationTimer >= windAltitudeBasedTimer + Math.floor(Math.random() * 2)) {
        windLinesCreationTimer = 0;
        
        // Create wind lines at screen edges
        const screenWidth = Graphics.width;
        const screenHeight = Graphics.height;
        
        // Create more wind lines per frame (2-4 instead of 3-5)
        const lineCount = 2 + Math.floor(Math.random() * 3);
        
        for (let i = 0; i < lineCount; i++) {
          // Random position at screen edges
          let x, y, direction;
          
          if (Math.random() < 0.25) {
            // Top edge
            x = Math.random() * screenWidth;
            y = 50; // Closer to center
            // Calculate radial direction from center to this point
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          } else if (Math.random() < 0.5) {
            // Right edge
            x = screenWidth - 50; // Closer to center
            y = Math.random() * screenHeight;
            // Calculate radial direction from center to this point
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          } else if (Math.random() < 0.75) {
            // Bottom edge
            x = Math.random() * screenWidth;
            y = screenHeight - 50; // Closer to center
            // Calculate radial direction from center to this point
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          } else {
            // Left edge
            x = 50; // Closer to center
            y = Math.random() * screenHeight;
            // Calculate radial direction from center to this point
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          }
          
          const windLine = new WindLinesSprite(x, y, direction);
          
          // Enhanced altitude-based wind line scaling
          if (windLine) {
            const windAltitudeIntensity = Math.max(0.1, (altitude - AIRSHIP_CONFIG.EFFECTS.WIND_LINES_ALTITUDE) / 80); // More intense at higher altitudes
            const windSizeMultiplier = 1.0 + (windAltitudeIntensity * 0.8); // Wind lines 1-1.8x larger at high altitude
            const windSpeedMultiplier = 1.0 + (windAltitudeIntensity * 2.0); // Wind lines move 1-3x faster at high altitude
            
            // Scale wind line size based on altitude
            windLine.scale.set(
              windLine.scale.x * windSizeMultiplier,
              windLine.scale.y * windSizeMultiplier
            );
            
            // Adjust wind line opacity based on altitude (more opaque at higher altitudes)
            windLine.opacity = Math.min(255, windLine.opacity * (1.0 + windAltitudeIntensity * 0.7));
            
            // Adjust wind line fade time based on altitude (longer fade at higher altitudes)
            windLine.fadeTimer = Math.floor(windLine.fadeTimer * (1.0 + windAltitudeIntensity * 0.3));
          }
          
          this._airshipEffectLayers.midground.addChild(windLine);
          windLinesSprites.push(windLine);
        }
        
        // Add inner wind lines (closer to center, more transparent)
        const innerLineCount = Math.random() < 0.5 ? 1 : 0; // 50% chance for 1 inner line, otherwise 0
        
        for (let i = 0; i < innerLineCount; i++) {
          // Random position closer to center
          let x, y, direction;
          
          if (Math.random() < 0.25) {
            // Top area
            x = Math.random() * screenWidth;
            y = 100; // Even closer to center
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          } else if (Math.random() < 0.5) {
            // Right area
            x = screenWidth - 100; // Even closer to center
            y = Math.random() * screenHeight;
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          } else if (Math.random() < 0.75) {
            // Bottom area
            x = Math.random() * screenWidth;
            y = screenHeight - 100; // Even closer to center
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          } else {
            // Left area
            x = 100; // Even closer to center
            y = Math.random() * screenHeight;
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          }
          
          const innerWindLine = new InnerWindLinesSprite(x, y, direction);
          
          // Enhanced altitude-based inner wind line scaling
          if (innerWindLine) {
            const innerWindAltitudeIntensity = Math.max(0.1, (altitude - AIRSHIP_CONFIG.EFFECTS.WIND_LINES_ALTITUDE) / 60); // More intense at higher altitudes
            const innerWindSizeMultiplier = 1.0 + (innerWindAltitudeIntensity * 0.6); // Inner wind lines 1-1.6x larger at high altitude
            const innerWindSpeedMultiplier = 1.0 + (innerWindAltitudeIntensity * 1.8); // Inner wind lines move 1-2.8x faster at high altitude
            
            // Scale inner wind line size based on altitude
            innerWindLine.scale.set(
              innerWindLine.scale.x * innerWindSizeMultiplier,
              innerWindLine.scale.y * innerWindSizeMultiplier
            );
            
            // Adjust inner wind line opacity based on altitude (more opaque at higher altitudes)
            innerWindLine.opacity = Math.min(255, innerWindLine.opacity * (1.0 + innerWindAltitudeIntensity * 0.5));
            
            // Adjust inner wind line fade time based on altitude (longer fade at higher altitudes)
            innerWindLine.fadeTimer = Math.floor(innerWindLine.fadeTimer * (1.0 + innerWindAltitudeIntensity * 0.2));
          }
          
          this._airshipEffectLayers.midground.addChild(innerWindLine);
          windLinesSprites.push(innerWindLine);
        }
        
        // Limit number of wind line sprites (reduced from 25 to 15)
        if (windLinesSprites.length > 15) {
          const oldWindLine = windLinesSprites.shift();
          if (oldWindLine && oldWindLine.parent) {
            oldWindLine.parent.removeChild(oldWindLine);
          }
        }
      }
    } else {
      // Reset wind lines timer when not moving
      windLinesCreationTimer = 0;
    }
    
    // Clean up wind lines sprites (in-place)
    (function pruneInPlace(arr){ let w = 0; for (let r = 0; r < arr.length; r++) { const s = arr[r]; if (s && s.parent) arr[w++] = s; } arr.length = w; })(windLinesSprites);
  };
  
  Scene_Map.prototype.updateCloudFlying = function(density = 1.0) {
    // Performance check: skip if not in airship
    if (!$gamePlayer || !$gamePlayer.isInAirship()) return;
    
    const airship = $gameMap.airship();
    if (!airship || !this._airshipEffectLayers) return;
    
    // Null safety checks
    if (!airship._altitude || typeof airship._altitude !== 'number') return;
    
    const altitude = airship._altitude || 0;
    const isMoving = isAirshipLateralMoving === true;
    
    // Create cloud flying effect at high altitude
    if (altitude >= AIRSHIP_CONFIG.EFFECTS.CLOUD_FLYING_ALTITUDE && isMoving) {
      cloudCreationTimer++;
      
      // Create clouds with dynamic timing based on altitude - more frequent at higher altitudes
      const cloudAltitudeBasedTimer = Math.max(1, Math.floor(5 - ((altitude - AIRSHIP_CONFIG.EFFECTS.CLOUD_FLYING_ALTITUDE) / 30))); // 1-5 frames based on altitude
      if (cloudCreationTimer >= cloudAltitudeBasedTimer + Math.floor(Math.random() * 3)) {
        cloudCreationTimer = 0;
        
        const screenWidth = Graphics.width;
        const screenHeight = Graphics.height;
        const centerX = screenWidth / 2;
        const centerY = screenHeight / 2;
        
        // Enhanced cloud density based on altitude
        const altitudeFactor = Math.min((altitude - AIRSHIP_CONFIG.EFFECTS.CLOUD_FLYING_ALTITUDE) / 50, 2.0); // Reduced from 2.5 to 2.0
        const baseCloudCount = Math.floor((1 + Math.floor(Math.random() * 2)) * altitudeFactor * EffectSystems.cloudFlying.config.densityMultiplier); // Reduced from 1-4 to 1-3
        const cloudCount = Math.floor(baseCloudCount * density);
        
        for (let i = 0; i < cloudCount; i++) {
          // Spawn clouds from horizon with radial flow pattern
          const screenWidth = Graphics.width;
          const screenHeight = Graphics.height;
          const horizonY = screenHeight * 0.5// Horizon at 70% down the screen
          
          // Spawn clouds along the horizon line
          const spawnX = Math.random() * screenWidth; // Random X along horizon
          const spawnY = horizonY + (Math.random() - 0.5) * 40; // Slight variation in Y
          
          // Calculate destination based on spawn position
          // Left side clouds flow down and left, right side flow down and right
          const centerX = screenWidth / 2;
          const destX = centerX + (spawnX - centerX) * 3 // Outward flow in same direction
          const destY = screenHeight + 50; // Flow down to bottom
          
          // Calculate direction from spawn to destination
          const direction = Math.atan2(destY - spawnY, destX - spawnX);
          
          // Random cloud size and opacity (increased opacity and scale)
          const size = 40 + Math.random() * 60; // 40-100px clouds (increased from 30-70)
          const opacity = 150 + Math.random() * 105; // 150-255 opacity (increased from 100-200)
          
          // Enhanced cloud layering system
          const screenHalfway = Graphics.height / 2;
          const isBackgroundCloud = spawnY < screenHalfway || Math.random() < 0.3; // 30% chance for background
          
          if (isBackgroundCloud) {
            // Background clouds - identical to foreground except slower and more transparent
            const bgSpeedMultiplier = 0.3; // Slower than foreground
            const bgOpacity = opacity * 0.7; // Increased from 0.5 to 0.7 for better visibility
            
            const bgCloud = new CloudSprite(spawnX, spawnY, size, bgOpacity, direction, bgSpeedMultiplier);
            this._airshipEffectLayers.background.addChild(bgCloud);
            backgroundCloudSprites.push(bgCloud);
          } else {
            // Foreground clouds - faster and more opaque
            const fgSpeedMultiplier = spawnY < screenHalfway ? 0.35 : 1.3;
            const fgOpacity = opacity * 1.6; // Increased from 1.4 to 1.6 for more opacity
            
            const fgCloud = new CloudSprite(spawnX, spawnY, size, fgOpacity, direction, fgSpeedMultiplier);
            this._airshipEffectLayers.foreground.addChild(fgCloud);
            foregroundCloudSprites.push(fgCloud);
          }
        }
        
        // Limit number of cloud sprites for each layer (reduced from 20 to 15)
        if (backgroundCloudSprites.length > 15) {
          const oldCloud = backgroundCloudSprites.shift();
          if (oldCloud && oldCloud.parent) {
            oldCloud.parent.removeChild(oldCloud);
          }
        }
        
        if (foregroundCloudSprites.length > 15) {
          const oldCloud = foregroundCloudSprites.shift();
          if (oldCloud && oldCloud.parent) {
            oldCloud.parent.removeChild(oldCloud);
          }
        }
      }
    } else {
      // Reset timer when not creating clouds
      cloudCreationTimer = 0;
    }
    
    // Clean up destroyed cloud sprites for both layers (in-place)
    (function pruneInPlace(arr){ let w = 0; for (let r = 0; r < arr.length; r++) { const s = arr[r]; if (s && s.parent) arr[w++] = s; } arr.length = w; })(backgroundCloudSprites);
    (function pruneInPlace(arr){ let w = 0; for (let r = 0; r < arr.length; r++) { const s = arr[r]; if (s && s.parent) arr[w++] = s; } arr.length = w; })(foregroundCloudSprites);
  };

  // Helper to create a small circular puff sprite that self-fades and returns its bitmap to the pool
  function createPuffSprite(poolKey, x, y, diameter, alpha, lifeFrames, vx = 0, vy = 0, shrinkPerFrame = 0.0) {
    const size = Math.max(2, Math.floor(diameter));
    const bm = BitmapPool.getBitmap(poolKey, size, size);
    const ctx = bm.context; bm.clear();
    ctx.fillStyle = `rgba(255,255,255,${Math.max(0.05, Math.min(0.4, alpha))})`;
    ctx.beginPath(); ctx.arc(size/2, size/2, size/2, 0, Math.PI*2); ctx.fill();
    // Disable legacy prototype puff rendering for contrails unless explicitly requested
    if (poolKey === 'contrail') {
      return null;
    }
    const spr = new Sprite(bm);
    spr.anchor.set(0.5, 0.5);
    spr.x = x; spr.y = y; spr.alpha = 1.0;
    spr.vx = vx; spr.vy = vy; spr._shrink = shrinkPerFrame;
    spr._life = Math.max(1, lifeFrames);
    spr._fade = 1.0 / spr._life;
    const oldUpdate = spr.update;
    spr.update = function(){
      if (oldUpdate) oldUpdate.call(this);
      this.x += this.vx; this.y += this.vy;
      if (this._shrink !== 0) { const f = Math.max(0.90, 1.0 - this._shrink); this.scale.set(this.scale.x * f, this.scale.y * f); }
      this.alpha -= this._fade;
      if (this.alpha <= 0) { if (this.parent) this.parent.removeChild(this); BitmapPool.returnBitmap(poolKey, this.bitmap); }
    };
    return spr;
  }

  // Ribbon-style contrails using PIXI.Graphics with per-segment alpha/width falloff
  function ContrailRibbon(width, maxPoints, baseAlpha, sideSign) {
    this.graphics = new PIXI.Graphics();
    // Dedicated soft halo layer (behind core) for a volumetric look
    this.halo = new PIXI.Graphics();
    try { this.halo.blendMode = PIXI.BLEND_MODES.SCREEN; } catch(_) {}
    try { if (PIXI.filters && PIXI.filters.BlurFilter) { this.halo.filters = [new PIXI.filters.BlurFilter(1.2)]; } } catch(_) {}
    this.points = [];
    this.width = Math.max(1, width || 3);
    this.maxPoints = Math.max(4, maxPoints || 32);
    this.baseAlpha = Math.max(0, Math.min(1, baseAlpha == null ? 0.6 : baseAlpha));
    // Chaotic behavior controls
    this._jitterMag = 0.04;      // per-point micro jitter magnitude (tamer)
    this._gustStrength = 0;      // current lateral gust strength
    this._gustVel = 0;           // direction/speed of current gust
    this._gustCooldown = 0;      // frames until next possible gust
    this._gustChance = 0.001;    // probability per frame to start a gust (rarer)
    this._gapNoiseSeed = Math.random() * 1000; // unique seed for gap pattern
    this._noiseOffset = 0;        // internal phase for noise
    // Visual noise controls
    this._opacityNoiseStrength = 0.0; // 0 = disabled, 1 = full opacity noise
    this._widthNoiseStrength = 0.15;  // subtle width noise
    this._gapProbability = 0.0;       // 0 disables stochastic core gaps
    // Segmenting to prevent reconnection after stops
    this._segmentId = 0;
    // Segment fade controls (older segments are dimmer)
    this._segmentFadeMax = 4;     // segments over which to fade from 1.0 -> min
    this._segmentMinAlpha = 0.4;  // minimum alpha factor for very old segments
    // Turning influence (scene drives _turnInput each frame)
    this._turnPush = 0;
    this._turnInput = 0;
    // Natural fade controls
    this._baseLife = 220;        // average point life in frames (altitude-adjusted externally)
    this._ageVariance = 0.25;    // +/- 25% life variance among points
    this._newestAgeBias = 0.8;   // newest points age faster (multiplier at tail), but softer
    this._minSpacing = 0.6; // denser points for smoother curves
    this._needsRedraw = false;
    this._friction = 0.995; // very light decay so motion persists
    this._gravity = 0.20;   // pull downwards each frame (screen space)
    this.sign = Math.sign(sideSign || 0); // -1 left, +1 right
    this._spread = 0.50;    // outward acceleration per step scaled by Y (doubled)
    this._alphaYBoost = 0.6; // extra alpha near bottom
    this._widthYBoost = 0.6; // extra width near bottom
    this._boostWidth = 1.0;  // dynamic width multiplier (set during boost)
    // Orbiting offset controls (used by ghost ribbons)
    this._orbitAmp = 0;      // velocity offset amplitude in px/frame
    this._orbitFreq = 0.35;  // radians per segment index
    this._orbitSpeed = 0.07; // radians per frame
    this._orbitPhase = Math.random() * Math.PI * 2; // evolving phase
    // Flowing breakup controls (animated along ribbon length)
    this._flowPhase = Math.random() * Math.PI * 2;
    this._flowSpeed = 0.030;   // radians per frame (slower)
    this._flowFreq = 5.0;      // cycles across ribbon (lower frequency)
    // Turn-memory and bank-coupled spread
    this._turnInput = 0;       // external input -1..+1
    this._turnMemory = 0;      // smoothed state that lags behind input
    this._turnLagK = 0.10;     // how quickly memory follows input (slightly more lag)
    this._turnDecay = 0.96;    // decay when no input for delayed relaxation
    this._spreadBase = this._spread; // keep base for dynamic calc
    this._spreadGain = 0.35;   // extra spread at high bank/turn (tamer)
    // Pair convergence near emission and gentle lateral drift
    this._convergeStrength = 0.15; // inward pull near the ship (tamer)
    this._pairDriftMag = 0.0;  // constant lateral drift for the whole ribbon
    this._pairDriftSpeed = 0.01; // drift phase speed
    this._pairDriftPhase = Math.random() * Math.PI * 2;
    // Environment multipliers (altitude/humidity)
    this._envAlphaMult = 1.0;
    this._envWidthMult = 1.0;
    // Style caches for fewer state changes
    this._lastCoreWidth = null; this._lastCoreAlpha = null; this._lastCoreColor = null;
    this._lastHaloWidth = null; this._lastHaloAlpha = null; this._lastHaloColor = null;
  }
  ContrailRibbon.prototype.addPoint = function(x, y, vx, vy) {
    const n = this.points.length;
    if (n > 0) {
      const last = this.points[n-1];
      const dx = x - last.x, dy = y - last.y;
      if ((dx*dx + dy*dy) < (this._minSpacing*this._minSpacing)) return;
    }
    const lifeJitter = (1 - this._ageVariance) + Math.random() * (this._ageVariance * 2);
    const pointLife = Math.max(30, Math.floor(this._baseLife * lifeJitter));
    this.points.push({ x, y, vx: vx||0, vy: vy||0, age: 0, life: pointLife, segment: this._segmentId });
    if (this.points.length > this.maxPoints) this.points.shift();
    this._needsRedraw = true;
  };
  ContrailRibbon.prototype.startNewSegment = function() {
    this._segmentId = (this._segmentId + 1) | 0;
  };
  ContrailRibbon.prototype.step = function() {
    if (this.points.length === 0) return;
    const len = this.points.length;
    // cache time sources once per step to avoid repeated Date.now() calls
    const frame = (Graphics && typeof Graphics.frameCount === 'number') ? Graphics.frameCount : 0;
    const nowTs = (Graphics && Graphics.frameCount != null) ? Graphics.frameCount * 16 : Date.now();
    // advance animated phases
    this._flowPhase += this._flowSpeed;
    // update turn memory once per step for this ribbon
    const input = (this._turnInput||0);
    this._turnMemory = this._turnMemory * this._turnDecay + (input - this._turnMemory) * this._turnLagK;
    // advance slow pair drift phase
    this._pairDriftPhase += this._pairDriftSpeed;
    for (let i=0;i<len;i++) {
      const p = this.points[i];
      // newest-first aging: accelerate age near the tail (newest)
      const r = len > 1 ? (i / (len - 1)) : 0; // 0 oldest .. 1 newest
      const r2 = r * r; // ease-in so only very tail accelerates notably
      const ageInc = 1 + this._newestAgeBias * r2;
      p.age = (p.age||0) + ageInc;
      // add slow wind meander (screen-space x drift)
      const t = nowTs + i*97;
      const wind = (this._windMag||0) * (fastSin(t*0.001)*0.5 + fastSin(t*0.000223+2.1)*0.3 + fastSin(t*0.000071+0.7)*0.2);
      p.vx += wind;
      // micro jitter to add chaotic turbulence (coherent, not pure random)
      const jx = this._jitterMag * (fastSin(frame*0.12 + i*0.9) + 0.5*fastSin(frame*0.07 + i*1.7));
      const jy = this._jitterMag * 0.3 * fastSin(frame*0.09 + i*0.5);
      p.vx += jx;
      p.vy += jy;
      // occasional lateral gusts that push the whole ribbon
      if (this._gustCooldown <= 0 && Math.random() < this._gustChance * (1 + (this._windMag||0)*4)) {
        this._gustStrength = 1.0 + Math.random()*0.7;
        this._gustVel = (Math.random() < 0.5 ? -1 : 1) * (0.4 + Math.random()*0.6);
        this._gustCooldown = 90 + Math.floor(Math.random()*90);
      }
      this._gustCooldown = Math.max(0, this._gustCooldown - 1);
      if (this._gustStrength > 0) {
        const gustFalloff = this._gustStrength;
        p.vx += this._gustVel * gustFalloff * (i / Math.max(1, this.points.length));
      }
      // outward spread increases with distance down the screen; add bank-coupled gain
      const k = Math.max(0, Math.min(1, Graphics && Graphics.height ? (p.y / Graphics.height) : 0));
      // base outward spread with dynamic gain from turn memory (bank)
      const spreadNow = this._spreadBase + Math.abs(this._turnMemory) * this._spreadGain;
      p.vx += this.sign * spreadNow * k;
      // gentle convergence near emission (i ~ len-1 newest)
      const ageT = i/Math.max(1,len-1); // 0 oldest .. 1 newest
      const converge = this._convergeStrength * (0.6 + 0.4*k) * (1 - ageT); // strongest on newest, fades with depth
      p.vx -= this.sign * converge;
      // global lateral drift so pair slowly sways together
      if (this._pairDriftMag !== 0) {
        const sway = this._pairDriftMag * Math.sin(this._pairDriftPhase);
        p.vx += sway * 0.02;
      }
      // turning influence: curve both ribbons together in the turn direction
      // _turnInput: -1 left, +1 right, 0 none.
      const turnCurve = this._turnMemory * 0.5; // use memory for delayed relaxation
      p.vx += turnCurve * (1 - i/Math.max(1,len-1)); // stronger on older segments
      // orbiting offset: make ghost ribbons oscillate around main trajectory
      if (this._orbitAmp > 0) {
        this._orbitPhase += this._orbitSpeed;
        const orbit = this._orbitAmp * fastSin(this._orbitPhase + i * this._orbitFreq);
        p.vx += orbit * 0.05; // small lateral velocity perturbation
        p.vy += orbit * 0.02; // slight vertical variance
      }
      p.x += p.vx; p.y += p.vy;
      p.vx *= this._friction; p.vy = (p.vy + this._gravity) * this._friction;
    }
    // decay gust over time
    this._gustStrength *= 0.94;
    if (this._gustStrength < 0.05) this._gustStrength = 0;
    // trim naturally expired points from the tail (newest first)
    while (this.points.length) {
      const tail = this.points[this.points.length - 1];
      if ((tail.age||0) > ((tail.life||this._baseLife) + 40)) this.points.pop(); else break;
    }
    this._needsRedraw = true;
  };
  ContrailRibbon.prototype.fadeStep = function(mult = 1) {
    if (this.points.length > 0) {
      const removeCount = Math.min(this.points.length, Math.max(1, Math.floor(mult * 0.5)));
      const start = Math.max(0, this.points.length - removeCount);
      this.points.splice(start, removeCount); // remove from tail so newest fade first
      this._needsRedraw = true;
    }
  };
  ContrailRibbon.prototype.clear = function() {
    this.points.length = 0; this.graphics.clear(); this._needsRedraw = false;
  };
  ContrailRibbon.prototype.redraw = function() {
    if (!this._needsRedraw) return;
    const gc = this.graphics; const gh = this.halo;
    gc.clear(); if (gh && gh.clear) gh.clear();
    // Derive contrail color from boost gauge gradient only when boost is active
    const desiredColor = (typeof boostActive !== 'undefined' && boostActive) ? getCurrentBoostColorHex() : 0xffffff;
    const setCoreStyle = (width, alpha) => {
      if (this._lastCoreWidth !== width || this._lastCoreAlpha !== alpha || this._lastCoreColor !== desiredColor) {
        gc.lineStyle(width, desiredColor, alpha, 0.5);
        this._lastCoreWidth = width;
        this._lastCoreAlpha = alpha;
        this._lastCoreColor = desiredColor;
      }
    };
    const setHaloStyle = (width, alpha) => {
      if (!gh) return;
      if (this._lastHaloWidth !== width || this._lastHaloAlpha !== alpha || this._lastHaloColor !== desiredColor) {
        gh.lineStyle(width, desiredColor, alpha, 0.5);
        this._lastHaloWidth = width;
        this._lastHaloAlpha = alpha;
        this._lastHaloColor = desiredColor;
      }
    };
    const pts = this.points; const n = pts.length; if (n < 2) return;
    // Use quadratic curves for smoother appearance
    // Precompute noise phase once per pass (was per segment)
    this._noiseOffset = (this._noiseOffset + 1) & 63;
    const noisePhase1 = this._noiseOffset;
    // Pass 1: soft halo for volume (drawn on halo graphics)
    if (!this._skipHalo && gh) {
      let p0 = pts[0];
      gh.moveTo(p0.x, p0.y);
      for (let i = 1; i < n - 1; i++) {
        const p1 = pts[i];
        const p2 = pts[i+1];
        const midX = (p1.x + p2.x) * 0.5; const midY = (p1.y + p2.y) * 0.5;
        // break path across segments to avoid visual reconnection
        if (p1.segment !== p0.segment) { gh.moveTo(p1.x, p1.y); p0 = p1; continue; }
        const t = i / (n - 2);
        const yAvg = (p0.y + p1.y) * 0.5;
        const yk = Math.max(0, Math.min(1, Graphics && Graphics.height ? (yAvg / Graphics.height) : 0));
        // age-based fade factor (smoothstep on normalized age)
        const age0 = p0.age||0, life0 = p0.life||this._baseLife;
        const age1 = p1.age||0, life1 = p1.life||this._baseLife;
        const ageNorm = Math.min(1, ((age0/life0) + (age1/life1)) * 0.5);
        const inv = 1 - ageNorm; const fadeAge = inv*inv*(3 - 2*inv);
        // segment-based fade: older segments are dimmer
        const dSeg = Math.max(0, this._segmentId - (p1.segment||0));
        const tSeg = Math.min(1, dSeg / Math.max(1, this._segmentFadeMax));
        const invSeg = 1 - tSeg;
        const fadeSeg = this._segmentMinAlpha + (1 - this._segmentMinAlpha) * (invSeg*invSeg*(3 - 2*invSeg));
        const noiseFactor = 0.5 + 0.5*Math.sin((i*0.3 + noisePhase1) * 0.7);
        const flow = 0.5 + 0.5*Math.sin(this._flowPhase + t * this._flowFreq * Math.PI*2);
        const alphaNoiseScale = (1 - this._opacityNoiseStrength) + this._opacityNoiseStrength * (0.65 + 0.35*flow);
        const widthNoiseScale = (1 - this._widthNoiseStrength) + this._widthNoiseStrength * (0.86 + 0.14*flow);
        // simple back/forward lighting: slightly brighter toward horizon (small yk)
        const light = 0.9 + 0.2 * (1 - yk);
        const alpha = Math.min(1, light * this._envAlphaMult * 0.5 * this.baseAlpha * (1 + this._alphaYBoost * yk) * alphaNoiseScale * fadeAge * fadeSeg);
        const w = this._envWidthMult * this.width * (0.6 + 0.4*fadeAge) * (1 - 0.35 * t) * (1 + this._widthYBoost * yk) * this._boostWidth * widthNoiseScale * 1.8;
        setHaloStyle(w, alpha * 0.45);
        gh.quadraticCurveTo(p1.x, p1.y, midX, midY);
        p0 = p1;
      }
      const last = pts[n-1];
      const ykLast = Math.max(0, Math.min(1, Graphics && Graphics.height ? ((last.y + p0.y) * 0.5 / Graphics.height) : 0));
      const age0 = p0.age||0, life0 = p0.life||this._baseLife;
      const age1 = last.age||0, life1 = last.life||this._baseLife;
      const ageNorm = Math.min(1, ((age0/life0) + (age1/life1)) * 0.5);
      const inv = 1 - ageNorm; const fadeAge = inv*inv*(3 - 2*inv);
      const dSegLast = Math.max(0, this._segmentId - (last.segment||0));
      const tSegLast = Math.min(1, dSegLast / Math.max(1, this._segmentFadeMax));
      const invSegLast = 1 - tSegLast;
      const fadeSegLast = this._segmentMinAlpha + (1 - this._segmentMinAlpha) * (invSegLast*invSegLast*(3 - 2*invSegLast));
      const alphaLast = Math.min(1, 0.5 * this.baseAlpha * (1 + this._alphaYBoost * ykLast) * 0.5 * fadeAge * fadeSegLast);
      const wLast = this.width * (0.6 + 0.4*fadeAge) * (1 + this._widthYBoost * ykLast) * this._boostWidth * 1.8;
      setHaloStyle(wLast, alphaLast * 0.45);
      if (last.segment !== p0.segment) { gh.moveTo(last.x, last.y); } else { gh.lineTo(last.x, last.y); }
    }
    // Pass 2: bright core with occasional gaps
    {
      let p0 = pts[0];
      gc.moveTo(p0.x, p0.y);
      const frame = (Graphics && typeof Graphics.frameCount === 'number') ? Graphics.frameCount : 0;
      const noisePhase2 = (noisePhase1 + 17) & 63;
      for (let i = 1; i < n - 1; i++) {
        const p1 = pts[i];
        const p2 = pts[i+1];
        const midX = (p1.x + p2.x) * 0.5; const midY = (p1.y + p2.y) * 0.5;
        // break path across segments to avoid visual reconnection
        if (p1.segment !== p0.segment) { gc.moveTo(p1.x, p1.y); p0 = p1; continue; }
        const t = i / (n - 2);
        const yAvg = (p0.y + p1.y) * 0.5;
        const yk = Math.max(0, Math.min(1, Graphics && Graphics.height ? (yAvg / Graphics.height) : 0));
        // small stochastic gaps along the core to add chaotic breakup
        const gapNoise = 0.5 + 0.5*Math.sin(i*0.6 + frame*0.25 + this._gapNoiseSeed);
        const makeGap = (this._gapProbability > 0) && (gapNoise < this._gapProbability);
        const noiseFactor = 0.5 + 0.5*Math.sin((i*0.3 + noisePhase2) * 0.7);
        const flow2 = 0.5 + 0.5*Math.sin(this._flowPhase + t * this._flowFreq * Math.PI*2 + Math.PI*0.25);
        const age0 = p0.age||0, life0 = p0.life||this._baseLife;
        const age1 = p1.age||0, life1 = p1.life||this._baseLife;
        const ageNorm = Math.min(1, ((age0/life0) + (age1/life1)) * 0.5);
        const inv = 1 - ageNorm; const fadeAge = inv*inv*(3 - 2*inv);
        // segment-based fade: older segments are dimmer
        const dSeg = Math.max(0, this._segmentId - (p1.segment||0));
        const tSeg = Math.min(1, dSeg / Math.max(1, this._segmentFadeMax));
        const invSeg = 1 - tSeg;
        const fadeSeg = this._segmentMinAlpha + (1 - this._segmentMinAlpha) * (invSeg*invSeg*(3 - 2*invSeg));
        const alphaNoiseScale = (1 - this._opacityNoiseStrength) + this._opacityNoiseStrength * (0.65 + 0.35*flow2);
        const widthNoiseScale = (1 - this._widthNoiseStrength) + this._widthNoiseStrength * (0.86 + 0.14*flow2);
        const light2 = 0.9 + 0.2 * (1 - yk);
        const alpha = Math.min(1, light2 * this._envAlphaMult * 0.5 * this.baseAlpha * (1 + this._alphaYBoost * yk) * alphaNoiseScale * fadeAge * fadeSeg);
        const w = this._envWidthMult * this.width * (0.6 + 0.4*fadeAge) * (1 - 0.35 * t) * (1 + this._widthYBoost * yk) * this._boostWidth * widthNoiseScale;
        if (makeGap) {
          gc.moveTo(midX, midY);
        } else {
          setCoreStyle(w, alpha);
          gc.quadraticCurveTo(p1.x, p1.y, midX, midY);
        }
        p0 = p1;
      }
      const last = pts[n-1];
      const ykLast = Math.max(0, Math.min(1, Graphics && Graphics.height ? ((last.y + p0.y) * 0.5 / Graphics.height) : 0));
      const age0 = p0.age||0, life0 = p0.life||this._baseLife;
      const age1 = last.age||0, life1 = last.life||this._baseLife;
      const ageNorm = Math.min(1, ((age0/life0) + (age1/life1)) * 0.5);
      const inv = 1 - ageNorm; const fadeAge = inv*inv*(3 - 2*inv);
      const dSegLast2 = Math.max(0, this._segmentId - (last.segment||0));
      const tSegLast2 = Math.min(1, dSegLast2 / Math.max(1, this._segmentFadeMax));
      const invSegLast2 = 1 - tSegLast2;
      const fadeSegLast2 = this._segmentMinAlpha + (1 - this._segmentMinAlpha) * (invSegLast2*invSegLast2*(3 - 2*invSegLast2));
      const alphaLast = Math.min(1, 0.5 * this.baseAlpha * (1 + this._alphaYBoost * ykLast) * 0.5 * fadeAge * fadeSegLast2);
      const wLast = this.width * (0.6 + 0.4*fadeAge) * (1 + this._widthYBoost * ykLast) * this._boostWidth;
      setCoreStyle(wLast, alphaLast);
      if (last.segment !== p0.segment) { gc.moveTo(last.x, last.y); } else { gc.lineTo(last.x, last.y); }
    }
    this._needsRedraw = false;
  };

  // High-altitude contrails (paired puffs behind the ship)
  Scene_Map.prototype.updateContrails = function(density = 1.0) {
    if (!$gamePlayer || !$gamePlayer.isInAirship()) return;
    const airship = $gameMap && $gameMap.airship ? $gameMap.airship() : null;
    if (!airship || !this._airshipEffectLayers) return;
    const altitude = airship._altitude || 0;
    const cfg = EffectSystems.contrail && EffectSystems.contrail.config ? EffectSystems.contrail.config : { altitudeThreshold: 40, baseInterval: 3 };
    if (altitude < cfg.altitudeThreshold && !boostActive) {
      contrailCreationTimer = 0;
      // Fast fade and clear ribbons when below threshold
      if (contrailRibbonL) {
        for (let i=0;i<3;i++) contrailRibbonL.fadeStep();
        contrailRibbonL.redraw();
        if (!contrailRibbonL.points.length) {
          if (contrailRibbonL.halo && contrailRibbonL.halo.parent) contrailRibbonL.halo.parent.removeChild(contrailRibbonL.halo);
          if (contrailRibbonL.graphics && contrailRibbonL.graphics.parent) contrailRibbonL.graphics.parent.removeChild(contrailRibbonL.graphics);
          contrailRibbonL = null;
        }
      }
      if (contrailRibbonR) {
        for (let i=0;i<3;i++) contrailRibbonR.fadeStep();
        contrailRibbonR.redraw();
        if (!contrailRibbonR.points.length) {
          if (contrailRibbonR.halo && contrailRibbonR.halo.parent) contrailRibbonR.halo.parent.removeChild(contrailRibbonR.halo);
          if (contrailRibbonR.graphics && contrailRibbonR.graphics.parent) contrailRibbonR.graphics.parent.removeChild(contrailRibbonR.graphics);
          contrailRibbonR = null;
        }
      }
      if (contrailGhostL) {
        for (let i=0;i<3;i++) contrailGhostL.fadeStep(2);
        contrailGhostL.redraw();
        if (!contrailGhostL.points.length) { if (contrailGhostL.graphics.parent) contrailGhostL.graphics.parent.removeChild(contrailGhostL.graphics); contrailGhostL = null; }
      }
      if (contrailGhostR) {
        for (let i=0;i<3;i++) contrailGhostR.fadeStep(2);
        contrailGhostR.redraw();
        if (!contrailGhostR.points.length) { if (contrailGhostR.graphics.parent) contrailGhostR.graphics.parent.removeChild(contrailGhostR.graphics); contrailGhostR = null; }
      }
      return;
    }
    // Setup ribbons once
    if (!contrailRibbonL) {
      contrailRibbonL = new ContrailRibbon(3, 72, 0.3, -1);
      // ensure halo behind core
      if (contrailRibbonL.halo) { this._airshipEffectLayers.background.addChild(contrailRibbonL.halo); contrailRibbonL.halo.z = 0; contrailRibbonL.halo.zIndex = 0; }
      this._airshipEffectLayers.background.addChild(contrailRibbonL.graphics); contrailRibbonL.graphics.z = 0; contrailRibbonL.graphics.zIndex = 1;
    }
    if (!contrailRibbonR) {
      contrailRibbonR = new ContrailRibbon(3, 72, 0.3, +1);
      if (contrailRibbonR.halo) { this._airshipEffectLayers.background.addChild(contrailRibbonR.halo); contrailRibbonR.halo.z = 0; contrailRibbonR.halo.zIndex = 0; }
      this._airshipEffectLayers.background.addChild(contrailRibbonR.graphics); contrailRibbonR.graphics.z = 0; contrailRibbonR.graphics.zIndex = 1;
    }
    // Ensure proper draw ordering by zIndex
    if (this._airshipEffectLayers.background && this._airshipEffectLayers.background.sortableChildren !== true) {
      this._airshipEffectLayers.background.sortableChildren = true;
    }
    // Remove ghost ribbons: clean up if they exist
    if (contrailGhostL) { if (contrailGhostL.graphics && contrailGhostL.graphics.parent) contrailGhostL.graphics.parent.removeChild(contrailGhostL.graphics); contrailGhostL = null; }
    if (contrailGhostR) { if (contrailGhostR.graphics && contrailGhostR.graphics.parent) contrailGhostR.graphics.parent.removeChild(contrailGhostR.graphics); contrailGhostR = null; }
    // Add new points at a fixed cadence
    contrailCreationTimer++;
    const stepEvery = Math.max(1, Math.floor(2 / Math.max(0.5, density)));
    // Skip emission/redraw work when HUD is on or performance is low
      const lowFps = (Graphics.frameRate && Graphics.frameRate < 50);
    if (lowFps && ((Graphics.frameCount||0) % Math.max(1, PERF_PARAM_DECIMATE) !== 0)) {
      // decimate spawns under low FPS
    }
    if (contrailCreationTimer >= stepEvery) {
      if (lowFps && ((Graphics.frameCount||0) % 2 === 1)) { /* skip spawn this frame */ }
      else { contrailCreationTimer = 0;
      // Always emit from the on-screen airship center; fixed lateral offset; ignore yaw
      const sx = airship.screenX(); const sy = airship.screenY();
      const backX = sx; const backY = sy - 10; // spawn a bit higher above the ship
      const minAlt = AIRSHIP_CONFIG.ALTITUDE.MIN; const maxAlt = AIRSHIP_CONFIG.ALTITUDE.MAX;
      const altRatio = Math.max(0, Math.min(1, (altitude - minAlt) / Math.max(1, (maxAlt - minAlt))));
      // widen wingtip separation with altitude: 1.0x at min alt → 2.0x at max alt
      const baseSide = 10;
      const side = baseSide * (1 + 1.0 * altRatio);
      let leftX = backX - side; let leftY = backY;
      let rightX = backX + side; let rightY = backY;
      // tiny symmetric emission jitter for organic feel
      const emitJit = (Math.random() - 0.5) * 0.6;
      leftX += emitJit; rightX -= emitJit;
      const windMag = 0.02 + 0.08 * altRatio; // more wind higher up
      const curWind = (this._contrailWindMagCache != null) ? this._contrailWindMagCache : windMag;
      const newWind = curWind + (windMag - curWind) * 0.2; // EMA to reduce jitter
      this._contrailWindMagCache = newWind;
      contrailRibbonL._windMag = newWind; contrailRibbonR._windMag = newWind;
      const vBack = 1.6 + (boostActive ? 0.6 : 0);
      // Increase width while boosting
      const boostWidth = boostActive ? 1.6 : 1.0;
      contrailRibbonL._boostWidth = boostWidth;
      contrailRibbonR._boostWidth = boostWidth;
      if (contrailGhostL) contrailGhostL._boostWidth = Math.max(1.0, boostWidth*0.85);
      if (contrailGhostR) contrailGhostR._boostWidth = Math.max(1.0, boostWidth*0.85);
      // Camera zoom-on-boost: small extra FOV and slight distance reduction
      if (unlandableLandingSuppressFrames === 0) {
        try {
          const baseFov = CameraCalculator.calculateDynamicFov(altitude);
          const baseDist = CameraCalculator.calculateDynamicCameraDistance(altitude);
          const targetFov = baseFov + (boostActive ? BOOST_ZOOM_FOV_DELTA : 0);
          const targetDist = baseDist * (boostActive ? BOOST_ZOOM_DIST_MULT : 1.0);
          const eps = 0.05;
          if (typeof UltraMode7.getFov === 'function' && Math.abs(UltraMode7.getFov() - targetFov) > eps) UltraMode7.animateFov(targetFov, BOOST_ZOOM_SMOOTH_FRAMES);
          if (typeof UltraMode7.getCameraDistance === 'function' && Math.abs(UltraMode7.getCameraDistance() - targetDist) > eps) UltraMode7.animateCameraDistance(targetDist, BOOST_ZOOM_SMOOTH_FRAMES);
        } catch(_) {}
      }
      const vx = 0; const vy = 10 * vBack; // vertical pull only
      const curlBase = 2.2 * (1 + 1.5 * altRatio); // stronger outward separation at higher altitude
      // seed initial lateral velocity with turning to kick visible bend early
      const yawNow = (typeof $gameTemp !== 'undefined' && $gameTemp && typeof $gameTemp._yawFacing === 'number') ? $gameTemp._yawFacing : (UltraMode7 && UltraMode7.getYaw ? UltraMode7.getYaw() : 0);
      this._ctrPrevYawEmit = this._ctrPrevYawEmit == null ? yawNow : this._ctrPrevYawEmit;
      let de = yawNow - this._ctrPrevYawEmit; while (de > 180) de -= 360; while (de < -180) de += 360;
      const yawVelEmit = de * (Graphics.frameRate || 60) / 40;
      this._ctrPrevYawEmit = yawNow;
      const turnKick = Math.max(-1, Math.min(1, yawVelEmit)) * 1.5;
      contrailRibbonL.addPoint(leftX, leftY, -curlBase + turnKick, vy);
      contrailRibbonR.addPoint(rightX, rightY, +curlBase + turnKick, vy);
      // enable a subtle synchronized drift for the pair based on altitude/wind
      const pairDrift = (newWind - 0.02) * 8; // scale with wind/higher alt
      if (contrailRibbonL) contrailRibbonL._pairDriftMag = pairDrift;
      if (contrailRibbonR) contrailRibbonR._pairDriftMag = pairDrift;
      // environment multipliers: higher altitude → thicker/wider, lower altitude → thinner
      // Humidity/region hook cached per map
      if (this._contrailHumidCache == null || this._contrailHumidCacheMapId !== ($gameMap ? $gameMap.mapId() : 0)) {
        let h = 0.0; try { h = ($dataMap && $dataMap.meta && $dataMap.meta.Humid) ? Math.max(0, Math.min(1, Number($dataMap.meta.Humid))) : 0; } catch(_) {}
        this._contrailHumidCache = h; this._contrailHumidCacheMapId = ($gameMap ? $gameMap.mapId() : 0);
      }
      const humid = this._contrailHumidCache || 0;
      const envAlpha = (0.8 + 0.6 * altRatio) * (1 + 0.3 * humid); // 0.8..1.4 scaled by humidity
      const envWidth = (0.9 + 0.6 * altRatio) * (1 + 0.2 * humid); // 0.9..1.5 scaled by humidity
      if (contrailRibbonL) { contrailRibbonL._envAlphaMult = envAlpha; contrailRibbonL._envWidthMult = envWidth; }
      if (contrailRibbonR) { contrailRibbonR._envAlphaMult = envAlpha; contrailRibbonR._envWidthMult = envWidth; }
      // subtle stochastic breakup along the bright core
      const gapBase = Math.max(0, Math.min(0.06, 0.01 + 0.03 * altRatio + (boostActive ? 0.02 : 0)));
      if (contrailRibbonL) { contrailRibbonL._gapProbability = gapBase; contrailRibbonL._opacityNoiseStrength = 0.15; }
      if (contrailRibbonR) { contrailRibbonR._gapProbability = gapBase; contrailRibbonR._opacityNoiseStrength = 0.15; }
      // ghosts removed
      }
    }
    // Integrate ribbon motion every frame so older points trail backward
    if (contrailRibbonL || contrailRibbonR) {
      // Drive turning input so ribbons curve with current yaw velocity
      const yawNow = (typeof $gameTemp !== 'undefined' && $gameTemp && typeof $gameTemp._yawFacing === 'number') ? $gameTemp._yawFacing : (UltraMode7 && UltraMode7.getYaw ? UltraMode7.getYaw() : 0);
      this._ctrPrevYaw = this._ctrPrevYaw == null ? yawNow : this._ctrPrevYaw;
      let d = yawNow - this._ctrPrevYaw; while (d > 180) d -= 360; while (d < -180) d += 360;
      const yawVel = d * (Graphics.frameRate || 60) / 40; // more sensitive
      const turnInput = Math.max(-1, Math.min(1, yawVel));
      if (contrailRibbonL) contrailRibbonL._turnInput = turnInput;
      if (contrailRibbonR) contrailRibbonR._turnInput = turnInput;
      this._ctrPrevYaw = yawNow;
    }
    // Step both, redraw once after stepping to reduce state churn
    const needRedrawL = !!contrailRibbonL; const needRedrawR = !!contrailRibbonR;
    // Adaptive LOD: reduce work under low FPS
    if (contrailRibbonL) {
      contrailRibbonL._minSpacing = lowFps ? 0.9 : 0.6;
      contrailRibbonL.maxPoints = lowFps ? 48 : 72;
      contrailRibbonL._skipHalo = lowFps && ((Graphics.frameCount||0) % 2 === 1);
      contrailRibbonL.step();
    }
    if (contrailRibbonR) {
      contrailRibbonR._minSpacing = lowFps ? 0.9 : 0.6;
      contrailRibbonR.maxPoints = lowFps ? 48 : 72;
      contrailRibbonR._skipHalo = lowFps && ((Graphics.frameCount||0) % 2 === 1);
      contrailRibbonR.step();
    }
    // ghosts removed
    if (needRedrawL) contrailRibbonL.redraw();
    if (needRedrawR) contrailRibbonR.redraw();
    // ghosts removed
  };

  // Wingtip vortices (brief puffs when turning sharply)
  Scene_Map.prototype.updateVortices = function(density = 1.0) {
    if (!$gamePlayer || !$gamePlayer.isInAirship()) return;
    const airship = $gameMap && $gameMap.airship ? $gameMap.airship() : null;
    if (!airship || !this._airshipEffectLayers) return;
    const yaw = (typeof $gameTemp !== 'undefined' && $gameTemp && typeof $gameTemp._yawFacing === 'number') ? $gameTemp._yawFacing : (UltraMode7 && UltraMode7.getYaw ? UltraMode7.getYaw() : 0);
    if (this._vortexPrevYaw == null) this._vortexPrevYaw = yaw;
    let d = yaw - this._vortexPrevYaw; while (d > 180) d -= 360; while (d < -180) d += 360;
    const turnRateDegPerSec = Math.abs(d) * (Graphics.frameRate || 60);
    this._vortexPrevYaw = yaw;
    const cfg = EffectSystems.vortices && EffectSystems.vortices.config ? EffectSystems.vortices.config : { turnThresholdDegPerSec: 60, baseInterval: 2 };
    if (turnRateDegPerSec < cfg.turnThresholdDegPerSec) { vortexCreationTimer = 0; return; }
    vortexCreationTimer++;
    const spawnEvery = Math.max(1, Math.floor(cfg.baseInterval / Math.max(0.5, density)));
    if (vortexCreationTimer >= spawnEvery) {
      vortexCreationTimer = 0;
      const sx = airship.screenX(); const sy = airship.screenY();
      const dir = (-yaw) * Math.PI / 180;
      const backX = sx - Math.cos(dir) * 20; const backY = sy - Math.sin(dir) * 20;
      const side = 14;
      const leftX = backX + Math.cos(dir + Math.PI/2) * side; const leftY = backY + Math.sin(dir + Math.PI/2) * side;
      const rightX = backX + Math.cos(dir - Math.PI/2) * side; const rightY = backY + Math.sin(dir - Math.PI/2) * side;
      const baseSize = 7 + Math.random() * 2; const life = Math.floor(20 + Math.random()*8);
      const backSpeed = 1.2; const curl = 0.8 * Math.sign(d); // curl outward relative to turn direction
      const backVx = -Math.cos(dir) * backSpeed; const backVy = -Math.sin(dir) * backSpeed;
      // Disable legacy contrail puffs (replaced by ribbon system) - remove references
    }
    // Clean up destroyed vortex sprites (in-place)
    (function pruneInPlace(arr){ let w = 0; for (let r = 0; r < arr.length; r++) { const s = arr[r]; if (s && s.parent) arr[w++] = s; } arr.length = w; })(vortexSprites);
  };

  // Integrate contrail fade into primary update
  Scene_Map.prototype._mauiContrailFade = function() {
    try {
      if (contrailRibbonL && (!isAirshipLateralMoving || !$gamePlayer.isInAirship())) { contrailRibbonL.fadeStep(3); contrailRibbonL.redraw(); this._contrailDriftT = Math.max(0, (this._contrailDriftT||0) - 1.0); }
      if (contrailRibbonR && (!isAirshipLateralMoving || !$gamePlayer.isInAirship())) { contrailRibbonR.fadeStep(3); contrailRibbonR.redraw(); this._contrailDriftT = Math.max(0, (this._contrailDriftT||0) - 1.0); }
    } catch(_) {}
  };
  
  Scene_Map.prototype.updateLowAltitudeEffects = function(density = 1.0) {
    // Performance check: skip if not in airship
    if (!$gamePlayer || !$gamePlayer.isInAirship()) return;
    
    const airship = $gameMap.airship();
    if (!airship || !this._airshipEffectLayers) return;
    
    // Null safety checks
    if (!airship._altitude || typeof airship._altitude !== 'number') return;
    
    const altitude = airship._altitude || 0;
    const isMoving = isAirshipLateralMoving === true;
    const airshipX = Math.floor(airship.x);
    const airshipY = Math.floor(airship.y);
    
    // Check if airship is over water
    const isOverWater = $gameMap.isWaterTile ? $gameMap.isWaterTile(airshipX, airshipY) : 
                       ($gameMap.isBoatPassable(airshipX, airshipY) || $gameMap.isShipPassable(airshipX, airshipY));
    
    // Create dust clouds at medium-low altitude (only on land)
    if (altitude <= AIRSHIP_CONFIG.EFFECTS.DUST_CLOUD_THRESHOLD && isMoving && !isOverWater) {
      // Dynamic dust creation probability based on altitude - more likely at lower altitudes
      const dustCreationProbability = Math.min(0.7, 0.2 + (0.5 * (1.0 - (altitude / AIRSHIP_CONFIG.EFFECTS.DUST_CLOUD_THRESHOLD)))); // 20-70% based on altitude
      if (Math.random() < dustCreationProbability) {
        const x = airship.screenX();
        const y = airship.screenY() + 44; // Same offset as trail
        
        // Determine airship movement direction (same logic as trail)
        let airshipDirection = 0;
        if (lastAirshipX !== 0 && lastAirshipY !== 0) {
          const deltaX = x - lastAirshipX;
          const deltaY = y - lastAirshipY;
          
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            airshipDirection = deltaX > 0 ? 6 : 4; // Right or Left
          } else {
            airshipDirection = deltaY > 0 ? 2 : 8; // Down or Up
          }
        }
        
        // Add more randomness to make it look more chaotic (same as trail)
        const randomX = x + (Math.random() - 0.5) * 12;
        const randomY = y + (Math.random() - 0.5) * 6;
        
        // Create more dust particles at lower altitudes (same logic as trail)
        const baseDustCount = altitude <= 40 ? 8 : (altitude <= 80 ? 5 : (altitude <= 120 ? 3 : 1)); // Optimized scaling
        const dustCount = Math.floor(baseDustCount * density);
        
        // Enhanced altitude-based dust particle scaling
        const altitudeIntensity = Math.max(0.1, 1.0 - (altitude / AIRSHIP_CONFIG.EFFECTS.DUST_CLOUD_THRESHOLD)); // More intense at lower altitudes for dust
        const dustSizeMultiplier = 1.0 + (altitudeIntensity * 1.2); // Dust particles 1-2.2x larger at low altitude
        const dustSpeedMultiplier = 1.0 + (altitudeIntensity * 1.0); // Dust particles move 1-2x faster at low altitude
        
        for (let i = 0; i < dustCount; i++) {
          try {
            const dustCloud = new DustCloudSprite(randomX, randomY, airshipDirection);
            
            // Apply altitude-based scaling to dust particle properties
            if (dustCloud) {
              // Scale dust particle size based on altitude
              dustCloud.scale.set(
                dustCloud.scale.x * dustSizeMultiplier,
                dustCloud.scale.y * dustSizeMultiplier
              );
              
              // Adjust dust particle speed based on altitude
              dustCloud.driftSpeed *= dustSpeedMultiplier;
              dustCloud.horizontalDrift *= dustSpeedMultiplier;
              
              // Adjust dust opacity based on altitude (more opaque at lower altitudes)
              dustCloud.opacity = Math.min(255, dustCloud.opacity * (1.0 + altitudeIntensity * 0.8));
              
              // Adjust dust fade time based on altitude (longer fade at lower altitudes)
              dustCloud.fadeTimer = Math.floor(dustCloud.fadeTimer * (1.0 + altitudeIntensity * 0.5));
            }
            
            this._airshipEffectLayers.midground.addChild(dustCloud);
            dustCloudSprites.push(dustCloud);
          } catch (error) {
            console.error('❌ Error creating dust cloud sprite:', error);
          }
        }
        
        // Update last position (same as trail)
        lastAirshipX = x;
        lastAirshipY = y;
      }
    }
    
    // Clean up destroyed sprites (in-place)
    (function pruneInPlace(arr){ let w = 0; for (let r = 0; r < arr.length; r++) { const s = arr[r]; if (s && s.parent) arr[w++] = s; } arr.length = w; })(dustCloudSprites);
  };
  

  
  // Extend the existing getOffVehicle function to clear trail
  const _getOffVehicle_original = Game_Player.prototype.getOffVehicle;
  Game_Player.prototype.getOffVehicle = function() {
    const result = _getOffVehicle_original.call(this);
    
    // Use the comprehensive cleanup system
    if (SceneManager._scene && SceneManager._scene.cleanupAllAirshipEffects) {
      SceneManager._scene.cleanupAllAirshipEffects();
    }
    
    return result;
  };
  
  // Add scene transition cleanup
  const _Scene_Map_terminate = Scene_Map.prototype.terminate;
  Scene_Map.prototype.terminate = function() {
    // Clean up airship effects before scene termination
    if (this.cleanupAllAirshipEffects) {
      this.cleanupAllAirshipEffects();
    }
    
    // FIX: Clear the integrity check timer on scene termination
    if (airshipIntegrityTimer) {
      clearInterval(airshipIntegrityTimer);
      airshipIntegrityTimer = null;
    }
    
    _Scene_Map_terminate.call(this);
  };
  
  // Add error recovery for unexpected crashes
  const _Scene_Map_start = Scene_Map.prototype.start;
  Scene_Map.prototype.start = function() {
    _Scene_Map_start.call(this);
    
    // Reset airship state on scene start
    isAirshipActive = false;
    frameCounter = 0;
    
    // FIX: Clear any existing timer before creating new one
    if (airshipIntegrityTimer) {
      clearInterval(airshipIntegrityTimer);
    }
    
    // Emergency cleanup if arrays are corrupted
    if (groundTrailSprites && groundTrailSprites.length > 100) {
      console.warn('🚨 Emergency cleanup: groundTrailSprites array corrupted');
      groundTrailSprites = [];
    }
    if (waterRippleSprites && waterRippleSprites.length > 100) {
      console.warn('🚨 Emergency cleanup: waterRippleSprites array corrupted');
      waterRippleSprites = [];
    }
    if (windLinesSprites && windLinesSprites.length > 100) {
      console.warn('🚨 Emergency cleanup: windLinesSprites array corrupted');
      windLinesSprites = [];
    }
    if (backgroundCloudSprites && backgroundCloudSprites.length > 100) {
      console.warn('🚨 Emergency cleanup: backgroundCloudSprites array corrupted');
      backgroundCloudSprites = [];
    }
    if (foregroundCloudSprites && foregroundCloudSprites.length > 100) {
      console.warn('🚨 Emergency cleanup: foregroundCloudSprites array corrupted');
      foregroundCloudSprites = [];
    }
    if (dustCloudSprites && dustCloudSprites.length > 100) {
      console.warn('🚨 Emergency cleanup: dustCloudSprites array corrupted');
      dustCloudSprites = [];
    }
  };

  // Fix tile loading at map edges by increasing extend tiles (configurable)
  UltraMode7.LOOP_MAPS_EXTEND_TILES = SEAM_EXTEND_TILES; // default 8

  // Optional dev seam overlay (toggle in plugin params)
  if (SEAM_OVERLAY_ENABLED) {
    const _Scene_Map_createDisplayObjects_seam = Scene_Map.prototype.createDisplayObjects;
    Scene_Map.prototype.createDisplayObjects = function() {
      _Scene_Map_createDisplayObjects_seam.call(this);
      this._seamOverlay = new Sprite(new Bitmap(Graphics.width, Graphics.height));
      this._seamOverlay.z = 9999;
      this._seamOverlay.opacity = 80;
      this.addChild(this._seamOverlay);
    };
    // Provide a helper to refresh seam overlay inside primary update to avoid re-aliasing
    Scene_Map.prototype._mauiRefreshSeamOverlay = function() {
      if (!this._seamOverlay) return;
      const b = this._seamOverlay.bitmap; b.clear();
      if (!DEBUG_MODE) return; // only draw debug text when debugging
      b.textColor = '#ff00ff';
      b.drawText(`Wrap: ${$gameMap.isLoopHorizontal()?'H':'-'}${$gameMap.isLoopVertical()?'V':'-'}`, 8, 8, 200, 24);
      b.textColor = '#00ffff';
      b.drawText(`Player: ${$gamePlayer.x.toFixed(2)}, ${$gamePlayer.y.toFixed(2)}`, 8, 32, 320, 24);
      b.textColor = '#ffaa00';
      b.drawText(`GuardPad: ${SEAM_GUARD_PADDING}px`, 8, 56, 240, 24);
    };
  }

  // Airship HUD (speed and altitude near the sprite)
  (function() {
    if (!HUD_ENABLED) return;
    let hudPrevRealX = null;
    let hudPrevRealY = null;
    let hudSpeedEma = 0; // tiles/sec exponential moving average (world space)
    let hudStillFrames = 0; // frames since last movement to drive roll-down-to-zero

    const _Scene_Map_createDisplayObjects_hud = Scene_Map.prototype.createDisplayObjects;
    Scene_Map.prototype.createDisplayObjects = function() {
      _Scene_Map_createDisplayObjects_hud.call(this);
      // container
      this._airshipHud = new Sprite();
      this._airshipHud.z = 9500;
      this._airshipHud.visible = false;
      this._airshipHud.alpha = 1;
      // compute geometry for bottom-center layout:
      // [compass]   [boost]   [speedo] all on a single row, centered, with extra bottom padding for heading label
      const rowGap = Math.max(8, HUD_DIAL_GAP_X);
      const dialH = Math.max(HUD_COMPASS_DIAM, HUD_SPEEDO_DIAM, HUD_BOOST_DIAM);
      const headingPad = HUD_HEADING_LABEL ? (Math.max(12, HUD_FONT_SIZE) + 8) : 0;
      const bmpW = HUD_BOOST_DIAM + HUD_COMPASS_DIAM + HUD_SPEEDO_DIAM + rowGap * 2 + HUD_SIDE_PAD * 2;
      const bmpH = dialH + HUD_DIAL_GAP_Y + headingPad;
      const geom = {};
      const centerY = Math.floor(dialH / 2) + Math.floor(HUD_DIAL_GAP_Y / 2);
      // centers for each dial (left: boost, middle: compass, right: speedo)
      geom.cY = centerY;
      geom.boostCx = HUD_SIDE_PAD + Math.floor(HUD_BOOST_DIAM / 2);
      geom.leftCx = geom.boostCx + Math.floor(HUD_BOOST_DIAM / 2) + rowGap + Math.floor(HUD_COMPASS_DIAM / 2);
      geom.rightCx = geom.leftCx + Math.floor(HUD_COMPASS_DIAM / 2) + rowGap + Math.floor(HUD_SPEEDO_DIAM / 2);
      geom.baseH = bmpH;
      geom.boostCy = centerY;
      geom.boostR = Math.floor(HUD_BOOST_DIAM / 2) - 2;

      this._airshipHudGeom = geom;

      // static layer bitmap
      const bmStatic = new Bitmap(bmpW * HUD_SUPERSAMPLE, bmpH * HUD_SUPERSAMPLE);
      bmStatic._context.scale(HUD_SUPERSAMPLE, HUD_SUPERSAMPLE);
      const ctxS = bmStatic.context; if (HUD_ROUND_CAPS) { ctxS.lineCap = 'round'; ctxS.lineJoin = 'round'; }
      // Compass static
      (function(cx, cy, r){
        // outer ring with lighter bezel and slimmer glow
        ctxS.strokeStyle = 'rgba(0,0,0,0.65)'; ctxS.lineWidth = HUD_HIGH_CONTRAST ? 6 : 4; ctxS.beginPath(); ctxS.arc(cx, cy, r, 0, Math.PI*2); ctxS.stroke();
        // inner shadow and highlight for bevel
        ctxS.strokeStyle = 'rgba(0,0,0,0.25)'; ctxS.lineWidth = 2; ctxS.beginPath(); ctxS.arc(cx, cy, r-2, 0, Math.PI*2); ctxS.stroke();
        ctxS.strokeStyle = 'rgba(255,255,255,0.18)'; ctxS.lineWidth = 1; ctxS.beginPath(); ctxS.arc(cx, cy, r-3, 0, Math.PI*2); ctxS.stroke();
        // slim outer glow
        ctxS.strokeStyle = 'rgba(255,255,255,0.08)'; ctxS.lineWidth = 3; ctxS.beginPath(); ctxS.arc(cx, cy, r+1, 0, Math.PI*2); ctxS.stroke();
        ctxS.strokeStyle = HUD_TICK_COLOR; ctxS.lineWidth = HUD_HIGH_CONTRAST ? 3 : 2; ctxS.beginPath(); ctxS.arc(cx, cy, r-1, 0, Math.PI*2); ctxS.stroke();
        ctxS.fillStyle = HUD_TEXT_COLOR; ctxS.font = `${Math.max(8, HUD_FONT_SIZE-6 + HUD_LABEL_SIZE_DELTA)}px sans-serif`; ctxS.textAlign='center'; ctxS.textBaseline='middle';
        [['N',0],['E',90],['S',180],['W',270]].forEach(([t,deg])=>{ const rad=((deg-90)*Math.PI/180); const tx=cx+Math.cos(rad)*(r-8); const ty=cy+Math.sin(rad)*(r-8); ctxS.fillText(t,tx,ty); });
        ctxS.fillStyle = HUD_TICK_COLOR; for(let i=0;i<4;i++){ const deg=45+i*90; const rad=((deg-90)*Math.PI/180); const tx=cx+Math.cos(rad)*(r-6); const ty=cy+Math.sin(rad)*(r-6); ctxS.beginPath(); ctxS.arc(tx,ty,2,0,Math.PI*2); ctxS.fill(); }
        // glass highlight sweep (top arc)
        ctxS.strokeStyle = 'rgba(255,255,255,0.12)'; ctxS.lineWidth = 6; ctxS.beginPath(); ctxS.arc(cx, cy, r-4, -Math.PI*0.75, -Math.PI*0.25); ctxS.stroke();
      })(geom.leftCx, geom.cY, Math.floor(HUD_COMPASS_DIAM/2)-3);
      // Speedo static
      (function(cx, cy, r){
        // bezel
        ctxS.strokeStyle = 'rgba(0,0,0,0.65)'; ctxS.lineWidth = HUD_HIGH_CONTRAST ? 6 : 4; ctxS.beginPath(); ctxS.arc(cx, cy, r, Math.PI, 2*Math.PI); ctxS.stroke();
        // inner shadow/highlight
        ctxS.strokeStyle = 'rgba(0,0,0,0.25)'; ctxS.lineWidth = 2; ctxS.beginPath(); ctxS.arc(cx, cy, r-2, Math.PI, 2*Math.PI); ctxS.stroke();
        ctxS.strokeStyle = 'rgba(255,255,255,0.18)'; ctxS.lineWidth = 1; ctxS.beginPath(); ctxS.arc(cx, cy, r-3, Math.PI, 2*Math.PI); ctxS.stroke();
        ctxS.strokeStyle = 'rgba(255,255,255,0.08)'; ctxS.lineWidth = 3; ctxS.beginPath(); ctxS.arc(cx, cy, r+1, Math.PI, 2*Math.PI); ctxS.stroke();
        ctxS.strokeStyle = HUD_TICK_COLOR; ctxS.lineWidth = HUD_HIGH_CONTRAST ? 3 : 2; ctxS.beginPath(); ctxS.arc(cx, cy, r-1, Math.PI, 2*Math.PI); ctxS.stroke();
        // major ticks (7 marks) - smaller
        for(let i=0;i<=6;i++){ const t=i/6; const ang=Math.PI+t*Math.PI; ctxS.beginPath(); ctxS.moveTo(cx+Math.cos(ang)*(r-1), cy+Math.sin(ang)*(r-1)); ctxS.lineTo(cx+Math.cos(ang)*(r-4), cy+Math.sin(ang)*(r-4)); ctxS.stroke(); }
        // minor ticks
        ctxS.lineWidth = 1; ctxS.strokeStyle = 'rgba(255,255,255,0.7)';
        for(let i=0;i<12;i++){ const t=(i+0.5)/6; const ang=Math.PI+t*Math.PI; ctxS.beginPath(); ctxS.moveTo(cx+Math.cos(ang)*(r-0), cy+Math.sin(ang)*(r-0)); ctxS.lineTo(cx+Math.cos(ang)*(r-3), cy+Math.sin(ang)*(r-3)); ctxS.stroke(); }
        // labels removed per request
      })(geom.rightCx, geom.cY, Math.floor(HUD_SPEEDO_DIAM/2)-3);
      // Boost static
      (function(cx, cy, r){
        ctxS.strokeStyle = 'rgba(0,0,0,0.65)'; ctxS.lineWidth = HUD_HIGH_CONTRAST ? 6 : 4; ctxS.beginPath(); ctxS.arc(cx, cy, r, 0, Math.PI*2); ctxS.stroke();
        ctxS.strokeStyle = HUD_TICK_COLOR; ctxS.lineWidth = HUD_HIGH_CONTRAST ? 3 : 2; ctxS.beginPath(); ctxS.arc(cx, cy, r-1, 0, Math.PI*2); ctxS.stroke();
        ctxS.strokeStyle = 'rgba(255,255,255,0.2)'; ctxS.lineWidth = Math.max(2, Math.floor(r * HUD_BOOST_PROGRESS_RATIO)); ctxS.beginPath(); ctxS.arc(cx, cy, r-3, -Math.PI/2, 3*Math.PI/2); ctxS.stroke();
        ctxS.strokeStyle = 'rgba(255,255,255,0.35)'; ctxS.lineWidth = 1.5; for(let i=0;i<4;i++){ const a=(-Math.PI/2)+i*(Math.PI/2); ctxS.beginPath(); ctxS.arc(cx,cy,r-3,a,a+0.001); ctxS.stroke(); }
        // subtle top highlight
        ctxS.strokeStyle = 'rgba(255,255,255,0.12)'; ctxS.lineWidth = 5; ctxS.beginPath(); ctxS.arc(cx, cy, r-4, -Math.PI*0.75, -Math.PI*0.25); ctxS.stroke();
      })(geom.boostCx, geom.boostCy, geom.boostR);

      // add static sprite
      this._airshipHudStatic = new Sprite(bmStatic);
      this._airshipHud.addChild(this._airshipHudStatic);

      // dynamic layer bitmap for needles/progress/labels
      const bmDyn = new Bitmap(bmpW * HUD_SUPERSAMPLE, bmpH * HUD_SUPERSAMPLE);
      bmDyn._context.scale(HUD_SUPERSAMPLE, HUD_SUPERSAMPLE);
      bmDyn.fontSize = HUD_FONT_SIZE; bmDyn.outlineColor = 'rgba(0,0,0,0.7)'; bmDyn.outlineWidth = 4;
      this._airshipHudLabel = new Sprite(bmDyn);
      this._airshipHud.addChild(this._airshipHudLabel);
      this._airshipHud.scale.set(Math.max(0.5, HUD_SCALE), Math.max(0.5, HUD_SCALE));
      this._airshipHudYawPrev = null;
      this.addChild(this._airshipHud);
    };

    const _Scene_Map_update_hud = Scene_Map.prototype.update;
    // Expose as an implementation function to be called from the primary update
    Scene_Map.prototype._mauiUpdateHudImpl = function() {
      if (!this._airshipHud) return;
      const hud = this._airshipHud;
      const airship = $gameMap && $gameMap.airship ? $gameMap.airship() : null;
      const inAirship = $gamePlayer && $gamePlayer.isInAirship && $gamePlayer.isInAirship();
      if (inAirship && airship) {
        const sx = airship.screenX();
        const sy = airship.screenY();
        // speed in tiles/sec using world coordinates (independent of camera centering)
        const rx = $gamePlayer._realX || $gamePlayer.x || 0;
        const ry = $gamePlayer._realY || $gamePlayer.y || 0;
        if (hudPrevRealX === null || hudPrevRealY === null) {
          hudPrevRealX = rx; hudPrevRealY = ry;
        }
        // Use map deltas to respect looping
        let dxTiles = rx - hudPrevRealX;
        let dyTiles = ry - hudPrevRealY;
        if ($gameMap && $gameMap.isLoopHorizontal && $gameMap.isLoopHorizontal()) {
          const w = $gameMap.width();
          if (dxTiles > w/2) dxTiles -= w; else if (dxTiles < -w/2) dxTiles += w;
        }
        if ($gameMap && $gameMap.isLoopVertical && $gameMap.isLoopVertical()) {
          const h = $gameMap.height();
          if (dyTiles > h/2) dyTiles -= h; else if (dyTiles < -h/2) dyTiles += h;
        }
        hudPrevRealX = rx; hudPrevRealY = ry;
        const tilesPerFrame = Math.sqrt(dxTiles*dxTiles + dyTiles*dyTiles);
        const speedTilesPerSec = tilesPerFrame * (Graphics.frameRate || 60);
        // EMA smoothing
        hudSpeedEma = hudSpeedEma * 0.85 + speedTilesPerSec * 0.15;
        // Roll-down to exact zero when stopped (keep smooth deceleration)
        const moving = tilesPerFrame > 1e-4;
        if (!moving) {
          hudStillFrames++;
          // after a short settle time, increase decay so the needle reaches 0
          const decay = hudStillFrames > 12 ? 0.82 : 0.92;
          hudSpeedEma *= decay;
          if (hudSpeedEma < 0.02) hudSpeedEma = 0;
        } else {
          hudStillFrames = 0;
        }

        // Prepare drawing helpers
        const label = this._airshipHudLabel; const bm = label.bitmap; const ctx = bm.context;
        const geom = this._airshipHudGeom;
        const yawDeg = (typeof $gameTemp !== 'undefined' && $gameTemp && typeof $gameTemp._yawFacing === 'number') ? $gameTemp._yawFacing : (UltraMode7 && UltraMode7.getYaw ? UltraMode7.getYaw() : 0);
        const boostT = Math.max(0, Math.min(1, boostCharge01));
        const nowTs = (Graphics && Graphics.frameCount != null) ? Graphics.frameCount * 16 : Date.now();
        if (HUD_ROUND_CAPS) { ctx.lineCap = 'round'; ctx.lineJoin = 'round'; }
        // HUD dirty check: only redraw when values change past thresholds
        const prevYawRadHud = this._hudYawRad;
        const prevSpeedAng = this._hudSpeedAng;
        const prevBoost = this._hudBoostT;
        this._hudBoostT = boostT;
        const yawChanged = !prevYawRadHud || Math.abs(((prevYawRadHud||0) - ((-yawDeg-90)*Math.PI/180))) > (1 * Math.PI/180);
        const speedChanged = !prevSpeedAng || Math.abs((prevSpeedAng||0) - (Math.PI + (hudSpeedEma * HUD_TILE_METERS * (HUD_SPEED_UNITS==='mph'?2.23693629:1) / (HUD_SPEED_UNITS==='mph'?HUD_MAX_MPH:HUD_TILES_MAX)) * Math.PI)) > (2 * Math.PI/180);
        // Dynamically compute a tiny epsilon based on expected per-update change
        // so the gauge updates even under slow drain/fill while faded
        const fpsNow = (Graphics.frameRate || 60);
        const dtBoost = UPDATE_FREQUENCY / fpsNow;
        let expectedDelta = 0.0;
        if (BOOST_ENABLED) {
          if (boostActive) expectedDelta = BOOST_ACTIVE_DRAIN_RATE * dtBoost;
          else if (isAirshipLateralMoving) expectedDelta = BOOST_FILL_RATE * dtBoost;
          else expectedDelta = BOOST_STOP_DRAIN_RATE * dtBoost;
        }
        const minBoostDelta = Math.max(1e-4, expectedDelta * 0.2); // 20% of expected change
        const boostChanged = (prevBoost == null) || (Math.abs((prevBoost||0) - boostT) >= minBoostDelta);
        const needRedrawHud = yawChanged || speedChanged || boostChanged || !this._hudDrawnOnce;
        // Disable decimation for HUD gauges so all meters update smoothly regardless of FPS
        const lowFpsHud = false;
        const decimateHud = false;
        const allowRedrawHud = needRedrawHud;
        if (!allowRedrawHud) {
          // placement and alpha still update below
          // Partial redraw path: redraw only the boost arc when boostChanged under decimation
          if (BOOST_ENABLED && boostChanged) {
            const bCx = geom.boostCx, bCy = geom.boostCy, rB = geom.boostR;
            // erase boost band area
            ctx.save();
            ctx.globalCompositeOperation = 'destination-out';
            ctx.beginPath();
            const pad = Math.ceil((rB * HUD_BOOST_PROGRESS_RATIO) + 4);
            ctx.arc(bCx, bCy, rB + pad, 0, Math.PI*2);
            ctx.arc(bCx, bCy, Math.max(1, rB - pad), 0, Math.PI*2, true);
            ctx.closePath();
            ctx.fill();
            ctx.restore();
            // draw boost arc only
            const interpColor = (tval)=>{ let rMix,gMix,bMix; if(tval<=0.5){ const k=tval*2; rMix=BOOST_RGB_START.r+(BOOST_RGB_MID.r-BOOST_RGB_START.r)*k; gMix=BOOST_RGB_START.g+(BOOST_RGB_MID.g-BOOST_RGB_START.g)*k; bMix=BOOST_RGB_START.b+(BOOST_RGB_MID.b-BOOST_RGB_START.b)*k; } else { const k=(tval-0.5)*2; rMix=BOOST_RGB_MID.r+(BOOST_RGB_END.r-BOOST_RGB_MID.r)*k; gMix=BOOST_RGB_MID.g+(BOOST_RGB_END.g-BOOST_RGB_MID.g)*k; bMix=BOOST_RGB_MID.b+(BOOST_RGB_END.b-BOOST_RGB_MID.b)*k; } return rgbaString(rMix,gMix,bMix,0.95); };
            const bucket = Math.max(0, Math.min(20, Math.round(boostT * 20)));
            this._boostColorCache = this._boostColorCache || {};
            let arcColor = this._boostColorCache[bucket];
            if (!arcColor) { arcColor = interpColor(bucket/20); this._boostColorCache[bucket] = arcColor; }
            ctx.strokeStyle = arcColor; ctx.lineWidth = Math.max(2, Math.floor(rB * HUD_BOOST_PROGRESS_RATIO));
            const sweep = boostT*2*Math.PI; const endAng = HUD_BOOST_DIRECTION==='ccw'? (-Math.PI/2 - sweep) : (-Math.PI/2 + sweep);
            ctx.beginPath(); ctx.arc(bCx,bCy,rB-3,-Math.PI/2,endAng); ctx.stroke();
            // end notch and rounded cap
            const notchX = bCx + Math.cos(endAng) * (rB-3);
            const notchY = bCy + Math.sin(endAng) * (rB-3);
            ctx.fillStyle = arcColor; ctx.beginPath(); ctx.arc(notchX, notchY, Math.max(2, (ctx.lineWidth/2)-0.5), 0, Math.PI*2); ctx.fill();
            bm._baseTexture.update();
          }
        } else {
          bm.clear();
        }
        // Compass needle and yaw trail
        const yawRadTarget = ((-yawDeg-90) * Math.PI/180);
        // needle spring easing toward target (normalize delta to shortest path to avoid 360 wrap)
        if (this._hudYawRad == null) {
          this._hudYawRad = yawRadTarget;
        } else {
          let delta = yawRadTarget - this._hudYawRad;
          while (delta > Math.PI) delta -= 2 * Math.PI;
          while (delta < -Math.PI) delta += 2 * Math.PI;
          this._hudYawRad += delta * 0.2;
        }
        const yawRad = this._hudYawRad;
        if (this._airshipHudYawPrev == null) this._airshipHudYawPrev = yawDeg;
        const prevYawRad = ((-this._airshipHudYawPrev-90) * Math.PI/180);
        // subtle yaw trail arc along ring
        if (yawChanged && allowRedrawHud) {
          ctx.strokeStyle = 'rgba(255,255,255,0.15)'; ctx.lineWidth = 1.5;
          const rTrail = Math.floor(HUD_COMPASS_DIAM/2)-4;
          let a0 = prevYawRad, a1 = yawRad;
          // normalize direction shortest path
          while (a1 - a0 > Math.PI) a0 += 2*Math.PI;
          while (a1 - a0 < -Math.PI) a0 -= 2*Math.PI;
          ctx.beginPath(); ctx.arc(geom.leftCx, geom.cY, rTrail, a0, a1); ctx.stroke();
        }
        // compass needle with subtle shadow and hub
        // wedge needle (compass)
        const rComp = Math.floor(HUD_COMPASS_DIAM/2) - 9;
        const nx = Math.cos(yawRad), ny = Math.sin(yawRad);
        const px = -ny, py = nx; // perpendicular
        const baseLen = Math.max(6, Math.floor(rComp*0.28));
        const tipX = geom.leftCx + nx * rComp;
        const tipY = geom.cY + ny * rComp;
        const baseX = geom.leftCx - nx * Math.max(4, baseLen*0.25);
        const baseY = geom.cY - ny * Math.max(4, baseLen*0.25);
        const halfW = Math.max(2.5, HUD_NEEDLE_WIDTH * 1.4);
        // shadow
        ctx.fillStyle = 'rgba(0,0,0,0.35)';
        ctx.beginPath();
        ctx.moveTo(tipX+1, tipY+1);
        ctx.lineTo(baseX + px*halfW + 1, baseY + py*halfW + 1);
        ctx.lineTo(baseX - px*halfW + 1, baseY - py*halfW + 1);
        ctx.closePath();
        ctx.fill();
        // needle
        if (allowRedrawHud) {
          ctx.fillStyle = HUD_NEEDLE_COLOR;
          ctx.beginPath();
          ctx.moveTo(tipX, tipY);
          ctx.lineTo(baseX + px*halfW, baseY + py*halfW);
          ctx.lineTo(baseX - px*halfW, baseY - py*halfW);
          ctx.closePath();
          ctx.fill();
          // hub
          ctx.fillStyle = HUD_NEEDLE_COLOR; ctx.beginPath(); ctx.arc(geom.leftCx, geom.cY, 2.8, 0, Math.PI*2); ctx.fill();
        }
        this._airshipHudYawPrev = yawDeg;
        // Heading label under compass
        if (HUD_HEADING_LABEL) {
          const dirs8 = ['N','NE','E','SE','S','SW','W','NW'];
          const dirs16 = ['N','NNE','NE','ENE','E','ESE','SE','SSE','S','SSW','SW','WSW','W','WNW','NW','NNW'];
          const set = HUD_HEADING_GRANULARITY === 16 ? dirs16 : dirs8;
          const displayDeg = ((-yawDeg % 360) + 360) % 360; // flip to match compass needle orientation (fixes E/W)
          const step = 360 / set.length; let idx = Math.round(displayDeg / step) % set.length;
          const head = set[idx];
          // pill background
          const rC = Math.floor(HUD_COMPASS_DIAM/2)-3; const pillY = geom.cY + rC + 8;
          const pillW = Math.max(24, head.length * 10); const pillH = 14; const rx = 8;
          ctx.fillStyle = 'rgba(0,0,0,0.35)'; ctx.beginPath();
          ctx.moveTo(geom.leftCx - pillW/2 + rx, pillY);
          ctx.arcTo(geom.leftCx + pillW/2, pillY, geom.leftCx + pillW/2, pillY + pillH, rx);
          ctx.arcTo(geom.leftCx + pillW/2, pillY + pillH, geom.leftCx - pillW/2, pillY + pillH, rx);
          ctx.arcTo(geom.leftCx - pillW/2, pillY + pillH, geom.leftCx - pillW/2, pillY, rx);
          ctx.arcTo(geom.leftCx - pillW/2, pillY, geom.leftCx + pillW/2, pillY, rx);
          ctx.closePath(); ctx.fill();
          // text
          ctx.fillStyle = HUD_TEXT_COLOR; ctx.font = `${Math.max(8, HUD_FONT_SIZE-4)}px sans-serif`; ctx.textAlign='center'; ctx.textBaseline='top';
          ctx.fillText(head, geom.leftCx, pillY + 2);
        }
        // Speedo needle
        const mps = hudSpeedEma * HUD_TILE_METERS; const mph = mps * 2.23693629; const value = HUD_SPEED_UNITS==='mph'? mph : hudSpeedEma; const maxVal = HUD_SPEED_UNITS==='mph'? HUD_MAX_MPH : HUD_TILES_MAX; const ratio = Math.max(0, Math.min(1, value / maxVal));
        const rS = Math.floor(HUD_SPEEDO_DIAM/2)-3; const angSTarget = Math.PI + ratio*Math.PI;
        this._hudSpeedAng = (this._hudSpeedAng == null) ? angSTarget : this._hudSpeedAng + (angSTarget - this._hudSpeedAng) * 0.18;
        const angS = this._hudSpeedAng;
        // speed needle with subtle shadow and hub
        // wedge needle (speedo)
        const nxS = Math.cos(angS), nyS = Math.sin(angS);
        const pxS = -nyS, pyS = nxS;
        const tipSX = geom.rightCx + nxS * (rS-6);
        const tipSY = geom.cY + nyS * (rS-6);
        const baseSX = geom.rightCx - nxS * Math.max(4, rS*0.20);
        const baseSY = geom.cY - nyS * Math.max(4, rS*0.20);
        const halfWS = Math.max(2.5, HUD_NEEDLE_WIDTH * 1.4);
        // shadow
        ctx.fillStyle = 'rgba(0,0,0,0.35)';
        ctx.beginPath();
        ctx.moveTo(tipSX+1, tipSY+1);
        ctx.lineTo(baseSX + pxS*halfWS + 1, baseSY + pyS*halfWS + 1);
        ctx.lineTo(baseSX - pxS*halfWS + 1, baseSY - pyS*halfWS + 1);
        ctx.closePath();
        ctx.fill();
        // needle
        if (allowRedrawHud) {
          ctx.fillStyle = HUD_NEEDLE_COLOR;
          ctx.beginPath();
          ctx.moveTo(tipSX, tipSY);
          ctx.lineTo(baseSX + pxS*halfWS, baseSY + pyS*halfWS);
          ctx.lineTo(baseSX - pxS*halfWS, baseSY - pyS*halfWS);
          ctx.closePath();
          ctx.fill();
          ctx.fillStyle = HUD_NEEDLE_COLOR; ctx.beginPath(); ctx.arc(geom.rightCx, geom.cY, 2.8, 0, Math.PI*2); ctx.fill();
        }
        const txt = HUD_SPEED_UNITS==='mph' ? `${value.toFixed(0)} mph` : `${value.toFixed(1)} t/s`;
        if (allowRedrawHud) {
          // always draw text on redraw to prevent flashing; cache only to avoid unnecessary clears
          ctx.fillStyle = HUD_TEXT_COLOR; ctx.font = `${Math.max(10, HUD_FONT_SIZE-2)}px sans-serif`; ctx.textAlign='center'; ctx.textBaseline='middle';
          ctx.fillText(txt, geom.rightCx, geom.cY+8);
          this._hudSpeedTxt = txt;
        }
        // Boost progress arc with gradient + direction
        if (BOOST_ENABLED && allowRedrawHud) {
          const bCx = geom.boostCx, bCy = geom.boostCy, rB = geom.boostR;
          const interpColor = (tval)=>{ let rMix,gMix,bMix; if(tval<=0.5){ const k=tval*2; rMix=BOOST_RGB_START.r+(BOOST_RGB_MID.r-BOOST_RGB_START.r)*k; gMix=BOOST_RGB_START.g+(BOOST_RGB_MID.g-BOOST_RGB_START.g)*k; bMix=BOOST_RGB_START.b+(BOOST_RGB_MID.b-BOOST_RGB_START.b)*k; } else { const k=(tval-0.5)*2; rMix=BOOST_RGB_MID.r+(BOOST_RGB_END.r-BOOST_RGB_MID.r)*k; gMix=BOOST_RGB_MID.g+(BOOST_RGB_END.g-BOOST_RGB_MID.g)*k; bMix=BOOST_RGB_MID.b+(BOOST_RGB_END.b-BOOST_RGB_MID.b)*k; } return rgbaString(rMix,gMix,bMix,0.95); };
          const bucket = Math.max(0, Math.min(20, Math.round(boostT * 20)));
          this._boostGradCache = this._boostGradCache || {};
          this._boostColorCache = this._boostColorCache || {};
          let arcColor = this._boostColorCache[bucket];
          if (!arcColor) { arcColor = interpColor(bucket/20); this._boostColorCache[bucket] = arcColor; }
          // Thicken the progress arc while boosting for stronger emphasis
          const arcWidth = Math.max(2, Math.floor(rB * HUD_BOOST_PROGRESS_RATIO) * (boostActive ? 1.5 : 1.0));
          ctx.strokeStyle = arcColor; ctx.lineWidth = arcWidth; ctx.beginPath(); const sweep = boostT*2*Math.PI; const endAng = HUD_BOOST_DIRECTION==='ccw'? (-Math.PI/2 - sweep) : (-Math.PI/2 + sweep); ctx.arc(bCx,bCy,rB-3,-Math.PI/2,endAng); ctx.stroke();
          // end notch
          const notchX = bCx + Math.cos(endAng) * (rB-3);
          const notchY = bCy + Math.sin(endAng) * (rB-3);
          ctx.fillStyle = arcColor; ctx.beginPath(); ctx.arc(notchX, notchY, 2, 0, Math.PI*2); ctx.fill();
          if (boostActive) {
            // Stronger pulsing halo and inner glow while boosting
            const phase=((Graphics.frameCount||0)%60)/60; const amp=0.45+0.45*Math.sin(phase*2*Math.PI);
            const aBucket = Math.max(0, Math.min(10, Math.round(amp*10)));
            this._boostGradCache[rB] = this._boostGradCache[rB] || {};
            let grad = this._boostGradCache[rB][aBucket];
            if (!grad) {
              grad = ctx.createRadialGradient(bCx,bCy,Math.max(1,rB/6),bCx,bCy,rB+2);
              grad.addColorStop(0,'rgba(255,255,255,0.0)');
              const a=(0.35+aBucket/10).toFixed(2);
              const endGlow = rgbaString((BOOST_RGB_END.r+255)/2,(BOOST_RGB_END.g+255)/2,(BOOST_RGB_END.b+255)/2,a);
              grad.addColorStop(1, endGlow);
              this._boostGradCache[rB][aBucket] = grad;
            }
            ctx.save();
            ctx.globalCompositeOperation = 'lighter';
            ctx.strokeStyle=grad; ctx.lineWidth=3; ctx.beginPath(); ctx.arc(bCx,bCy,rB-1,0,Math.PI*2); ctx.stroke();
            // Soft inner fill
            const innerFill = rgbaString(BOOST_RGB_MID.r, BOOST_RGB_MID.g, BOOST_RGB_MID.b, 0.12 + amp*0.08);
            ctx.fillStyle = innerFill; ctx.beginPath(); ctx.arc(bCx,bCy,Math.max(1,rB-8),0,Math.PI*2); ctx.fill();
            ctx.restore();
          }
          // rounded cap illusion for progress arc end
          // Incremental charging indicators: ticks around the ring that light up as charge fills
          const segments = 12; // number of ticks
          // Light up tick as soon as the segment threshold is reached
          const litSegments = Math.min(segments, Math.ceil(boostT * segments));
          const tickLen = Math.max(2, Math.floor(rB * 0.05));
          const innerTick = rB - tickLen;
          const outerTick = rB - 1;
          for (let i = 0; i < segments; i++) {
            const angCS = this._boostTickAngles ? this._boostTickAngles[i] : null;
            const c = angCS ? angCS.c : Math.cos(-Math.PI/2 + (i/segments) * Math.PI * 2);
            const s = angCS ? angCS.s : Math.sin(-Math.PI/2 + (i/segments) * Math.PI * 2);
            const isLit = i < litSegments;
            ctx.strokeStyle = isLit ? arcColor : 'rgba(255,255,255,0.18)';
            ctx.lineWidth = isLit ? 1.5 : 1;
            const tx0 = bCx + c * (innerTick - 2);
            const ty0 = bCy + s * (innerTick - 2);
            const tx1 = bCx + c * outerTick;
            const ty1 = bCy + s * outerTick;
            ctx.beginPath(); ctx.moveTo(tx0, ty0); ctx.lineTo(tx1, ty1); ctx.stroke();
          }
          ctx.fillStyle = arcColor; ctx.beginPath(); ctx.arc(notchX, notchY, Math.max(2, (ctx.lineWidth/2)-0.5), 0, Math.PI*2); ctx.fill();
          // smaller center text
          const centerTxt = HUD_BOOST_SHOW_PERCENT ? `${Math.round(boostT*100)}%` : (boostActive ? 'BST' : 'CHG');
          // always draw text on redraw to prevent flashing
          ctx.fillStyle = HUD_TEXT_COLOR; ctx.font = `${Math.max(7, HUD_FONT_SIZE-6)}px sans-serif`; ctx.textAlign='center'; ctx.textBaseline='middle';
          ctx.fillText(centerTxt, bCx, bCy);
          this._hudCenterTxt = centerTxt;
          if (!boostActive && boostT>=1) { ctx.fillStyle = HUD_TEXT_COLOR; ctx.font = `${Math.max(8, HUD_FONT_SIZE-6)}px sans-serif`; ctx.textAlign='center'; ctx.textBaseline='bottom'; ctx.fillText('READY', bCx, bCy - (rB+6)); }
        }
        if (allowRedrawHud) bm._baseTexture.update();
        this._hudDrawnOnce = true;

        // placement: corner or sprite-follow
        if (HUD_MODE === 'corner') {
          const w = bm.width * HUD_SCALE; const h = bm.height * HUD_SCALE;
          let x = 0, y = 0;
          if (HUD_CORNER.indexOf('right')>=0) x = Graphics.width - w;
          if (HUD_CORNER.indexOf('bottom')>=0) y = Graphics.height - h;
          x = Math.max(0, Math.min(x, Graphics.width - w));
          y = Math.max(0, Math.min(y, Graphics.height - h));
          hud.x = Math.round(x + HUD_OFFSET_X);
          hud.y = Math.round(y + HUD_OFFSET_Y);
        } else if (HUD_MODE === 'sprite') {
          // center HUD horizontally under the airship
          hud.x = Math.round(sx - (bm.width/2)) + HUD_OFFSET_X;
          hud.y = sy + HUD_OFFSET_Y;
        } else {
          // bottom-center screen layout
          const w = bm.width * HUD_SCALE; const h = bm.height * HUD_SCALE;
          const x = Math.round((Graphics.width - w) / 2);
          const y = Math.round(Graphics.height - h - HUD_BOTTOM_MARGIN);
          hud.x = x + HUD_OFFSET_X;
          hud.y = y; // ignore HUD_OFFSET_Y for bottom-center; use margin instead
        }
        hud.visible = true;
        // Auto-fade based on speed and boost state
        if (HUD_AUTO_FADE) {
          const fps = Graphics.frameRate || 60;
          const alphaTarget = (HUD_ALWAYS_VISIBLE_DURING_BOOST && boostActive) ? 1 : (speedTilesPerSec > HUD_IDLE_SPEED ? 1 : 0.35);
          const tau = alphaTarget > hud.alpha ? (HUD_FADE_IN_MS/1000) : (HUD_FADE_OUT_MS/1000);
          const k = Math.min(1, (1 / Math.max(1e-3, tau)) / fps);
          hud.alpha += (alphaTarget - hud.alpha) * k;
        }
      } else {
        if (this._airshipHud.visible) this._airshipHud.visible = false;
        hudPrevRealX = null; hudPrevRealY = null; hudSpeedEma = 0;
      }
    };

    // Rewire primary update once to call our hud implementation, avoiding re-alias chains
    if (!Scene_Map.prototype._mauiHudUpdateInstalled) {
      const _Scene_Map_update_hud_outer = Scene_Map.prototype.update;
      Scene_Map.prototype.update = function() {
        _Scene_Map_update_hud_outer.call(this);
        this._mauiUpdateHudImpl && this._mauiUpdateHudImpl();
        this._mauiRefreshSeamOverlay && this._mauiRefreshSeamOverlay();
        this._mauiContrailFade && this._mauiContrailFade();
      };
      Scene_Map.prototype._mauiHudUpdateInstalled = true;
    }

    const _Scene_Map_terminate_hud = Scene_Map.prototype.terminate;
    Scene_Map.prototype.terminate = function() {
      if (this._airshipHudLabel && this._airshipHudLabel.bitmap) {
        this._airshipHudLabel.bitmap.clear();
      }
      if (this._airshipHudStatic && this._airshipHudStatic.bitmap) {
        this._airshipHudStatic.bitmap.clear();
      }
      this._airshipHudGeom = null;
      _Scene_Map_terminate_hud.call(this);
    };
  })();
  
  // Global error recovery and debugging system
  window.MAUI_FF6Airship_ErrorRecovery = {
    // Debug information
    debugInfo: {
      totalSprites: 0,
      frameRate: 0,
      memoryUsage: 0,
      lastUpdate: Date.now(),
      bitmapPoolStats: null,
      memoryLeaks: []
    },
    
    // Update debug information
    updateDebugInfo: function() {
      const arrays = [groundTrailSprites, waterRippleSprites, windLinesSprites, 
                     backgroundCloudSprites, foregroundCloudSprites, dustCloudSprites,
                     contrailSprites, vortexSprites];
      
      this.debugInfo.totalSprites = arrays.reduce((total, array) => total + array.length, 0);
      this.debugInfo.frameRate = Graphics.frameRate;
      this.debugInfo.lastUpdate = Date.now();
      
      // Check for bitmap pool memory leaks
      if (BitmapPool && BitmapPool.checkForLeaks) {
        this.debugInfo.memoryLeaks = BitmapPool.checkForLeaks();
        this.debugInfo.bitmapPoolStats = BitmapPool.getStats();
      }
      
      // Periodic debug logging removed for reliability
    },
    // Force cleanup all airship effects
    forceCleanup: function() {
      console.log('🚨 Emergency airship effects cleanup triggered');
      
      // FIX: Clear the integrity check timer
      if (airshipIntegrityTimer) {
        clearInterval(airshipIntegrityTimer);
        airshipIntegrityTimer = null;
      }
      
      // Clear all sprite arrays
      const arrays = [groundTrailSprites, waterRippleSprites, windLinesSprites, 
                     backgroundCloudSprites, foregroundCloudSprites, dustCloudSprites,
                     contrailSprites, vortexSprites];
      
      arrays.forEach(array => {
        if (array && Array.isArray(array)) {
          array.forEach(sprite => {
      if (sprite && sprite.parent) {
              try {
        sprite.parent.removeChild(sprite);
                sprite._destroyed = true;
                // FIX: Properly destroy sprites to prevent memory leaks
                if (sprite.destroy) {
                  sprite.destroy();
                }
              } catch (error) {
                console.error('❌ Error during emergency sprite cleanup:', error);
              }
            }
          });
          array.length = 0;
        }
      });
      
      // FIX: Clear bitmap pool to prevent memory leaks
      if (BitmapPool && BitmapPool.clear) {
        BitmapPool.clear();
      }
      
      // Reset all timers and state
      trailCreationTimer = 0;
      rippleCreationTimer = 0;
      windLinesCreationTimer = 0;
      cloudCreationTimer = 0;
      lastAirshipX = 0;
      lastAirshipY = 0;
      isAirshipActive = false;
      frameCounter = 0;
      
      console.log('✅ Emergency cleanup completed');
    },
    
    // Check for corrupted state
    checkIntegrity: function() {
      const arrays = [groundTrailSprites, waterRippleSprites, windLinesSprites, 
                     backgroundCloudSprites, foregroundCloudSprites, dustCloudSprites,
                     contrailSprites, vortexSprites];
      
      let totalSprites = 0;
      arrays.forEach(array => {
        if (array && Array.isArray(array)) {
          totalSprites += array.length;
        }
      });
      
      // Check for memory leaks in bitmap pool
      let hasMemoryLeaks = false;
      if (BitmapPool && BitmapPool.checkForLeaks) {
        const leaks = BitmapPool.checkForLeaks();
        if (leaks && leaks.length > 0) {
          hasMemoryLeaks = true;
          console.warn('🚨 BitmapPool memory leaks detected during integrity check');
        }
      }
      
      if (totalSprites > 800 || hasMemoryLeaks) { // Reduced from 1000 to 800 for better performance
        console.warn('🚨 Too many sprites or memory leaks detected, triggering emergency cleanup');
        this.forceCleanup();
        return false;
      }
      
      return true;
    }
  };
  
  // FIX: Use timer variable for proper cleanup
  airshipIntegrityTimer = setInterval(() => {
    if (window.MAUI_FF6Airship_ErrorRecovery) {
      window.MAUI_FF6Airship_ErrorRecovery.checkIntegrity();
      window.MAUI_FF6Airship_ErrorRecovery.updateDebugInfo();
    }
  }, 60000); // Check every 60 seconds (reduced frequency)
  
  console.log('✅ MAUI_FF6Airship plugin loaded with error recovery system');

})();

