/*:
 * @target MZ
 * @plugindesc Dark Portal Goddess v2.0.0 - Dark Portal with Evil Goddess Eyes
 *
 * @command StartDarkPortal
 * @text Start Dark Portal
 * @desc Start the dark portal with evil goddess eyes effect
 *
 * @command StopDarkPortal
 * @text Stop Dark Portal
 * @desc Stop the dark portal effect
 *
 * @param Duration
 * @text Portal Duration
 * @type number
 * @min 60
 * @max 1800
 * @default 600
 * @desc Duration of the portal effect in frames (10-30 seconds at 60fps)
 *
 * @param Intensity
 * @text Portal Intensity
 * @type number
 * @min 0.1
 * @max 1.0
 * @decimals 1
 * @default 0.8
 * @desc Intensity of the portal effect (0.1-1.0)
 *
 * @param EyeColor
 * @text Evil Eye Color
 * @type string
 * @default #FF0000
 * @desc Color of the evil goddess eyes
 *
 * @param PortalSize
 * @text Portal Size
 * @type number
 * @min 100
 * @max 400
 * @default 200
 * @desc Size of the dark portal
 *
 * @help DarkPortalGoddess.js
 *
 * A dramatic atmospheric plugin that creates a dark portal opening
 * with evil goddess eyes appearing through it.
 * Uses canvas shapes and PIXI effects for an immersive experience.
 *
 * FEATURES:
 * - Dark portal opening animation
 * - Evil goddess eyes with glowing effects
 * - Atmospheric darkness and corruption
 * - Particle effects and energy waves
 * - Customizable duration and intensity
 * - Smooth transitions and animations
 *
 * USAGE:
 * - Call "Start Dark Portal" from events
 * - Use "Stop Dark Portal" to end the effect
 * - Configure parameters in plugin settings
 *
 * Perfect for:
 * - Horror scenes
 * - Evil deity appearances
 * - Dark ritual sequences
 * - Atmospheric storytelling
 */

(() => {
    'use strict';

    const pluginName = 'DarkPortalGoddess';
    const parameters = PluginManager.parameters(pluginName);

    // Plugin parameters
    const DURATION = Number(parameters['Duration'] || 600);
    const INTENSITY = Number(parameters['Intensity'] || 0.8);
    const EYE_COLOR = parameters['EyeColor'] || '#FF0000';
    const PORTAL_SIZE = Number(parameters['PortalSize'] || 200);

    // Plugin commands
    PluginManager.registerCommand(pluginName, 'StartDarkPortal', args => {
        startPortal();
    });

    PluginManager.registerCommand(pluginName, 'StopDarkPortal', args => {
        stopPortal();
    });

    // Main portal class
    class DarkPortalGoddess {
        constructor() {
            this.active = false;
            this.timer = 0;
            this.progress = 0;
            this.intensity = INTENSITY;
            this.duration = DURATION;
            
            // Visual elements
            this.portalSprite = null;
            this.centeredEye = null;
            this.energyWaves = [];
            this.darknessOverlay = null;
            
            // Animation properties
            this.animationTimer = 0;
            this.energyParticles = [];
            
            // Memory management
            this.pendingTimeouts = new Set();
            this.activeGraphics = new Set();
            
            this.initialize();
        }

        initialize() {
            this.createContainers();
            this.createVisualElements();
            this.createParticleSystems();
        }

        createContainers() {
            this.container = new PIXI.Container();
            this.container.zIndex = 1000;
            
            this.particleContainer = new PIXI.Container();
            this.particleContainer.zIndex = 1001;
            
            this.darknessContainer = new PIXI.Container();
            this.darknessContainer.zIndex = 999;
        }

        createVisualElements() {
            this.createPortal();
            this.createEyes();
            this.createDarknessOverlay();
            this.createEnergyWaves();
        }

        createPortal() {
            const portalTexture = this.createPortalTexture();
            this.portalSprite = new PIXI.Sprite(portalTexture);
            this.portalSprite.anchor.set(0.5);
            this.portalSprite.x = Graphics.width / 2;
            this.portalSprite.y = Graphics.height / 2;
            this.portalSprite.alpha = 0;
            this.container.addChild(this.portalSprite);
        }

        createPortalTexture() {
            const canvas = document.createElement('canvas');
            canvas.width = PORTAL_SIZE * 4;
            canvas.height = PORTAL_SIZE * 4;
            const ctx = canvas.getContext('2d');
            
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // Create swirling vortex void - deep black center
            const voidGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, PORTAL_SIZE * 2);
            voidGradient.addColorStop(0, 'rgba(0, 0, 0, 1)');
            voidGradient.addColorStop(0.1, 'rgba(0, 0, 0, 1)');
            voidGradient.addColorStop(0.3, 'rgba(20, 0, 40, 0.9)');
            voidGradient.addColorStop(0.6, 'rgba(50, 0, 100, 0.6)');
            voidGradient.addColorStop(0.8, 'rgba(100, 0, 150, 0.3)');
            voidGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
            
            ctx.fillStyle = voidGradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, PORTAL_SIZE * 2, 0, Math.PI * 2);
            ctx.fill();
            
            // Create realistic spiral arms with smooth curves
            for (let arm = 0; arm < 6; arm++) {
                const armAngle = (Math.PI * 2 * arm) / 6;
                
                // Create smooth spiral path
                ctx.beginPath();
                ctx.strokeStyle = `rgba(150, 0, 200, 0.6)`;
                ctx.lineWidth = 3;
                ctx.lineCap = 'round';
                
                // Draw spiral curve using parametric equations
                for (let i = 0; i <= 100; i++) {
                    const t = i / 100;
                    // Logarithmic spiral: r = a * e^(b*θ)
                    const angle = armAngle + t * Math.PI * 6; // 3 full rotations
                    const radius = PORTAL_SIZE * (0.05 + t * 1.5) * Math.exp(t * 0.5);
                    
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    
                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();
                
                // Add particles along the spiral path
                for (let i = 0; i < 25; i++) {
                    const t = i / 25;
                    const angle = armAngle + t * Math.PI * 6;
                    const radius = PORTAL_SIZE * (0.05 + t * 1.5) * Math.exp(t * 0.5);
                    
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    
                    // Particle size decreases as it gets further from center
                    const particleSize = (1 - t) * 6 + 1;
                    const particleAlpha = (1 - t) * 0.9;
                    
                    // Main particle
                    ctx.fillStyle = `rgba(255, 100, 200, ${particleAlpha})`;
                    ctx.beginPath();
                    ctx.arc(x, y, particleSize, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // Glow effect
                    ctx.fillStyle = `rgba(200, 50, 250, ${particleAlpha * 0.4})`;
                    ctx.beginPath();
                    ctx.arc(x, y, particleSize * 2, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
            
            // Add secondary spiral arms for more complexity
            for (let arm = 0; arm < 3; arm++) {
                const armAngle = (Math.PI * 2 * arm) / 3 + Math.PI / 3;
                
                ctx.beginPath();
                ctx.strokeStyle = `rgba(100, 0, 150, 0.4)`;
                ctx.lineWidth = 2;
                ctx.lineCap = 'round';
                
                // Tighter spiral for secondary arms
                for (let i = 0; i <= 80; i++) {
                    const t = i / 80;
                    const angle = armAngle + t * Math.PI * 4;
                    const radius = PORTAL_SIZE * (0.1 + t * 1.2) * Math.exp(t * 0.3);
                    
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    
                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();
            }
            
            // Add spiral vortex rings that get tighter toward center
            for (let i = 0; i < 8; i++) {
                const ringRadius = PORTAL_SIZE * (0.05 + i * 0.2);
                const ringAlpha = 0.4 - i * 0.05;
                
                // Create spiral ring instead of perfect circle
                ctx.beginPath();
                ctx.strokeStyle = `rgba(150, 0, 200, ${ringAlpha})`;
                ctx.lineWidth = 2;
                ctx.lineCap = 'round';
                
                for (let j = 0; j <= 100; j++) {
                    const t = j / 100;
                    const angle = t * Math.PI * 2;
                    // Add spiral variation to the ring
                    const spiralOffset = Math.sin(angle * 3) * (ringRadius * 0.1);
                    const radius = ringRadius + spiralOffset;
                    
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    
                    if (j === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();
            }
            
            // Add energy tendrils reaching into the void
            for (let i = 0; i < 8; i++) {
                const angle = (Math.PI * 2 * i) / 8;
                const tendrilLength = PORTAL_SIZE * 1.5;
                
                const startX = centerX + Math.cos(angle) * (PORTAL_SIZE * 0.3);
                const startY = centerY + Math.sin(angle) * (PORTAL_SIZE * 0.3);
                const endX = centerX + Math.cos(angle) * tendrilLength;
                const endY = centerY + Math.sin(angle) * tendrilLength;
                
                const tendrilGradient = ctx.createLinearGradient(startX, startY, endX, endY);
                tendrilGradient.addColorStop(0, 'rgba(255, 0, 100, 0.8)');
                tendrilGradient.addColorStop(0.5, 'rgba(200, 0, 150, 0.6)');
                tendrilGradient.addColorStop(1, 'rgba(100, 0, 200, 0.2)');
                
                ctx.strokeStyle = tendrilGradient;
                ctx.lineWidth = 3;
                ctx.lineCap = 'round';
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();
            }
            
            // Add swirling energy tendrils in the center
            for (let i = 0; i < 12; i++) {
                const angle = (Math.PI * 2 * i) / 12;
                
                ctx.beginPath();
                ctx.strokeStyle = `rgba(255, 100, 200, 0.6)`;
                ctx.lineWidth = 2;
                ctx.lineCap = 'round';
                
                // Create tight spiral tendrils
                for (let j = 0; j <= 50; j++) {
                    const t = j / 50;
                    const spiralAngle = angle + t * Math.PI * 4;
                    const radius = PORTAL_SIZE * 0.1 * Math.exp(t * 1.5);
                    
                    const x = centerX + Math.cos(spiralAngle) * radius;
                    const y = centerY + Math.sin(spiralAngle) * radius;
                    
                    if (j === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();
            }
            
            // Add void center - pure black
            ctx.fillStyle = 'rgba(0, 0, 0, 1)';
            ctx.beginPath();
            ctx.arc(centerX, centerY, PORTAL_SIZE * 0.15, 0, Math.PI * 2);
            ctx.fill();
            
            return PIXI.Texture.from(canvas);
        }

        createEyes() {
            // Single centered eye - much scarier and more menacing
            this.centeredEye = this.createScaryEyeTexture();
            this.centeredEye.x = Graphics.width / 2;
            this.centeredEye.y = Graphics.height / 2;
            this.centeredEye.alpha = 0;
            this.container.addChild(this.centeredEye);
        }

        createScaryEyeTexture() {
            const canvas = document.createElement('canvas');
            canvas.width = 200;
            canvas.height = 200;
            const ctx = canvas.getContext('2d');
            
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // Parse eye color
            const eyeColor = EYE_COLOR.startsWith('#') ? EYE_COLOR : '#FF0000';
            
            // Massive terrifying eye glow - much larger and more intense
            const outerGlow = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, 100);
            outerGlow.addColorStop(0, eyeColor);
            outerGlow.addColorStop(0.2, eyeColor + 'FF');
            outerGlow.addColorStop(0.4, eyeColor + 'CC');
            outerGlow.addColorStop(0.7, eyeColor + '80');
            outerGlow.addColorStop(1, 'rgba(0, 0, 0, 0)');
            
            ctx.fillStyle = outerGlow;
            ctx.beginPath();
            ctx.arc(centerX, centerY, 100, 0, Math.PI * 2);
            ctx.fill();
            
            // Inner intense glow
            const innerGlow = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, 60);
            innerGlow.addColorStop(0, eyeColor);
            innerGlow.addColorStop(0.3, eyeColor + 'FF');
            innerGlow.addColorStop(0.7, eyeColor + 'CC');
            innerGlow.addColorStop(1, eyeColor + '80');
            
            ctx.fillStyle = innerGlow;
            ctx.beginPath();
            ctx.arc(centerX, centerY, 60, 0, Math.PI * 2);
            ctx.fill();
            
            // Dark sclera for more menacing look
            ctx.fillStyle = '#000000';
            ctx.beginPath();
            ctx.arc(centerX, centerY, 45, 0, Math.PI * 2);
            ctx.fill();
            
            // Darker iris with intense gradient
            const irisGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, 35);
            irisGradient.addColorStop(0, eyeColor);
            irisGradient.addColorStop(0.3, eyeColor + 'FF');
            irisGradient.addColorStop(0.6, eyeColor + 'CC');
            irisGradient.addColorStop(0.9, eyeColor + '80');
            irisGradient.addColorStop(1, '#000000');
            
            ctx.fillStyle = irisGradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, 35, 0, Math.PI * 2);
            ctx.fill();
            
            // Massive evil slit pupil - much larger and more terrifying
            ctx.fillStyle = '#000000';
            ctx.beginPath();
            ctx.ellipse(centerX, centerY, 8, 25, 0, 0, Math.PI * 2);
            ctx.fill();
            
            // Inner void pupil
            ctx.fillStyle = '#000000';
            ctx.beginPath();
            ctx.ellipse(centerX, centerY, 4, 15, 0, 0, Math.PI * 2);
            ctx.fill();
            
            // Deepest void center
            ctx.fillStyle = '#000000';
            ctx.beginPath();
            ctx.arc(centerX, centerY, 2, 0, Math.PI * 2);
            ctx.fill();
            
            // Add terrifying energy corona - more intense
            for (let i = 0; i < 12; i++) {
                const angle = (Math.PI * 2 * i) / 12;
                const rayLength = 60;
                const startX = centerX + Math.cos(angle) * 40;
                const startY = centerY + Math.sin(angle) * 40;
                const endX = centerX + Math.cos(angle) * rayLength;
                const endY = centerY + Math.sin(angle) * rayLength;
                
                const rayGradient = ctx.createLinearGradient(startX, startY, endX, endY);
                rayGradient.addColorStop(0, eyeColor);
                rayGradient.addColorStop(0.3, eyeColor + 'CC');
                rayGradient.addColorStop(0.7, eyeColor + '80');
                rayGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
                
                ctx.strokeStyle = rayGradient;
                ctx.lineWidth = 3;
                ctx.lineCap = 'round';
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();
            }
            
            // Add secondary energy spikes for extra terror
            for (let i = 0; i < 8; i++) {
                const angle = (Math.PI * 2 * i) / 8 + Math.PI / 8;
                const rayLength = 40;
                const startX = centerX + Math.cos(angle) * 50;
                const startY = centerY + Math.sin(angle) * 50;
                const endX = centerX + Math.cos(angle) * rayLength;
                const endY = centerY + Math.sin(angle) * rayLength;
                
                const rayGradient = ctx.createLinearGradient(startX, startY, endX, endY);
                rayGradient.addColorStop(0, eyeColor + '80');
                rayGradient.addColorStop(0.5, eyeColor + '40');
                rayGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
                
                ctx.strokeStyle = rayGradient;
                ctx.lineWidth = 2;
                ctx.lineCap = 'round';
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();
            }
            
            return new PIXI.Sprite(PIXI.Texture.from(canvas));
        }

        createDarknessOverlay() {
            this.darknessOverlay = new PIXI.Graphics();
            this.darknessOverlay.beginFill(0x000000, 0);
            this.darknessOverlay.drawRect(0, 0, Graphics.width, Graphics.height);
            this.darknessOverlay.endFill();
            this.darknessContainer.addChild(this.darknessOverlay);
        }

        createEnergyWaves() {
            // Create multiple layers of energy waves with different colors
            for (let i = 0; i < 5; i++) {
                const wave = new PIXI.Graphics();
                const hue = (i * 60) % 360;
                const color = this.hslToHex(hue, 100, 50);
                wave.lineStyle(3 + i, color, 0);
                wave.drawCircle(0, 0, PORTAL_SIZE * (0.3 + i * 0.3));
                wave.x = Graphics.width / 2;
                wave.y = Graphics.height / 2;
                this.energyWaves.push(wave);
                this.container.addChild(wave);
            }
        }
        
        hslToHex(h, s, l) {
            h /= 360;
            s /= 100;
            l /= 100;
            
            const c = (1 - Math.abs(2 * l - 1)) * s;
            const x = c * (1 - Math.abs((h * 6) % 2 - 1));
            const m = l - c / 2;
            let r = 0, g = 0, b = 0;
            
            if (0 <= h && h < 1/6) {
                r = c; g = x; b = 0;
            } else if (1/6 <= h && h < 1/3) {
                r = x; g = c; b = 0;
            } else if (1/3 <= h && h < 1/2) {
                r = 0; g = c; b = x;
            } else if (1/2 <= h && h < 2/3) {
                r = 0; g = x; b = c;
            } else if (2/3 <= h && h < 5/6) {
                r = x; g = 0; b = c;
            } else if (5/6 <= h && h <= 1) {
                r = c; g = 0; b = x;
            }
            
            const rHex = Math.round((r + m) * 255).toString(16).padStart(2, '0');
            const gHex = Math.round((g + m) * 255).toString(16).padStart(2, '0');
            const bHex = Math.round((b + m) * 255).toString(16).padStart(2, '0');
            
            return parseInt(rHex + gHex + bHex, 16);
        }

        createParticleSystems() {
            this.energyParticles = [];
            for (let i = 0; i < 100; i++) {
                const particle = this.createEnergyParticle();
                particle.x = Graphics.width / 2 + (Math.random() - 0.5) * PORTAL_SIZE * 2;
                particle.y = Graphics.height / 2 + (Math.random() - 0.5) * PORTAL_SIZE * 2;
                particle.vx = (Math.random() - 0.5) * 3;
                particle.vy = (Math.random() - 0.5) * 3;
                particle.life = 300 + Math.random() * 300;
                particle.maxLife = particle.life;
                particle.size = 1 + Math.random() * 4;
                
                this.energyParticles.push(particle);
                this.particleContainer.addChild(particle);
            }
        }

        createEnergyParticle() {
            const graphics = new PIXI.Graphics();
            const colors = [0xFF0000, 0xFF6600, 0xFF0066, 0x6600FF, 0x00FFFF];
            const color = colors[Math.floor(Math.random() * colors.length)];
            
            // Particle glow
            graphics.beginFill(color, 0.6);
            graphics.drawCircle(0, 0, 4);
            graphics.endFill();
            
            // Particle core
            graphics.beginFill(0xFFFFFF, 0.8);
            graphics.drawCircle(0, 0, 2);
            graphics.endFill();
            
            return graphics;
        }

        start() {
            this.active = true;
            this.timer = 0;
            this.progress = 0;
            
            this.container.visible = true;
            this.darknessContainer.visible = true;
            this.particleContainer.visible = true;
        }

        stop() {
            if (!this.active) return;
            
            this.active = false;
            this.clearAllTimeouts();
            
            // Clean up arrays
            this.energyParticles = [];
            this.energyWaves = [];
        }

        update() {
            if (!this.active) return;
            
            this.timer++;
            this.animationTimer++;
            this.updatePortalProgress();
            this.updateVisualEffects();
            this.updateParticles();
            
            if (this.timer >= this.duration) {
                this.stop();
            }
        }

        updatePortalProgress() {
            this.progress = this.timer / this.duration;
        }

        updateVisualEffects() {
            // Portal opening animation with swirling effect
            if (this.portalSprite) {
                const openProgress = Math.min(1, this.progress * 2); // Open in first half
                this.portalSprite.alpha = openProgress * this.intensity;
                this.portalSprite.scale.set(0.1 + openProgress * 0.9);
                
                // Add swirling rotation to the portal
                this.portalSprite.rotation = this.animationTimer * 0.02; // Slow, hypnotic swirl
            }
            
            // Single centered eye appears after portal opens - static and terrifying
            if (this.centeredEye) {
                const eyeProgress = Math.max(0, (this.progress - 0.3) / 0.4); // Appear 30%-70%
                const eyeAlpha = eyeProgress * this.intensity;
                
                this.centeredEye.alpha = eyeAlpha;
                // No movement - just static, menacing presence
            }
            
            // Energy waves with dramatic expansion
            this.energyWaves.forEach((wave, index) => {
                const waveProgress = Math.max(0, (this.progress - 0.2) / 0.6);
                const waveAlpha = waveProgress * this.intensity * (1 - index * 0.15);
                wave.alpha = waveAlpha;
                
                const waveScale = 0.3 + waveProgress * 2.5;
                wave.scale.set(waveScale);
                
                // Add rotation to waves
                wave.rotation = this.animationTimer * 0.02 + index * 0.5;
            });
            
            // Darkness overlay
            if (this.darknessOverlay) {
                const darknessProgress = this.progress * this.intensity * 0.5;
                this.darknessOverlay.alpha = darknessProgress;
            }
            
            // Scene tint
            if (SceneManager._scene && SceneManager._scene.background) {
                const tintProgress = this.progress * this.intensity;
                const redTint = Math.floor(255 * (1 - tintProgress * 0.3));
                const greenTint = Math.floor(255 * (1 - tintProgress * 0.5));
                const blueTint = Math.floor(255 * (1 - tintProgress * 0.7));
                
                SceneManager._scene.background.tint = (redTint << 16) | (greenTint << 8) | blueTint;
            }
        }

        updateParticles() {
            for (let i = this.energyParticles.length - 1; i >= 0; i--) {
                const particle = this.energyParticles[i];
                
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life--;
                
                // Update particle alpha based on life
                if (particle.maxLife && particle.maxLife > 0) {
                    particle.alpha = Math.max(0, Math.min(1, particle.life / particle.maxLife));
                }
                
                // Add subtle rotation to particles
                particle.rotation += 0.1;
                
                if (particle.life <= 0) {
                    this.particleContainer.removeChild(particle);
                    this.energyParticles.splice(i, 1);
                    
                    // Create new particle with more dramatic positioning
                    const newParticle = this.createEnergyParticle();
                    const angle = Math.random() * Math.PI * 2;
                    const radius = PORTAL_SIZE * (0.5 + Math.random() * 1.5);
                    newParticle.x = Graphics.width / 2 + Math.cos(angle) * radius;
                    newParticle.y = Graphics.height / 2 + Math.sin(angle) * radius;
                    newParticle.vx = (Math.random() - 0.5) * 4;
                    newParticle.vy = (Math.random() - 0.5) * 4;
                    newParticle.life = 400 + Math.random() * 400;
                    newParticle.maxLife = newParticle.life;
                    
                    this.energyParticles.push(newParticle);
                    this.particleContainer.addChild(newParticle);
                }
                
                // Enhanced wrap around portal with spiral effect
                const centerX = Graphics.width / 2;
                const centerY = Graphics.height / 2;
                const distance = Math.sqrt((particle.x - centerX) ** 2 + (particle.y - centerY) ** 2);
                
                if (distance > PORTAL_SIZE * 2) {
                    const angle = Math.atan2(particle.y - centerY, particle.x - centerX);
                    const newRadius = PORTAL_SIZE * 0.5;
                    particle.x = centerX + Math.cos(angle) * newRadius;
                    particle.y = centerY + Math.sin(angle) * newRadius;
                    
                    // Add spiral velocity
                    particle.vx += Math.cos(angle + Math.PI/2) * 0.5;
                    particle.vy += Math.sin(angle + Math.PI/2) * 0.5;
                }
            }
        }

        clearAllTimeouts() {
            for (const timeoutId of this.pendingTimeouts) {
                clearTimeout(timeoutId);
            }
            this.pendingTimeouts.clear();
        }
    }

    // Portal Scene Class
    class Scene_DarkPortal extends Scene_Base {
        constructor() {
            super();
            this.portal = null;
            this.background = null;
            this.timer = 0;
            this.duration = DURATION;
        }

        create() {
            super.create();
            this.createBackground();
            this.createPortal();
            this.startPortal();
        }

        createBackground() {
            this.background = new PIXI.Container();
            
            this.skyBackground = new PIXI.Graphics();
            this.skyBackground.beginFill(0x000000);
            this.skyBackground.drawRect(0, 0, Graphics.width, Graphics.height);
            this.skyBackground.endFill();
            this.background.addChild(this.skyBackground);
            
            this.addChild(this.background);
        }

        createPortal() {
            this.portal = new DarkPortalGoddess();
            this.portal.scene = this;
            this.addChild(this.portal.container);
            this.addChild(this.portal.darknessContainer);
            this.addChild(this.portal.particleContainer);
        }

        startPortal() {
            this.portal.start();
            this.timer = 0;
        }

        update() {
            super.update();
            
            if (this.portal && this.portal.active) {
                this.portal.update();
                this.timer++;
                
                if (this.timer >= this.duration) {
                    this.completePortal();
                }
            }
        }

        completePortal() {
            this.exitPortal();
        }

        exitPortal() {
            try {
                if (this.portal) {
                    this.portal.stop();
                }
                SceneManager.pop();
            } catch (e) {
                console.log('Error exiting portal:', e);
                SceneManager.pop();
            }
        }
    }

    // Plugin functions
    function startPortal() {
        SceneManager.push(Scene_DarkPortal);
    }

    function stopPortal() {
        if (SceneManager._scene instanceof Scene_DarkPortal) {
            SceneManager.pop();
        }
    }

    // Add to global scope for external access
    window.DarkPortalGoddess = {
        start: startPortal,
        stop: stopPortal,
        getInstance: () => SceneManager._scene instanceof Scene_DarkPortal ? SceneManager._scene.portal : null
    };

})(); 