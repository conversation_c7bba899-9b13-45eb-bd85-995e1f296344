[null, {"id": 1, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 301, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Go<PERSON>licer", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: gobjobless sheet>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Goblin Slicer is at the very bottom of the goblin hierarchy. They craft crude blades from stone in order to defend their territory from intruders, but other than that they are basically feral..\nThey communicate through grunts and snarls, and their idea of a good time is wrestling with each other in the mud. They're not exactly the most social creatures, but they do form tight-knit packs with their fellow Goblin Slicers. And let's face it, with those jagged teeth and wild eyes, you wouldn't want to be their friend anyways. If you see a Goblin Slicer in the wild, it's best to just turn around and run the other way... or bring a really big stick.\n</Bestiary Lore>\n\n", "params": [150, 0, 30, 20, 20, 20, 30, 20]}, {"id": 2, "actions": [{"skillId": 302, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 339, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.1}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Goblin Hunter", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: gobarcher_0003_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nGoblin Hunters use bows made from tree branches, and are adept at tracking their prey for many miles. Their weapons may be primitive, but they know how to use them with deadly precision. The Goblin Hunters are the bane of any traveler, as they strike from the shadows and disappear just as quickly. Some say they have a special connection with the forest, and can communicate with the animals that live within it. Others say they are just really good at hiding behind trees.\n</Bestiary Lore>", "params": [200, 0, 20, 20, 20, 20, 30, 20]}, {"id": 3, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 303, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 102, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.1}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Goblin Shaman", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: gobmage_0000_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Goblin Shaman is a spiritual leader to his tribe, able to call upon the rudimentary power of fire using forbidden rituals.\nSome say the Goblin Shaman's powers come from the spirits of their ancestors, while others believe it's just indigestion from eating too many moldy mushrooms. They can summon flames that would make a dragon jealous, and their screams can shatter even the bravest warrior's nerves. Despite their fearsome reputation, the Goblin Shaman is a respected figure among their tribe. They act as judge, jury, and executioner, and their word is law. Just make sure you're not on their bad side, or you might end up being the main course at their next feast.\n</Bestiary Lore>\n", "params": [150, 0, 20, 20, 30, 20, 25, 20]}, {"id": 4, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 20, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 35, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "<PERSON> Troll", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: troll_0000_green>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nLegend has it that Hill Trolls were once sophisticated beings with a love for poetry and fine cuisine, but somewhere along the way they hit their heads a few too many times and lost most of their brain cells. Now they spend their days hitting rocks with their boulder clubs and grunting at each other. Living under bridges is a natural fit for these lumbering beasts, as they don't have to worry about pesky things like rent or property taxes. Their diet consists mainly of whatever small animals they can catch, and occasionally a careless adventurer who happens to wander too close. If you're ever on a journey through the hills, be sure to keep an eye out for these simple-minded giants. Just don't try to engage them in conversation, or you might end up with a boulder-sized headache.\n</Bestiary Lore>", "params": [250, 0, 30, 25, 20, 20, 20, 20]}, {"id": 5, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 305, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 64, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Goblin Captain", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: gobwar_0000_silver>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Goblin Captains are a force to be reckoned with on the battlefield. They have a knack for stealing the best weapons and armor from their fallen enemies, and wearing it as a badge of honor. But it's not just their shiny gear that makes them stand out. Their battle cries are enough to make even the bravest warrior quake in their boots, and their leadership skills are unparalleled. They can rally their fellow goblins into a frenzy, and even bring fallen comrades back into battle with a swift kick to the rear. If you ever find yourself facing a Goblin Captain, be prepared for a fierce fight. And maybe bring some extra padding for your backside, just in case.\n</Bestiary Lore>", "params": [250, 0, 30, 25, 20, 20, 20, 20]}, {"id": 6, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 306, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 33, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "<PERSON><PERSON>ultist", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: gobmage_0002_purple>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThese twisted beings have forsaken all that is good and pure, choosing instead to embrace the shadows and the forbidden knowledge that lies within. Goblin Warlocks are shrouded in a cloud of dark magic, and their eyes glow with an eerie green light. They can summon creatures from the netherworld to do their bidding, and twist the minds of their enemies with spells that cause hallucinations and nightmares. Many goblin tribes fear the Warlocks, as they have been known to turn on their own kind if it suits their purposes. It's said that if you cross a Goblin Warlock, they will curse you with a terrible fate worse than death. So if you ever find yourself face to face with one of these malevolent creatures, it's best to tread carefully... or run for your life.\n</Bestiary Lore>", "params": [150, 0, 20, 25, 30, 20, 20, 20]}, {"id": 7, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 131, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 307, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Goblin Geomancer", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: gobmage_0002_geomancer>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nGoblin Geomancers are in tune with the natural world, and they possess an innate ability to communicate with the elements of the earth and water. They are the healers of their tribe, using their knowledge of herbs, crystals, and minerals to cure a wide variety of ailments. They are also responsible for maintaining the delicate balance of nature in their territory, ensuring that the plants and animals are healthy and thriving. Despite their gentle nature, Goblin Geomancers are not to be underestimated in battle. They can call forth powerful earthquakes and tidal waves to crush their enemies, and their mastery of the earth and water can make them nearly invincible in the right circumstances. Just be careful not to step on any plants or disturb any animals in their presence, or you may face their wrath.\n</Bestiary Lore>", "params": [150, 0, 20, 25, 30, 20, 20, 20]}, {"id": 8, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 308, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 15, "rating": 5, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.25}, {"skillId": 345, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Goblin Ambusher", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: gobrogue_0000_dark>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Goblin Ambusher is the ultimate coward. They don't have the guts to face you head-on, so they resort to throwing sand in your eyes and stealing your gold like a common street thug. If you ever find yourself facing an Ambusher, just make sure you've got some goggles and a good grip on your coin purse. These little buggers are slippery, and they'll do anything to avoid a fair fight. And if you do manage to catch one, don't be surprised if they try to talk their way out of it. They'll beg for mercy, promise to turn over a new leaf, and then steal your boots when you're not looking. It's best to just give them a wide berth and let them go about their thieving ways. After all, they're just doing what goblins do best... being annoying little pests.\n</Bestiary Lore>", "params": [200, 0, 25, 20, 20, 20, 30, 20]}, {"id": 9, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 344, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 321, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Goblin Knight", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: gobdragoon_0003_blue>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Goblin Knight is a walking contradiction. On one hand, they're tiny, green, and not particularly intimidating. On the other hand, they're decked out in shiny armor and wielding a pointy spear that looks like it could skewer a small horse. It's like watching a Chihuahua wearing a suit of armor trying to take down a Great Dane. But don't let their comical appearance fool you... they're still dangerous. They may not be able to swing very hard, but they make up for it with sheer determination and a willingness to throw themselves into battle without a second thought. And if they ever do manage to get a lucky hit in, it'll feel like you got stung by a hornet... a really, really angry hornet.\n</Bestiary Lore>", "params": [250, 0, 25, 30, 20, 20, 25, 20]}, {"id": 10, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 324, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 41, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Goblin Warhound", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: armoredwolf_0004_silver>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe goblin warhound is a fearsome creature that resembles a cross between a wolf and a hyena. Its mangy fur is a mottled mix of brown and gray, and its sharp teeth gleam in the light with a menacing glint. Its eyes are yellow and intelligent, betraying a cunning and ferocious nature. It is a loyal and obedient companion to its goblin masters, who use them to hunt and track down their enemies. They are trained from birth to be savage killers, and are often used as shock troops in goblin armies. Their powerful jaws can crush bones with ease, and their claws are razor-sharp, capable of tearing through armor and flesh alike. And they're surprisingly good at fetching, as long as you're throwing something they're interested in, like a chunk of human flesh or a particularly juicy bone.\n</Bestiary Lore>", "params": [200, 0, 30, 20, 20, 20, 25, 20]}, {"id": 11, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 346, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 27, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 130, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Stinkbug", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: beetle_0000_green>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Stinkbug is a fearsome creature that strikes terror into the hearts of even the bravest adventurers... or at least, it would, if anyone could get close enough to smell it. This monstrous insect is easily recognizable by its massive horn, which it uses to skewer prey and ward off predators. But what really sets the Stinkbug apart is its odor. Legend has it that the stench of its foul-smelling gas can knock out a dragon at 50 paces, and it's certainly enough to make any human or elf wish they were somewhere else. If you ever encounter a giant horned stinkbug in the wild, be sure to hold your nose and keep your distance... or bring a lot of air freshener.\n</Bestiary Lore>", "params": [150, 0, 25, 20, 30, 20, 20, 20]}, {"id": 12, "actions": [{"skillId": 312, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 318, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 359, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Sporeleaf Stinger", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: plant_0001_blue>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Sporeleaf Stinger - nature's way of saying \"I'm sorry, did you need that hand?\" If you see one of these bad boys, run the other way as fast as you can. Or, if you're feeling adventurous, go ahead and touch one - just be prepared for the kind of pain that makes childbirth look like a walk in the park. And whatever you do, don't even think about using those flowers to impress your sweetheart - unless they're really into excruciating agony, in which case, hey, you do you. Because what's the point of a beautiful flower if it doesn't also have the power to ruin your entire week? If you see these guys in the brush, it's best to give them a wide berth, unless of course you're in the market for some top-notch pain management. Just be sure to bring some tissues and a strong sense of regret.\n</Bestiary Lore>", "params": [150, 0, 25, 25, 20, 20, 25, 20]}, {"id": 13, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 313, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 319, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 75, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "<PERSON> Spider", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: spider_0004_green>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nForest Spiders - because why settle for the comforting notion that tiny spiders might be crawling around your house, when you could be imagining a giant, camouflaged spider lurking in the woods, waiting to pounce on unsuspecting prey? The Forest Spider is a creature straight out of a nightmare. This eight-legged arachnid is the size of a small dog and can spin webs strong enough to catch even the bravest of adventurers. But don't worry, it's not all bad news - this spider has a great sense of humor. In fact, it's been known to spin webs that say things like \"Welcome to my parlor\" and \"Gotcha!\" just to give its prey a good chuckle before devouring them.\n</Bestiary Lore>", "params": [200, 0, 30, 25, 20, 20, 20, 20]}, {"id": 14, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 316, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 26, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "G<PERSON><PERSON><PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: Bat_0002_brown>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nPart bird, part demon, and all nightmare fuel. These little guys may seem harmless enough, until you realize that they're not just perching on high places for the view. Nope, they're waiting for their next meal to come along, and when it does, they strike like a feathered lightning bolt, turning their prey to stone with a glance. So if you see a gargoyle perched on a nearby building, it's best to keep your head down and run like the wind, unless of course you're in the market for a new lawn ornament.\n</Bestiary Lore>", "params": [200, 0, 30, 30, 20, 20, 20, 20]}, {"id": 15, "actions": [{"skillId": 30, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 315, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 359, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Toxic Nightshade", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: plant_0003_violet>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nToxic Nightshade a plant so deadly, it makes poison ivy look like a mild case of the sniffles. With its vibrant purple petals and ominous aura of darkness, this plant is not to be trifled with. And if the deadly poison doesn't get you, the hallucinations will - suddenly, even your closest friends and family will look like bloodthirsty monsters, and you'll find yourself running for your life through a landscape straight out of your worst nightmares. So if you're thinking about taking a shortcut through the woods, just remember - the shortest route isn't always the safest. Good luck out there!\n</Bestiary Lore>", "params": [150, 0, 25, 25, 20, 20, 25, 20]}, {"id": 16, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 37, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Red Kobold", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: kobold_0000_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Red Kobold is a fiery little reminder that sometimes, even the runts of the dragon family tree can still pack a mean punch. Don't let their diminutive size fool you - these little guys have got enough aggression to make even a berserker blush. And if you see smoke coming out of their nostrils? Well, let's just say you don't want to stick around to find out what happens next. So if you happen upon a pack of Red Kobolds in your travels, it's best to just turn around and run - or, you know, invest in some fireproof gear. Your call!\n</Bestiary Lore>", "params": [300, 0, 20, 20, 20, 20, 20, 20]}, {"id": 17, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 38, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Blue Kobold", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: kobold_0002_blue>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nAh, the Blue Kobold - the thinking kobold's kobold. While their Red cousins are busy breathing fire and smashing things, the Blue Kobolds are busy tapping into the frigid power of ice. With their mastery of the arcane arts, they're the closest thing the kobold world has to a brain trust. But don't be fooled by their intelligence - these guys can still pack a mean punch when the going gets tough. So if you're looking for a fight with a Blue Kobold, just be prepared to dodge a few icicles along the way. Stay frosty, my friend!\n</Bestiary Lore>", "params": [300, 0, 20, 20, 20, 20, 20, 20]}, {"id": 18, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 346, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 44, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "<PERSON><PERSON><PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: koboldcleric_0002_green>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Kobold Healer is proof that even the most ruthless of creatures can have a heart of gold. Legend has it that the first Kobold Healer was once a fearsome warrior who bested a Paladin in combat and made off with their spell book. But instead of using it for evil, this little guy had a change of heart and decided to dedicate his life to healing others. And thus, the Kobold Healer was born. So if you find yourself in need of a little magical TLC, just seek out one of these pint-sized miracle workers - they may have a bit of a checkered past, but they've got a heart of gold. Or at least, a heart of something shiny and valuable.\n</Bestiary Lore>", "params": [250, 0, 20, 20, 20, 20, 20, 20]}, {"id": 19, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 344, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 347, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "<PERSON><PERSON>d <PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: koboldwarrior_0003_green>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Kobold Dragoon is proof that sometimes, the simplest weapons are the deadliest. Armed with nothing but a pointy stick and a whole lot of determination, these little guys are not to be underestimated. Sure, they may not have the fancy spells of the Kobold Healers or the fiery breath of the Red Kobolds, but they've got something even more important - a willingness to stab anything that moves. So if you find yourself face-to-face with a pack of Kobold Spearmen, just remember - they may be small, but they're fierce. And pointy. Very, very pointy.\n</Bestiary Lore>", "params": [350, 0, 20, 20, 20, 20, 20, 20]}, {"id": 20, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 342, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 71, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "<PERSON><PERSON> Frog", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: frog_0000_blue>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nDespite its diminutive size, the Dart Frog is not to be trifled with. This tiny terror is the bane of all who dare cross its path. Its skin is so poisonous that even a mere touch could leave you feeling like you just went on a three-day bender with a group of rowdy pirates. And if their poisonous skin doesn't get you, their powerful legs will - these guys can jump and kick with the best of them. So if you see a Dart Frog hopping your way, it's best to just steer clear - unless, of course, you're in the market for a one-way trip to the Great Beyond. But hey, at least you'll have a good story to tell in the afterlife, right?\n</Bestiary Lore>", "params": [200, 0, 20, 20, 20, 20, 25, 20]}, {"id": 21, "actions": [{"skillId": 149, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 68, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.2}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Shadow Spirit", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 11 To 15: 10%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Size: 64, 200>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: ghost_0004_dark>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Shadow Spirit is a truly eerie and unsettling creature, a whisper from the darkest corners of the cosmos. Its ghostly form seems to flicker and waver like a candle in the wind, and its eyes burn with an otherworldly light that chills the soul. Legend has it that the Shadow Spirit is a lost soul from another realm, forever doomed to wander the void between worlds. It is said that the creature was once a powerful being of great intelligence and cunning, but its mind has been twisted and corrupted by its long years in the void. Now, it exists only to spread chaos and destruction wherever it goes, haunting the dreams of the innocent and feeding on their fears.\n</Bestiary Lore>", "params": [150, 0, 15, 15, 15, 5, 15, 15]}, {"id": 22, "actions": [{"skillId": 131, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 66, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.2}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Earth Spirit", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 11 To 15: 10%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Size: 64, 200>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: ghost_0002_brown>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Earth Spirit is a terrifying creature that haunts the soil and rocks of the land. Its translucent body appears as if made of dust and stone, and it moves with a silent grace that belies its earthy guise. Legends say that it was created when an ancient earth wisp fused with the soul of a human, creating a being that is both ethereal and corporeal.\nDespite its terrifying reputation, the Earth Spirit is said to possess great wisdom and knowledge of the earth's secrets. Some brave adventurers have sought it out, hoping to learn from its ancient wisdom, but few have returned to tell the tale. To most, the Earth Spirit remains a creature of myth and legend, feared and revered by all who hear its name.\n</Bestiary Lore>\n", "params": [150, 0, 15, 15, 15, 5, 15, 15]}, {"id": 23, "actions": [{"skillId": 102, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 61, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.2}], "gold": 50, "name": "Fire Spirit", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 11 To 15: 10%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Size: 64, 200>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: ghost_0001_fire>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\n\nThe Fire Spirit is a fearsome creature of legend, said to be born from the blazes of a great inferno. It appears as a fiery apparition, with flickering embers for hair and smoldering eyes that burn like coals. Its body is a swirling vortex of flames, and it leaves a trail of scorch marks wherever it goes.\nThe Fire Ghost is known for its ability to conjure and control flames, and it delights in setting things ablaze. It is said that it can create walls of fire to trap its prey, and that it can even make its entire body explode into a fiery burst, incinerating anything in its path.\n</Bestiary Lore>", "params": [150, 0, 15, 15, 15, 5, 15, 15]}, {"id": 24, "actions": [{"skillId": 143, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 67, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.2}], "gold": 50, "name": "Light Spirit", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 11 To 15: 10%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Size: 64, 200>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: ghost_0000_light>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Light Spirit is a spectral creature that appears to be made of pure white light. It glows softly and emits a faint humming sound as it moves, which can be both mesmerizing and eerie to those who encounter it. Despite its ethereal appearance, the Light Spirit is a formidable foe in battle, as it can use its powers to blind and disorient its opponents, leaving them vulnerable to its attacks. Legends say that the Light Spirit is the remnant of a powerful Paladin who was betrayed by their own kind and now seeks revenge on all who cross its path.\n</Bestiary Lore>", "params": [150, 0, 15, 15, 15, 5, 15, 15]}, {"id": 25, "actions": [{"skillId": 110, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 336, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 64, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.2}], "gold": 50, "name": "Ice Spirit", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 11 To 15: 10%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Size: 64, 200>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: ghost_0000_ice>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nAs it moves, the Ice Spirit leaves behind a trail of frost and chilling winds, freezing everything in its path. Its touch can instantly freeze any living being, leaving them trapped in an icy prison.\nSome say that the Ice Spirit is the embodiment of the harsh winter season, seeking to freeze the world in an endless icy grip. Others believe that it is the vengeful spirit of someone who died in the cold, seeking to exact revenge on the living. Regardless of its origins, the Ice Ghost is a creature to be feared and avoided at all costs.\n</Bestiary Lore>", "params": [150, 0, 15, 15, 15, 5, 15, 15]}, {"id": 26, "actions": [{"skillId": 118, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 62, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.2}], "gold": 50, "name": "Thunder Spirit", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 11 To 15: 10%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Size: 64, 200>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: ghost_0001_thunder>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Thunder Spirit appears as a shimmering, ghostly figure surrounded by crackling bolts of lightning, and can move with incredible speed and agility. You will know one is nearby when the hairs on the back of your neck start to stand on end. It is said that the lightning ghost feeds on the energy of thunderstorms, becoming stronger and more powerful with each bolt of lightning that strikes. Its mere presence can cause electrical devices to malfunction or short-circuit, and it is known to be deadly to those who come into direct contact with its electrified body. \n</Bestiary Lore>", "params": [150, 0, 15, 15, 15, 5, 15, 15]}, {"id": 27, "actions": [{"skillId": 125, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 65, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.2}], "gold": 50, "name": "Water Spirit", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 11 To 15: 10%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Size: 64, 200>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: ghost_0000_water>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nThe Water Spirit is a terrifying creature that haunts bodies of water, whether they be oceans, rivers, or lakes. It is said to have the ability to control water, conjuring up waves and currents to drown unsuspecting victims. Legends say that the Water Spirit is the vengeful memory of someone who drowned in the waters it now haunts. Its eerie laughter can be heard echoing across the water, sending shivers down the spines of those who hear it.\nThose who are foolish enough to venture into the Water Spirit's territory may find themselves trapped in its watery grasp.\n</Bestiary Lore>", "params": [150, 0, 15, 15, 15, 5, 15, 15]}, {"id": 28, "actions": [{"skillId": 137, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 63, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.2}], "gold": 50, "name": "Wind Spirit", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 11 To 15: 10%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Size: 64, 200>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: ghost_0001_wind>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nA Wind Spirit is a spectral creature that haunts the skies, riding the winds with a grace and speed that belies its ethereal nature. These beings are often invisible to the naked eye, but their presence can be felt through the rush of air and the shifting of clouds. Their powers are closely tied to the wind, and they can summon gusts and squalls with a flick of their translucent fingers. They are said to be highly intelligent and possess a cunning that allows them to outmaneuver even the most skilled of opponents.\n</Bestiary Lore>", "params": [150, 0, 15, 15, 15, 5, 15, 15]}, {"id": 29, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 30, "actions": [], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "-----Beach", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 31, "actions": [{"skillId": 304, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 326, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 28, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 6, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Red Crab", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: crab sheet>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 2>\n<Bestiary Battleback 2: Desert>\n<Bestiary Lore>\nTen-legged, with bulging eyes and razor-sharp claws, the Red Crab is a fearsome creature that roams the sandy beaches of Cascadia. Its shell is as tough as steel and colored a vibrant red, like a lobster that's been sunburned for too long.\nIn fact, the Red Crab's main goal seems to be making everyone's day worse. It'll scuttle up to you, make rude gestures with its claws, and scuttle away cackling like a maniac.\nSo if you see a Red Crab on the beach, do yourself a favor and keep your distance. Or better yet, bring a big pot of boiling water and some butter, and turn that crab into a delicious seafood feast!\n</Bestiary Lore>", "params": [250, 0, 20, 20, 20, 20, 20, 20]}, {"id": 32, "actions": [{"skillId": 6, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 304, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 322, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 326, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Blue Crab", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: blue crab sheet>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 2>\n<Bestiary Battleback 2: Desert>\n<Bestiary Lore>\nLegend has it that the Blue Crab gained its magical powers after feasting on a particularly potent batch of seaweed. Now, it spends its days lurking in the shallow waters, waiting for unsuspecting prey to pass by. With a wave of its pincers, it can conjure up a magical bubble that will leave you breathless... literally. If you're brave enough to face this bubble-casting crustacean, be sure to bring plenty of waterproof gear.\n</Bestiary Lore>", "params": [350, 0, 20, 20, 20, 20, 20, 20]}, {"id": 33, "actions": [{"skillId": 19, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 318, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 71, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Reefstalker", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: fishman rogue blue>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 2>\n<Bestiary Battleback 2: Desert>\n<Bestiary Lore>\nThese jellyfish monsters are not bound by the limitations of water, and can move freely on land or in the air, trailing their long, glowing tentacles behind them. Their translucent bodies shimmer with iridescent colors, making them hypnotic to watch. But beware, for if you get too close, you'll feel the searing pain of their venomous stingers, which can cause hallucinations, paralysis, and even death. Some say that these monsters were created by a powerful mage, while others believe that they are the result of a curse or a mutation caused by dark magic.\n</Bestiary Lore>", "params": [300, 0, 20, 20, 20, 20, 20, 20]}, {"id": 34, "actions": [{"skillId": 322, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 42, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 309, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Sandcaster", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: fishman mage green>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 2>\n<Bestiary Battleback 2: Desert>\n<Bestiary Lore>\nThe Cuttlefish, otherwise known as the Miracle Fish, a rare and mystical creature that inhabits the depths of the ocean. Its bioluminescent abilities allow it to emit a soft, glowing light that has powerful healing properties.\nHowever, despite its peaceful nature and desire to aid others, the Luminescent Healer is not to be underestimated. Its body is covered in sharp, razor-like spines that it can use to defend itself from any would-be attackers.\n</Bestiary Lore>", "params": [250, 0, 20, 20, 20, 20, 20, 20]}, {"id": 35, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 36, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 37, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 38, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 39, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 40, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "-----<PERSON>", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 41, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 41, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 131, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Earth Wolgen", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: magicwolf_0005_earth>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 3>\n<Bestiary Battleback 2: Forest>\n<Bestiary Lore>\nThis fearsome creature is known for its rugged, brown fur that helps it blend seamlessly into the forest environment. Its sharp claws are capable of tearing through even the thickest of tree trunks, making it a formidable predator that strikes fear into the hearts of all who dare to cross its path.\nIn addition to its physical prowess, this earth wolf also possesses a unique ability to harness the power of the earth itself. With a flick of its tail or a roar from its mighty jaws, it can summon forth powerful spells that cause the ground to shake and shift beneath its enemies' feet.\n</Bestiary Lore>", "params": [300, 0, 30, 20, 20, 20, 20, 20]}, {"id": 42, "actions": [{"skillId": 358, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 324, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 359, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "<PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: plant_0004_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 3>\n<Bestiary Battleback 2: Forest>\n<Bestiary Lore>\nIts large, gaping maw is filled with razor-sharp teeth that are capable of grinding bone to dust. Its leaves are a vibrant shade of red, almost like fresh blood, and they are lined with small, needle-like hairs that inject a potent venom into its victims. This venom paralyzes its prey, making it easier for the plant to ensnare and consume them. The plant's tendrils are not only dangerous due to their sharpness, but also because they are incredibly strong, capable of crushing bones with ease. The red man eating plant is a feared creature among the forest's inhabitants, as it is a relentless predator that will stop at nothing to satiate its insatiable hunger.\n</Bestiary Lore>", "params": [350, 0, 25, 25, 20, 20, 20, 20]}, {"id": 43, "actions": [{"skillId": 309, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 137, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 311, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 31, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Dream Fairy", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 50%>\n<Visual Hover Effect>\n Base: 10\n Speed: 20\n Rate: 5.0\n Death: floor\n</Visual Hover Effect>\n<Sideview Battler: dream_fairy>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 3>\n<Bestiary Battleback 2: Forest>\n<Bestiary Lore>\nBeware, for once you fall asleep, it will feed on your soul, draining your energy and leaving you weak and vulnerable. With delicate wings and a hauntingly beautiful voice, the dream fairy lulls its prey into a deep slumber before descending upon them, consuming their dreams and leaving them feeling disoriented and drained upon waking. Its magical abilities also allow it to manipulate and distort dreams, leaving its victims in a state of perpetual nightmares. The only way to defend against the dream fairy is to stay alert and avoid its song.\n</Bestiary Lore>", "params": [250, 0, 25, 20, 25, 20, 25, 20]}, {"id": 44, "actions": [{"skillId": 102, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 358, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 326, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Charred Treant", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: treant_0001_treant-chared>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 3>\n<Bestiary Battleback 2: Forest>\n<Bestiary Lore>\n\nTheir bark is blackened and their leaves have withered to ash, leaving them with a haunting appearance. They are said to have been cursed by a powerful sorcerer, and now they wander the forest, setting ablaze anything in their path.\nIn addition to their fiery exterior, these treants are also skilled in the use of fire magic. Some say that they can even control the very flames of the forest, causing wildfires and wreaking havoc on the land.\n</Bestiary Lore>", "params": [350, 0, 20, 20, 30, 20, 20, 20]}, {"id": 45, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 41, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 125, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Water Wolgen", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: magicwolf_0002_water>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 3>", "params": [800, 0, 10, 10, 10, 10, 10, 10]}, {"id": 46, "actions": [{"skillId": 358, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 359, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 320, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 32, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "<PERSON>bell", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: plant_0002_yellow>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nToxic Nightshade a plant so deadly, it makes poison ivy look like a mild case of the sniffles. With its vibrant purple petals and ominous aura of darkness, this plant is not to be trifled with. And if the deadly poison doesn't get you, the hallucinations will - suddenly, even your closest friends and family will look like bloodthirsty monsters, and you'll find yourself running for your life through a landscape straight out of your worst nightmares. So if you're thinking about taking a shortcut through the woods, just remember - the shortest route isn't always the safest. Good luck out there!\n</Bestiary Lore>", "params": [600, 0, 10, 10, 10, 10, 10, 10]}, {"id": 47, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 31, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Wild Boar", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 300%>\n<Sideview Battler: wildboarbattler1>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 3>", "params": [1000, 0, 10, 10, 10, 10, 10, 10]}, {"id": 48, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "<PERSON> Bear", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 300%>\n<Sideview Battler: bear_0001_brown>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 3>", "params": [1000, 0, 10, 10, 10, 10, 10, 10]}, {"id": 49, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 342, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 71, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Owlbeast", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: owlbeast_0003_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Bestiary Lore>\nDespite its diminutive size, the Dart Frog is not to be trifled with. This tiny terror is the bane of all who dare cross its path. Its skin is so poisonous that even a mere touch could leave you feeling like you just went on a three-day bender with a group of rowdy pirates. And if their poisonous skin doesn't get you, their powerful legs will - these guys can jump and kick with the best of them. So if you see a Dart Frog hopping your way, it's best to just steer clear - unless, of course, you're in the market for a one-way trip to the Great Beyond. But hey, at least you'll have a good story to tell in the afterlife, right?\n</Bestiary Lore>", "params": [200, 0, 20, 20, 20, 20, 25, 20]}, {"id": 50, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "-----Underground Waterway", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 51, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 343, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 323, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 52, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Rolly-pede", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: crawler_0003_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 5>\n<Bestiary Battleback 1: tg-bbg-riv-01-a>\n<Bestiary Lore>\nThe Rolly-pede is a massive arthropod that can grow up to ten feet in length, with a segmented body covered in tough, chitinous plates. Its many legs are powerful and agile, allowing it to move quickly and smoothly through any terrain.\nOne of the Rolly-pede's most unusual features is its ability to curl itself into a ball, using its armor-plated segments as a protective shell. In this form, it is almost impervious to attack and can even roll like a wagon wheel, crushing anything in its path.\n</Bestiary Lore>", "params": [250, 0, 25, 20, 25, 20, 20, 20]}, {"id": 52, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 71, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 312, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Deathcap", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: shroom_0001_purp>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 5>\n<Bestiary Battleback 1: tg-bbg-riv-01-a>\n<Bestiary Lore>\nThe Deathcap mushroom is not your average fungus. Sure, it might look innocent enough with its pale cap and delicate gills, but don't let its appearance fool you. This little guy packs a deadly punch that'll knock your socks off... or should I say, knock your feet off, because that's exactly what it does!\nOne whiff of its spores and you'll be begging for mercy as your toes start to tingle and your legs turn to jelly. So, if you're out foraging in the woods and come across a Deathcap mushroom, just remember: it might be a fungi, but this monster is no fun-guy!\n</Bestiary Lore>", "params": [300, 0, 25, 20, 25, 20, 20, 20]}, {"id": 53, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 321, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 322, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Myrmidon", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: sahuagin sheet>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 5>\n<Bestiary Battleback 1: tg-bbg-riv-01-a>\n<Bestiary Lore>\nKnown for their sharp teeth and powerful tails, the Sahuagin are the bane of any beachgoer's existence. With their slick scales and beady eyes, they're like a cross between a piranha and a human. Don't be fooled by their aquatic agility though, these fishy fiends are just as happy to flop around on land and ruin your picnic as they are to swim up and chomp your toes. The Sahuagin is known to be fiercely territorial, and will stop at nothing to defend its watery domain from any intruders. And don't even get me started on their breath! It's like they've been feasting on seaweed and rotten fish for a week straight.\n</Bestiary Lore>", "params": [300, 0, 30, 20, 20, 20, 20, 20]}, {"id": 54, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 91, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 316, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Vampire Bat", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: bat_0000_dark>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 5>\n<Bestiary Battleback 1: tg-bbg-riv-01-a>\n<Bestiary Lore>\nThis Vampire Bat is not like any other bat you may have encountered. It's a scary creature with razor-sharp fangs and wings that span up to six feet wide. Its body is covered in dense, black fur that makes it difficult to spot in the darkness of its cave home.\nThis bat is a nocturnal predator, feeding on the blood of unsuspecting prey that ventures into its territory. \nIts blood-draining bites are painful and potentially lethal, making it a creature to be feared and avoided by all but the bravest adventurers.\n</Bestiary Lore>", "params": [200, 0, 25, 20, 25, 20, 20, 20]}, {"id": 55, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 56, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 57, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 58, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 59, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 60, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "-----Mountains", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 61, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 137, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 22, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Wind Slime", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 0%>\n<Sideview Battler: slime windB sheet>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 5>\n<Bestiary Battleback 2: Cliff>\n<Bestiary Lore>\nThis mischievous little beast is not your ordinary slime! Instead of being slimy and gooey, it's light and fluffy like a cloud. But don't let its innocent appearance fool you - this wind slime is notorious for causing trouble in the mountains. Its favorite pastime is to sneak up behind unsuspecting travelers and blow them off the edge of a cliff with a powerful gust of wind. And don't even think about trying to catch it - this slippery critter can slip through your fingers like a gust of wind. So if you're planning a hike in the mountains, beware of the wind slime, or you might just end up with a one-way ticket to the valley below!\n</Bestiary Lore>", "params": [300, 0, 25, 20, 25, 20, 20, 20]}, {"id": 62, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 41, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 118, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Thunder Wolgen", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: magicwolf_0001_thunder>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 5>\n<Bestiary Battleback 2: Cliff>\n<Bestiary Lore>\nThe Thunder Wolgen is no ordinary beast, oh no! With fur as white as a thundercloud and eyes that sparkle like lightning bolts, this monster is a true force to be reckoned with. It roams the mountains with a ferocious hunger, always searching for its next meal.\nBut beware, for this wolf doesn't just hunt with its sharp teeth and claws - it can also summon lightning bolts from the sky to strike down its prey. So if you hear the distant rumble of thunder while hiking in the mountains, beware, for it might just be this electrifying beast on the prowl!\n</Bestiary Lore>", "params": [300, 0, 25, 25, 20, 20, 20, 20]}, {"id": 63, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 313, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 131, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Rock Serpent", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: snake_0003_dark>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 5>\n<Bestiary Battleback 2: Cliff>\n<Bestiary Lore>\nThe Rock Serpent is known for its incredibly hard exterior and its tendency to eat unsuspecting climbers who mistake it for a cozy rock to cuddle up to. \nThis mischievous creature loves to play pranks on unsuspecting hikers by coiling itself around their ankles and pretending to be a decorative boulder. But don't be fooled by its playful demeanor - this serpent has a wicked bite that can turn even the strongest knight into a quivering mess. Its scales are as hard as diamonds, and its hiss can shatter glass. Approach with caution, or you might end up as its next chew toy!\n</Bestiary Lore>", "params": [250, 0, 25, 20, 20, 20, 25, 20]}, {"id": 64, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 321, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 35, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 39, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Golem", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: golem_0004_brown>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 5>\n<Bestiary Battleback 2: Cliff>\n<Bestiary Lore>\nLiving in the depths of the mountains, this monster is a master of camouflage, blending seamlessly into the rocky terrain. It is often mistaken for a boulder or a part of the landscape, lulling its prey into a false sense of security before striking with lightning-fast reflexes.\nDespite its formidable appearance, the rock golem is a solitary creature, preferring to keep to itself and only engaging in combat when absolutely necessary. Its origins are shrouded in mystery, with some believing it to be the physical manifestation of the mountains themselves, while others claim it was created by powerful wizards or ancient deities.\n</Bestiary Lore>", "params": [350, 0, 25, 25, 20, 20, 20, 20]}, {"id": 65, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 378, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "<PERSON> Horne<PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: hornet_yellow>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 5>", "params": [300, 0, 35, 30, 30, 30, 30, 50]}, {"id": 66, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 41, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 137, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Wind Wolgen", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: magicwolf_0003_wind>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 5>", "params": [400, 0, 30, 30, 35, 30, 35, 30]}, {"id": 67, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 379, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Mountain Eagle", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Size: 150, 250>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: eagle_white>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 5>", "params": [400, 0, 35, 30, 25, 30, 35, 30]}, {"id": 68, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 380, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Stone Ogre", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Size: 150, 200>\n<Sideview Shadow Scale X: 400%>\n<Sideview Battler: ogretone brown>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 5>", "params": [600, 0, 50, 70, 30, 10, 20, 20]}, {"id": 69, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 70, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "-----Desert", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 71, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 308, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 324, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 51, "denominator": 100}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "<PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: gnoll_0002_sand>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 10>\n<Bestiary Battleback 2: Sand>\n<Bestiary Lore>\nThe Sand Gnoll Scavenger is a fearsome creature that roams the desolate dunes of the desert, preying on any unfortunate travelers that cross its path. With its sharp claws and teeth, it tears apart its victims and devours them whole. Its hide is tough and covered in thick, matted fur that helps protect it from the scorching sun and biting sandstorms that plague the wasteland. The Sand Gnoll Scavenger is a cunning hunter, able to track its prey across the shifting sands for days on end. It is said that the creature possesses a keen sense of smell, able to detect even the slightest hint of blood or sweat from miles away.\n</Bestiary Lore>", "params": [300, 0, 35, 35, 30, 30, 30, 30]}, {"id": 72, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 37, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 313, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 51, "denominator": 100}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Fire Rattler", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: snake_0005_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 10>\n<Bestiary Battleback 2: Sand>\n<Bestiary Lore>\nThe Fire Rattler snake is a dangerous creature that calls the scorching deserts its home. With scales as red as the blazing sun and eyes that burn like embers, it's a creature that strikes fear into the hearts of all who cross its path. Its venom is not only deadly, but it ignites on contact, leaving its prey to suffer a fiery fate. The fire rattler's rattle can be heard from far and wide, warning all who hear it to stay far away. But those who are foolish enough to ignore the warning will quickly find themselves facing one of the deadliest creatures in the desert.\n</Bestiary Lore>", "params": [250, 0, 35, 30, 30, 30, 35, 30]}, {"id": 73, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 36, "rating": 5, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.5}, {"skillId": 37, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 346, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 51, "denominator": 100}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Fire Scarab", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: scarab_0002_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 10>\n<Bestiary Battleback 2: Sand>\n<Bestiary Lore>\nAs its name suggests, the Fire Scarab possesses the ability to generate intense heat and flames from its body, which it uses to ward off predators and prey alike. Its favorite method of attack is to scuttle towards its target at blinding speed, leaving a trail of flames in its wake. Its sharp mandibles can quickly tear through flesh and bone, and it's been known to devour entire caravans of travelers who dare to venture too close to its lair.\nMany desert-dwelling tribes believe that the fire scarab is a harbinger of death and destruction, a cursed creature sent to punish those who disrespect the harsh, unforgiving landscape.\n</Bestiary Lore>", "params": [250, 0, 35, 30, 30, 30, 35, 30]}, {"id": 74, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 343, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 37, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 51, "denominator": 100}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Fire Lizard", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: Lizard_0003_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 10>\n<Bestiary Battleback 2: Sand>\n<Bestiary Lore>\nThe Fire Lizard is a fearsome creature that dwells in the scorching sands of the desert. It has a sleek, serpentine body covered in scales that shimmer like molten gold in the blazing sun. Its sharp claws and teeth are as hot as embers, and it can breathe jets of searing flame that can incinerate anything in its path. Legends tell of great fire lizards that can grow to enormous sizes and even fly through the air on wings of fire. Some even say that these creatures possess magical powers, and that their scales can be used to create powerful potions and spells.\n</Bestiary Lore>", "params": [300, 0, 35, 35, 30, 30, 30, 30]}, {"id": 75, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 321, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 35, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 20, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 51, "denominator": 100}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Sand Gnoll Brute", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: gnollwar_0002_sand>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 10>\n<Bestiary Battleback 2: Sand>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [350, 0, 35, 35, 30, 30, 30, 30]}, {"id": 76, "actions": [{"skillId": 137, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 140, "rating": 5, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.5}, {"skillId": 28, "rating": 5, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.5}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 51, "denominator": 100}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Sand Mage", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Visual Hover Effect>\n Base: 10\n Speed: 20\n Rate: 5.0\n Death: floor\n</Visual Hover Effect>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 10>\n<Bestiary Battleback 2: Sand>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [250, 0, 30, 30, 35, 35, 30, 30]}, {"id": 77, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 326, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 318, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 51, "denominator": 100}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "Desert Scorpion", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: scorpion_0002_sand>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 10>\n<Bestiary Battleback 2: Sand>\n<Bestiary Lore>\n</Bestiary Lore>\n", "params": [300, 0, 35, 30, 35, 30, 30, 30]}, {"id": 78, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 79, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 80, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "-----Undead", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 81, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 45, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 2, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 326, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 362, "rating": 1, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 150, "name": "Risen Champion", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: skullwar_0000_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n<Passive State: 63>\n\n", "params": [400, 0, 30, 40, 25, 30, 25, 35]}, {"id": 82, "actions": [{"skillId": 110, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 91, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 341, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 111, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 150, "name": "Risen <PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: skullmage_0002_dark>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n<Death Transform>\n Dark Spirit\n</Death Transform>", "params": [250, 0, 25, 30, 30, 40, 25, 35]}, {"id": 83, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 47, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 46, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 49, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 362, "rating": 1, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 150, "name": "<PERSON><PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: skullknight_0000_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n<Passive State: 63>", "params": [350, 0, 30, 40, 25, 30, 25, 35]}, {"id": 84, "actions": [{"skillId": 125, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 131, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 36, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 44, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 150, "name": "Risen Druid", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: skullstaff_0002_dark>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n<Death Transform>\n Dark Spirit\n</Death Transform>", "params": [300, 0, 25, 30, 30, 40, 25, 35]}, {"id": 85, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 354, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 355, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 348, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 150, "name": "<PERSON><PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: skullrogue_0002_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n<Death Transform>\n Dark Spirit\n</Death Transform>", "params": [325, 0, 30, 30, 25, 35, 30, 35]}, {"id": 86, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 149, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 50, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 75, "name": "Dark Spirit", "note": "<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: skull_0003_purple>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 10>", "params": [100, 0, 20, 5, 20, 5, 50, 20]}, {"id": 87, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 149, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 151, "rating": 5, "conditionType": 2, "conditionParam1": 0.35, "conditionParam2": 0.75}, {"skillId": 34, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 56, "rating": 5, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.35}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 200, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 300, "name": "<PERSON>rrupt Palad<PERSON>", "note": "<Level: 10>\n<Level Variance: 1>\n<Item Drop 7 To 15: 2%>\n<Item Drop 23 To 39: 1%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: WARM_0007_corrupted>", "params": [1000, 0, 35, 35, 15, 35, 25, 25]}, {"id": 88, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 304, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 324, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 10000, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "<PERSON><PERSON><PERSON>", "note": "<Level: 30>\n<Level Variance: 0>\n<Item Drop 7 To 15: 2%>\n<Item Drop 23 To 39: 1%>\n<Sideview Shadow Scale X: 150%>\n<Sideview Battler: ghoul_0004_dark>", "params": [500, 0, 50, 50, 50, 50, 50, 50]}, {"id": 89, "actions": [{"skillId": 358, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 360, "rating": 5, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.75}, {"skillId": 100, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 30, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 3, "dataId": 289, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 200, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}, {"code": 14, "dataId": 9, "value": 1}], "gold": 500, "name": "Petrified Dryad", "note": "<Level: 15>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Battler: treant_0002_treant-chared>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 300%>\n<Sideview Size: 100, 100>", "params": [1500, 0, 30, 35, 35, 30, 40, 30]}, {"id": 90, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "-----Snow", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 91, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 92, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 93, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 94, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 95, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 96, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 97, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 98, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 99, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 100, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "-----<PERSON>", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 101, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1061, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1062, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Flayer Brute", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: tribal brute 1>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n<Bestiary Lore>\nThe Red Kobold is a fiery little reminder that sometimes, even the runts of the dragon family tree can still pack a mean punch. Don't let their diminutive size fool you - these little guys have got enough aggression to make even a berserker blush. And if you see smoke coming out of their nostrils? Well, let's just say you don't want to stick around to find out what happens next. So if you happen upon a pack of Red Kobolds in your travels, it's best to just turn around and run - or, you know, invest in some fireproof gear. Your call!\n</Bestiary Lore>", "params": [300, 0, 40, 40, 40, 40, 40, 40]}, {"id": 102, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1063, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1061, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Flayer <PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: tribal assassin 1>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n<Bestiary Lore>\nAh, the Blue Kobold - the thinking kobold's kobold. While their Red cousins are busy breathing fire and smashing things, the Blue Kobolds are busy tapping into the frigid power of ice. With their mastery of the arcane arts, they're the closest thing the kobold world has to a brain trust. But don't be fooled by their intelligence - these guys can still pack a mean punch when the going gets tough. So if you're looking for a fight with a Blue Kobold, just be prepared to dodge a few icicles along the way. Stay frosty, my friend!\n</Bestiary Lore>", "params": [300, 0, 40, 40, 40, 40, 40, 40]}, {"id": 103, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1064, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1061, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Flayer Cannibal", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: tribal spearman1>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n<Bestiary Lore>\nThe Kobold Healer is proof that even the most ruthless of creatures can have a heart of gold. Legend has it that the first Kobold Healer was once a fearsome warrior who bested a Paladin in combat and made off with their spell book. But instead of using it for evil, this little guy had a change of heart and decided to dedicate his life to healing others. And thus, the Kobold Healer was born. So if you find yourself in need of a little magical TLC, just seek out one of these pint-sized miracle workers - they may have a bit of a checkered past, but they've got a heart of gold. Or at least, a heart of something shiny and valuable.\n</Bestiary Lore>", "params": [250, 0, 40, 40, 40, 40, 40, 40]}, {"id": 104, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 149, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1065, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1061, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Flayer Desecrator", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: tribal shaman 1>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n<Bestiary Lore>\nThe Kobold Dragoon is proof that sometimes, the simplest weapons are the deadliest. Armed with nothing but a pointy stick and a whole lot of determination, these little guys are not to be underestimated. Sure, they may not have the fancy spells of the Kobold Healers or the fiery breath of the Red Kobolds, but they've got something even more important - a willingness to stab anything that moves. So if you find yourself face-to-face with a pack of Kobold Spearmen, just remember - they may be small, but they're fierce. And pointy. Very, very pointy.\n</Bestiary Lore>", "params": [450, 0, 40, 40, 40, 40, 40, 40]}, {"id": 105, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 102, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1067, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1066, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Adder Axefang", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 0%>\n<Sideview Shadow Scale Y: 0%>\n<Sideview Battler: snakefolk berserker red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n<Bestiary Lore>\nThe Red Kobold is a fiery little reminder that sometimes, even the runts of the dragon family tree can still pack a mean punch. Don't let their diminutive size fool you - these little guys have got enough aggression to make even a berserker blush. And if you see smoke coming out of their nostrils? Well, let's just say you don't want to stick around to find out what happens next. So if you happen upon a pack of Red Kobolds in your travels, it's best to just turn around and run - or, you know, invest in some fireproof gear. Your call!\n</Bestiary Lore>", "params": [300, 0, 40, 40, 40, 40, 40, 40]}, {"id": 106, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 149, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1068, "rating": 6, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1066, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Adder Spellcoil", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 0%>\n<Sideview Shadow Scale Y: 0%>\n<Sideview Battler: snakefolk mage purple>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n<Bestiary Lore>\nAh, the Blue Kobold - the thinking kobold's kobold. While their Red cousins are busy breathing fire and smashing things, the Blue Kobolds are busy tapping into the frigid power of ice. With their mastery of the arcane arts, they're the closest thing the kobold world has to a brain trust. But don't be fooled by their intelligence - these guys can still pack a mean punch when the going gets tough. So if you're looking for a fight with a Blue Kobold, just be prepared to dodge a few icicles along the way. Stay frosty, my friend!\n</Bestiary Lore>", "params": [300, 0, 40, 40, 40, 40, 40, 40]}, {"id": 107, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1066, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1069, "rating": 6, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 110, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Adder Veilblade", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 0%>\n<Sideview Shadow Scale Y: 0%>\n<Sideview Battler: snakefolk rogue blue>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n<Bestiary Lore>\nThe Kobold Healer is proof that even the most ruthless of creatures can have a heart of gold. Legend has it that the first Kobold Healer was once a fearsome warrior who bested a Palad<PERSON> in combat and made off with their spell book. But instead of using it for evil, this little guy had a change of heart and decided to dedicate his life to healing others. And thus, the Kobold Healer was born. So if you find yourself in need of a little magical TLC, just seek out one of these pint-sized miracle workers - they may have a bit of a checkered past, but they've got a heart of gold. Or at least, a heart of something shiny and valuable.\n</Bestiary Lore>", "params": [250, 0, 40, 40, 40, 40, 80, 40]}, {"id": 108, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1066, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1070, "rating": 6, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 137, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Adder Shieldscale", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 0%>\n<Sideview Shadow Scale Y: 0%>\n<Sideview Battler: serpentfolk war green>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n<Bestiary Lore>\nThe Kobold Dragoon is proof that sometimes, the simplest weapons are the deadliest. Armed with nothing but a pointy stick and a whole lot of determination, these little guys are not to be underestimated. Sure, they may not have the fancy spells of the Kobold Healers or the fiery breath of the Red Kobolds, but they've got something even more important - a willingness to stab anything that moves. So if you find yourself face-to-face with a pack of Kobold Spearmen, just remember - they may be small, but they're fierce. And pointy. Very, very pointy.\n</Bestiary Lore>", "params": [350, 0, 40, 40, 40, 40, 40, 40]}, {"id": 109, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 110, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "-----Steampunk", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 111, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 383, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Cogsburgh Sniper", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: cogs<PERSON>_sniper>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 25>", "params": [100, 0, 10, 10, 10, 10, 100, 10]}, {"id": 112, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 75, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 234, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Cogsburgh Machinist", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: cogsburgh_machinist>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 25>", "params": [1000, 0, 10, 10, 10, 10, 10, 10]}, {"id": 113, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 385, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Cogsburgh Marauder", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: cogsburgh_marauder>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 25>", "params": [1000, 0, 10, 10, 10, 10, 10, 10]}, {"id": 114, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 384, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Cogsburgh Riveteer", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: cogsburgh_riveteer>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 25>", "params": [200, 0, 10, 10, 10, 10, 100, 10]}, {"id": 115, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 381, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 382, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "<PERSON><PERSON><PERSON><PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 400%>\n<Sideview Battler: drillmech>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>", "params": [500, 0, 10, 100, 10, 10, 100, 10]}, {"id": 116, "actions": [{"skillId": 6, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 32, "dataId": 39, "value": 0.5}], "gold": 0, "name": "Steam Giant", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 300%>\n<Sideview Shadow Scale Y: 300%>\n<Sideview Battler: steambot_gold>\n<Sideview Size: 150, 225>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>", "params": [20000, 0, 50, 50, 50, 50, 0, 50]}, {"id": 117, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 118, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 119, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 120, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "-----<PERSON><PERSON>", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 121, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 102, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Molten Slime", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: slime_magma>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>", "params": [1000, 0, 50, 50, 50, 50, 20, 50]}, {"id": 122, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "<PERSON><PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: snake_0005_red>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>", "params": [1000, 0, 50, 50, 50, 50, 20, 50]}, {"id": 123, "actions": [{"skillId": 6, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 102, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Magma Crab", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: crab_magma>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>", "params": [1000, 0, 50, 50, 50, 50, 20, 50]}, {"id": 124, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 102, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Embersnail", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: snail_magma>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>", "params": [1000, 0, 50, 50, 50, 50, 20, 50]}, {"id": 125, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Fire Drake", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: Dragonling_0000_fireling>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>", "params": [1000, 0, 50, 50, 50, 50, 20, 50]}, {"id": 126, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "<PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: golem_magma>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>", "params": [1000, 0, 50, 50, 50, 50, 20, 50]}, {"id": 127, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 128, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 129, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 130, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "-----V<PERSON>", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 131, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"skillId": 1071, "rating": 6, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Abomination", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: abomination void>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n\n<Level: 15>\n<Level Variance: 10>", "params": [1000, 0, 40, 10, 20, 10, 40, 10]}, {"id": 132, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"skillId": 1072, "rating": 6, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "<PERSON><PERSON><PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: eye horror void>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n\n<Level: 15>\n<Level Variance: 10>", "params": [1000, 0, 20, 10, 40, 10, 40, 10]}, {"id": 133, "actions": [{"skillId": 6, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 26, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 31, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 33, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 34, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.15}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Beholder", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: floating eye void>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n\n<Level: 15>\n<Level Variance: 10>", "params": [500, 0, 20, 50, 20, 50, 40, 10]}, {"id": 134, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"skillId": 1073, "rating": 6, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Shadowhound", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: stalker void>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n\n<Level: 15>\n<Level Variance: 10>", "params": [1000, 0, 10, 10, 10, 10, 40, 10]}, {"id": 135, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"skillId": 1074, "rating": 6, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Mindflayer", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: flayer void>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>\n\n<Level: 15>\n<Level Variance: 10>", "params": [1000, 0, 20, 20, 20, 20, 40, 20]}, {"id": 136, "actions": [{"skillId": 150, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 314, "rating": 6, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "REVIEW Mawbeast", "note": "<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 0%>\n<Sideview Shadow Scale Y: 0%>\n<Sideview Battler: maw void>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>", "params": [9999, 0, 20, 50, 20, 50, 40, 20]}, {"id": 137, "actions": [{"skillId": 44, "rating": 5, "conditionType": 4, "conditionParam1": 133, "conditionParam2": 0}, {"skillId": 102, "rating": 5, "conditionType": 4, "conditionParam1": 133, "conditionParam2": 0}, {"skillId": 110, "rating": 5, "conditionType": 4, "conditionParam1": 133, "conditionParam2": 0}, {"skillId": 118, "rating": 5, "conditionType": 4, "conditionParam1": 133, "conditionParam2": 0}, {"skillId": 125, "rating": 5, "conditionType": 4, "conditionParam1": 133, "conditionParam2": 0}, {"skillId": 131, "rating": 5, "conditionType": 4, "conditionParam1": 133, "conditionParam2": 0}, {"skillId": 137, "rating": 5, "conditionType": 4, "conditionParam1": 133, "conditionParam2": 0}, {"skillId": 149, "rating": 5, "conditionType": 4, "conditionParam1": 133, "conditionParam2": 0}, {"skillId": 1076, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1077, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "REVIEW Tentacle", "note": "<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 0%>\n<Sideview Shadow Scale Y: 0%>\n<Sideview Battler: eldrich tentacle void>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>", "params": [1499, 0, 30, 10, 30, 10, 70, 10]}, {"id": 138, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Blightdrake", "note": "<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: eldrichdragon void>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>", "params": [100, 0, 10, 10, 10, 10, 40, 10]}, {"id": 139, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Corrupter", "note": "<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.6%>\n<Armor Drop 365 To 381: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: king void>\n<Sideview Size: 100, 150>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 15>", "params": [100, 0, 10, 10, 10, 10, 40, 10]}, {"id": 140, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "-----High Level", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 141, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 301, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 71, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "<PERSON><PERSON><PERSON><PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: hobgobjobless sheet>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe Goblin Slicer is at the very bottom of the goblin hierarchy. They craft crude blades from stone in order to defend their territory from intruders, but other than that they are basically feral..\nThey communicate through grunts and snarls, and their idea of a good time is wrestling with each other in the mud. They're not exactly the most social creatures, but they do form tight-knit packs with their fellow Goblin Slicers. And let's face it, with those jagged teeth and wild eyes, you wouldn't want to be their friend anyways. If you see a Goblin Slicer in the wild, it's best to just turn around and run the other way... or bring a really big stick.\n</Bestiary Lore>\n\n", "params": [300, 0, 35, 30, 25, 30, 30, 25]}, {"id": 142, "actions": [{"skillId": 302, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 339, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 89, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.1}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "<PERSON><PERSON><PERSON><PERSON> Tracker", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: hobgobarcher_0003_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nGoblin Hunters use bows made from tree branches, and are adept at tracking their prey for many miles. Their weapons may be primitive, but they know how to use them with deadly precision. The Goblin Hunters are the bane of any traveler, as they strike from the shadows and disappear just as quickly. Some say they have a special connection with the forest, and can communicate with the animals that live within it. Others say they are just really good at hiding behind trees.\n</Bestiary Lore>", "params": [400, 0, 35, 30, 30, 30, 25, 40]}, {"id": 143, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 303, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 102, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.1}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "<PERSON><PERSON><PERSON><PERSON> Evoker", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: hobgobmage_0002_purple>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe Goblin Shaman is a spiritual leader to his tribe, able to call upon the rudimentary power of fire using forbidden rituals.\nSome say the Goblin Shaman's powers come from the spirits of their ancestors, while others believe it's just indigestion from eating too many moldy mushrooms. They can summon flames that would make a dragon jealous, and their screams can shatter even the bravest warrior's nerves. Despite their fearsome reputation, the Goblin Shaman is a respected figure among their tribe. They act as judge, jury, and executioner, and their word is law. Just make sure you're not on their bad side, or you might end up being the main course at their next feast.\n</Bestiary Lore>\n", "params": [300, 0, 25, 25, 35, 35, 25, 40]}, {"id": 144, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 20, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 380, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Hill Ogre", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: ogretone grey>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nLegend has it that Hill Trolls were once sophisticated beings with a love for poetry and fine cuisine, but somewhere along the way they hit their heads a few too many times and lost most of their brain cells. Now they spend their days hitting rocks with their boulder clubs and grunting at each other. Living under bridges is a natural fit for these lumbering beasts, as they don't have to worry about pesky things like rent or property taxes. Their diet consists mainly of whatever small animals they can catch, and occasionally a careless adventurer who happens to wander too close. If you're ever on a journey through the hills, be sure to keep an eye out for these simple-minded giants. Just don't try to engage them in conversation, or you might end up with a boulder-sized headache.\n</Bestiary Lore>", "params": [500, 0, 40, 50, 20, 30, 25, 30]}, {"id": 145, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 305, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 64, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Hobgob Commander", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: hobgobwar_0000_silver>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe Goblin Captains are a force to be reckoned with on the battlefield. They have a knack for stealing the best weapons and armor from their fallen enemies, and wearing it as a badge of honor. But it's not just their shiny gear that makes them stand out. Their battle cries are enough to make even the bravest warrior quake in their boots, and their leadership skills are unparalleled. They can rally their fellow goblins into a frenzy, and even bring fallen comrades back into battle with a swift kick to the rear. If you ever find yourself facing a Goblin Captain, be prepared for a fierce fight. And maybe bring some extra padding for your backside, just in case.\n</Bestiary Lore>", "params": [500, 0, 30, 35, 30, 35, 40, 40]}, {"id": 146, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 306, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 33, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "<PERSON><PERSON><PERSON><PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: hobgobmage_0000_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThese twisted beings have forsaken all that is good and pure, choosing instead to embrace the shadows and the forbidden knowledge that lies within. Goblin Warlocks are shrouded in a cloud of dark magic, and their eyes glow with an eerie green light. They can summon creatures from the netherworld to do their bidding, and twist the minds of their enemies with spells that cause hallucinations and nightmares. Many goblin tribes fear the Warlocks, as they have been known to turn on their own kind if it suits their purposes. It's said that if you cross a Goblin Warlock, they will curse you with a terrible fate worse than death. So if you ever find yourself face to face with one of these malevolent creatures, it's best to tread carefully... or run for your life.\n</Bestiary Lore>", "params": [300, 0, 25, 25, 35, 35, 40, 40]}, {"id": 147, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 131, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 307, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "<PERSON><PERSON><PERSON><PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: hobgobmage_0001_green>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nGoblin Geomancers are in tune with the natural world, and they possess an innate ability to communicate with the elements of the earth and water. They are the healers of their tribe, using their knowledge of herbs, crystals, and minerals to cure a wide variety of ailments. They are also responsible for maintaining the delicate balance of nature in their territory, ensuring that the plants and animals are healthy and thriving. Despite their gentle nature, Goblin Geomancers are not to be underestimated in battle. They can call forth powerful earthquakes and tidal waves to crush their enemies, and their mastery of the earth and water can make them nearly invincible in the right circumstances. Just be careful not to step on any plants or disturb any animals in their presence, or you may face their wrath.\n</Bestiary Lore>", "params": [300, 0, 25, 25, 35, 35, 30, 40]}, {"id": 148, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 308, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 15, "rating": 5, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.25}, {"skillId": 345, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 350, "name": "<PERSON><PERSON><PERSON><PERSON>pur<PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: hobgobrogue_0003_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe Goblin Ambusher is the ultimate coward. They don't have the guts to face you head-on, so they resort to throwing sand in your eyes and stealing your gold like a common street thug. If you ever find yourself facing an Ambusher, just make sure you've got some goggles and a good grip on your coin purse. These little buggers are slippery, and they'll do anything to avoid a fair fight. And if you do manage to catch one, don't be surprised if they try to talk their way out of it. They'll beg for mercy, promise to turn over a new leaf, and then steal your boots when you're not looking. It's best to just give them a wide berth and let them go about their thieving ways. After all, they're just doing what goblins do best... being annoying little pests.\n</Bestiary Lore>", "params": [400, 0, 35, 25, 20, 25, 35, 60]}, {"id": 149, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 344, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 321, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Hobgob Impaler", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: hobgobdragoon_0002_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe Goblin Knight is a walking contradiction. On one hand, they're tiny, green, and not particularly intimidating. On the other hand, they're decked out in shiny armor and wielding a pointy spear that looks like it could skewer a small horse. It's like watching a Chihuahua wearing a suit of armor trying to take down a Great Dane. But don't let their comical appearance fool you... they're still dangerous. They may not be able to swing very hard, but they make up for it with sheer determination and a willingness to throw themselves into battle without a second thought. And if they ever do manage to get a lucky hit in, it'll feel like you got stung by a hornet... a really, really angry hornet.\n</Bestiary Lore>", "params": [500, 0, 35, 30, 25, 30, 30, 30]}, {"id": 150, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 324, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 41, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Direwolgen", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: direwolf_grey>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe goblin warhound is a fearsome creature that resembles a cross between a wolf and a hyena. Its mangy fur is a mottled mix of brown and gray, and its sharp teeth gleam in the light with a menacing glint. Its eyes are yellow and intelligent, betraying a cunning and ferocious nature. It is a loyal and obedient companion to its goblin masters, who use them to hunt and track down their enemies. They are trained from birth to be savage killers, and are often used as shock troops in goblin armies. Their powerful jaws can crush bones with ease, and their claws are razor-sharp, capable of tearing through armor and flesh alike. And they're surprisingly good at fetching, as long as you're throwing something they're interested in, like a chunk of human flesh or a particularly juicy bone.\n</Bestiary Lore>", "params": [400, 0, 35, 25, 25, 25, 35, 50]}, {"id": 151, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 346, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 27, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 130, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Plated Stinkbug", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: beetle_0001_dark>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe Stinkbug is a fearsome creature that strikes terror into the hearts of even the bravest adventurers... or at least, it would, if anyone could get close enough to smell it. This monstrous insect is easily recognizable by its massive horn, which it uses to skewer prey and ward off predators. But what really sets the Stinkbug apart is its odor. Legend has it that the stench of its foul-smelling gas can knock out a dragon at 50 paces, and it's certainly enough to make any human or elf wish they were somewhere else. If you ever encounter a giant horned stinkbug in the wild, be sure to hold your nose and keep your distance... or bring a lot of air freshener.\n</Bestiary Lore>", "params": [500, 0, 35, 100, 30, 0, 25, 40]}, {"id": 152, "actions": [{"skillId": 312, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 318, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 359, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Red Treant", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: plant_0004_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe Sporeleaf Stinger - nature's way of saying \"I'm sorry, did you need that hand?\" If you see one of these bad boys, run the other way as fast as you can. Or, if you're feeling adventurous, go ahead and touch one - just be prepared for the kind of pain that makes childbirth look like a walk in the park. And whatever you do, don't even think about using those flowers to impress your sweetheart - unless they're really into excruciating agony, in which case, hey, you do you. Because what's the point of a beautiful flower if it doesn't also have the power to ruin your entire week? If you see these guys in the brush, it's best to give them a wide berth, unless of course you're in the market for some top-notch pain management. Just be sure to bring some tissues and a strong sense of regret.\n</Bestiary Lore>", "params": [300, 0, 25, 30, 35, 30, 30, 25]}, {"id": 153, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 313, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 319, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 75, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Blood Widow", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: spider_0001_red>\n<JS Negative Level Variance: $gameVariables.value(143)><Minimum Level: 20>\n<Minimum Level: 20>\n<Bestiary Lore>\nForest Spiders - because why settle for the comforting notion that tiny spiders might be crawling around your house, when you could be imagining a giant, camouflaged spider lurking in the woods, waiting to pounce on unsuspecting prey? The Forest Spider is a creature straight out of a nightmare. This eight-legged arachnid is the size of a small dog and can spin webs strong enough to catch even the bravest of adventurers. But don't worry, it's not all bad news - this spider has a great sense of humor. In fact, it's been known to spin webs that say things like \"Welcome to my parlor\" and \"Gotcha!\" just to give its prey a good chuckle before devouring them.\n</Bestiary Lore>", "params": [400, 0, 35, 30, 25, 30, 30, 40]}, {"id": 154, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 316, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 26, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Blood Bat", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 40 To 45: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: Bat_0001_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nPart bird, part demon, and all nightmare fuel. These little guys may seem harmless enough, until you realize that they're not just perching on high places for the view. Nope, they're waiting for their next meal to come along, and when it does, they strike like a feathered lightning bolt, turning their prey to stone with a glance. So if you see a gargoyle perched on a nearby building, it's best to keep your head down and run like the wind, unless of course you're in the market for a new lawn ornament.\n</Bestiary Lore>", "params": [400, 0, 35, 30, 25, 30, 30, 40]}, {"id": 155, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1051, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1052, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "<PERSON><PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: orc berserker A>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe goblin warhound is a fearsome creature that resembles a cross between a wolf and a hyena. Its mangy fur is a mottled mix of brown and gray, and its sharp teeth gleam in the light with a menacing glint. Its eyes are yellow and intelligent, betraying a cunning and ferocious nature. It is a loyal and obedient companion to its goblin masters, who use them to hunt and track down their enemies. They are trained from birth to be savage killers, and are often used as shock troops in goblin armies. Their powerful jaws can crush bones with ease, and their claws are razor-sharp, capable of tearing through armor and flesh alike. And they're surprisingly good at fetching, as long as you're throwing something they're interested in, like a chunk of human flesh or a particularly juicy bone.\n</Bestiary Lore>", "params": [800, 0, 35, 20, 15, 20, 25, 35]}, {"id": 156, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1053, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1054, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "<PERSON><PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: orc warrior A>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe goblin warhound is a fearsome creature that resembles a cross between a wolf and a hyena. Its mangy fur is a mottled mix of brown and gray, and its sharp teeth gleam in the light with a menacing glint. Its eyes are yellow and intelligent, betraying a cunning and ferocious nature. It is a loyal and obedient companion to its goblin masters, who use them to hunt and track down their enemies. They are trained from birth to be savage killers, and are often used as shock troops in goblin armies. Their powerful jaws can crush bones with ease, and their claws are razor-sharp, capable of tearing through armor and flesh alike. And they're surprisingly good at fetching, as long as you're throwing something they're interested in, like a chunk of human flesh or a particularly juicy bone.\n</Bestiary Lore>", "params": [1200, 0, 40, 35, 15, 10, 15, 20]}, {"id": 157, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1056, "rating": 6, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1055, "rating": 6, "conditionType": 4, "conditionParam1": 90, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "<PERSON><PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: orc axemwoman A>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Passive State: 124>\n<Bestiary Lore>\nThe goblin warhound is a fearsome creature that resembles a cross between a wolf and a hyena. Its mangy fur is a mottled mix of brown and gray, and its sharp teeth gleam in the light with a menacing glint. Its eyes are yellow and intelligent, betraying a cunning and ferocious nature. It is a loyal and obedient companion to its goblin masters, who use them to hunt and track down their enemies. They are trained from birth to be savage killers, and are often used as shock troops in goblin armies. Their powerful jaws can crush bones with ease, and their claws are razor-sharp, capable of tearing through armor and flesh alike. And they're surprisingly good at fetching, as long as you're throwing something they're interested in, like a chunk of human flesh or a particularly juicy bone.\n</Bestiary Lore>", "params": [1100, 0, 45, 40, 15, 15, 20, 25]}, {"id": 158, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1057, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1058, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "<PERSON><PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: orc spearwoman A>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe goblin warhound is a fearsome creature that resembles a cross between a wolf and a hyena. Its mangy fur is a mottled mix of brown and gray, and its sharp teeth gleam in the light with a menacing glint. Its eyes are yellow and intelligent, betraying a cunning and ferocious nature. It is a loyal and obedient companion to its goblin masters, who use them to hunt and track down their enemies. They are trained from birth to be savage killers, and are often used as shock troops in goblin armies. Their powerful jaws can crush bones with ease, and their claws are razor-sharp, capable of tearing through armor and flesh alike. And they're surprisingly good at fetching, as long as you're throwing something they're interested in, like a chunk of human flesh or a particularly juicy bone.\n</Bestiary Lore>", "params": [700, 0, 30, 25, 20, 25, 40, 35]}, {"id": 159, "actions": [{"skillId": 1059, "rating": 6, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1060, "rating": 6, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.5}, {"skillId": 306, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Orc Ritualist", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: orc shaman A>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe goblin warhound is a fearsome creature that resembles a cross between a wolf and a hyena. Its mangy fur is a mottled mix of brown and gray, and its sharp teeth gleam in the light with a menacing glint. Its eyes are yellow and intelligent, betraying a cunning and ferocious nature. It is a loyal and obedient companion to its goblin masters, who use them to hunt and track down their enemies. They are trained from birth to be savage killers, and are often used as shock troops in goblin armies. Their powerful jaws can crush bones with ease, and their claws are razor-sharp, capable of tearing through armor and flesh alike. And they're surprisingly good at fetching, as long as you're throwing something they're interested in, like a chunk of human flesh or a particularly juicy bone.\n</Bestiary Lore>", "params": [600, 0, 15, 15, 30, 45, 20, 30]}, {"id": 160, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Dark Elf Warrior", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: darkelf_red>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe goblin warhound is a fearsome creature that resembles a cross between a wolf and a hyena. Its mangy fur is a mottled mix of brown and gray, and its sharp teeth gleam in the light with a menacing glint. Its eyes are yellow and intelligent, betraying a cunning and ferocious nature. It is a loyal and obedient companion to its goblin masters, who use them to hunt and track down their enemies. They are trained from birth to be savage killers, and are often used as shock troops in goblin armies. Their powerful jaws can crush bones with ease, and their claws are razor-sharp, capable of tearing through armor and flesh alike. And they're surprisingly good at fetching, as long as you're throwing something they're interested in, like a chunk of human flesh or a particularly juicy bone.\n</Bestiary Lore>", "params": [99999, 0, 15, 20, 15, 20, 5, 35]}, {"id": 161, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "Dark Elf Slayer", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: darkelf_red2>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe goblin warhound is a fearsome creature that resembles a cross between a wolf and a hyena. Its mangy fur is a mottled mix of brown and gray, and its sharp teeth gleam in the light with a menacing glint. Its eyes are yellow and intelligent, betraying a cunning and ferocious nature. It is a loyal and obedient companion to its goblin masters, who use them to hunt and track down their enemies. They are trained from birth to be savage killers, and are often used as shock troops in goblin armies. Their powerful jaws can crush bones with ease, and their claws are razor-sharp, capable of tearing through armor and flesh alike. And they're surprisingly good at fetching, as long as you're throwing something they're interested in, like a chunk of human flesh or a particularly juicy bone.\n</Bestiary Lore>", "params": [99999, 0, 15, 20, 15, 20, 5, 35]}, {"id": 162, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 250, "name": "<PERSON> <PERSON><PERSON>er", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Item Drop 7 To 10: 5%>\n<Item Drop 46 To 49: 50%>\n<Item Drop 11 To 15: 2.5%>\n<Item Drop 23 To 39: 0.6%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: darkelf_red3>\n<JS Negative Level Variance: $gameVariables.value(143)>\n<Minimum Level: 20>\n<Bestiary Lore>\nThe goblin warhound is a fearsome creature that resembles a cross between a wolf and a hyena. Its mangy fur is a mottled mix of brown and gray, and its sharp teeth gleam in the light with a menacing glint. Its eyes are yellow and intelligent, betraying a cunning and ferocious nature. It is a loyal and obedient companion to its goblin masters, who use them to hunt and track down their enemies. They are trained from birth to be savage killers, and are often used as shock troops in goblin armies. Their powerful jaws can crush bones with ease, and their claws are razor-sharp, capable of tearing through armor and flesh alike. And they're surprisingly good at fetching, as long as you're throwing something they're interested in, like a chunk of human flesh or a particularly juicy bone.\n</Bestiary Lore>", "params": [99999, 0, 15, 20, 15, 20, 5, 35]}, {"id": 163, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 164, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 165, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 166, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 167, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 168, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 169, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 170, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 171, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 172, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 173, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 174, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 175, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 176, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 177, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 178, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 179, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 180, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 181, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "T2 Jungle", "note": "<Swap Enemies>\nAdder Shieldscale\nAdder Veilblade\nAdder Spellcoil\nFlayer Desecrator\nFlayer Brute\nAdder Axefang\nFlayer Scavenger\nFlayer Cannibal\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 182, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "T2 Orcs", "note": "<Swap Enemies>\nOrc Hammerer\nOrc Skirmisher\nOrc Ritualist\n<PERSON><PERSON> Bloodmaiden\nOrc Berserker\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 183, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "T2 Lava Monster", "note": "<Swap Enemies>\nMolten Slime\nLava Serpent\nMagma Crab\nEmbersnail\nFire Drake\nBlaze Golem\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 184, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "T2 Mountain Monster", "note": "<Swap Enemies>\nHobgob Evoker\nHobgob Commander\n<PERSON><PERSON><PERSON><PERSON> <PERSON>lock\nHobgob Impaler\nDeath Hornet\nWind Wolgen\nMountain Eagle\nStone Ogre\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 185, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "T2 Cave Monster", "note": "<Swap Enemies>\nHobgob Miscreant\nHobgob Tracker\nHobgob Evoker\nHill Ogre\nHobgob Commander\n<PERSON><PERSON><PERSON><PERSON> Warlock\nHobgob Vinemancer\nHobgob Cutpurse\nHobgob Impaler\nDirewolgen\nPlated Stinkbug\nPyrethorn Plant\nBlood Widow\nVampire Bat\nYellow Bellflower\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 186, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "T2 Forest Monster", "note": "<Swap Enemies>\nHobgob Miscreant\nHobgob Tracker\nHobgob Vinemancer\nHobgob Cutpurse\nWater Wolgen\nYellowbell\nOwlbeast\nBrown Bear\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 187, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "T2 Beach Monster", "note": "<Swap Enemies>\nHobgob Miscreant\nHobgob Tracker\nHobgob Evoker\nHill Ogre\nHobgob Commander\n<PERSON><PERSON><PERSON><PERSON> Warlock\nHobgob Vinemancer\nHobgob Cutpurse\nHobgob Impaler\nDirewolgen\nPlated Stinkbug\nPyrethorn Plant\nBlood Widow\nVampire Bat\nYellow Bellflower\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 188, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "T2 World Map Monster", "note": "<Swap Enemies>\nHobgob Miscreant\nHobgob Tracker\nHobgob Evoker\nHill Ogre\nHobgob Commander\n<PERSON><PERSON><PERSON><PERSON> Warlock\nHobgob Vinemancer\nHobgob Cutpurse\nHobgob Impaler\nDirewolgen\nPlated Stinkbug\nPyrethorn Plant\nBlood Widow\nBlood Bat\nOrc Berserker\nOrc Hammerer\nOrc Ritualist\nOrc Bloodmaiden\nOrc Skirmisher\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 189, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "T2 Void Monsters", "note": "Shadowhound\n<Swap Enemies>\nAbomination\nMindflayer\nGazer\n</Swap Enemies>\nBeholder\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 190, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Volcano Monsters", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 191, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Jungle Monsters", "note": "\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 192, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Snow Monsters", "note": "\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 193, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Undead Monster", "note": "<Swap Enemies>\nRisen Champion\nRisen Necron\nRisen Knight\nRisen Druid\nRisen <PERSON>alker\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 194, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Desert Monster", "note": "<Minimum Level: 10>\n<Swap Enemies>\nStinging Sporeleaf\nToxic Nightshade\nSand Gnoll Scavenger\nFire Rattler\nFire Scarab\nFire Lizard\nSand Gnoll Bruth\nSand Mage\nDesert Scorpion\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 195, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Mountain Monster", "note": "<Swap Enemies>\nGoblin Slicer\nGoblin Hunter\nGoblin Shaman\nHill Troll\nGoblin Captain\nGoblin Warlock\nGoblin Geomancer\nGoblin Ambusher\nGoblin Knight\nGoblin Warhound\nGargoyle\nWind Spirit\nWind Slime\nThunder Wolgen\nRock Serpent\nGolem\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 196, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Cave Monster", "note": "<Swap Enemies>\nWater Spirit\nRed Crab\nBlue Crab\nMan o' War\nCuttlefish\nRolly-pede\nDeathcap\nSahuagin\nVampire Bat\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 197, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Forest Monster", "note": "<Swap Enemies>\nGoblin Ambusher\nForest Spider\nKobold Dragoon\nDart Frog\nEarth Spirit\nEarth Wolgen\nScarlet Thrasher\nDream Fairy\nCharred Treant\nDeathcap\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 198, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Beach Monster", "note": "<Swap Enemies>\nRed Crab\nBlue crab\nReefstalker\nSandcaster\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 199, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 237, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 20, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "World Map Monster", "note": "<Swap Enemies>\nGoblin Slicer\nGoblin Hunter\nGoblin Shaman\nHill Troll\nGoblin Captain\nGoblin Warlock\nGoblin Geomancer\nGoblin Ambusher\nGoblin Knight\nGoblin Warhound\nStinkbug\nStinging Sporeleaf\nForest Spider\nGargoyle\nToxic Nightshade\nRed Kobold\nBlue Kobold\nKobold Healer\nKobold Dragoon\nDart Frog\nShadow Spirit\nEarth Spirit\nFire Spirit\nLight Spirit\nIce Spirit\nThunder Spirit\nWater Spirit\nWind Spirit\nGolden Canary\n</Swap Enemies>\n<Hide in Bestiary>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 200, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "-----<PERSON><PERSON>", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 201, "actions": [{"skillId": 358, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 360, "rating": 5, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.75}, {"skillId": 137, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 312, "rating": 5, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.5}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 3, "dataId": 289, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 200, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}], "gold": 500, "name": "Corrupted Dryad", "note": "<Level: 3>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Multi-Layer HP Gauge Layers: 2>\n<Sideview Battler: treant_0006_tree>\n<Sideview Shadow Scale X: 600%>\n<Sideview Shadow Scale Y: 400%>\n<Sideview Size: 250, 250>\n<Bestiary Category: Boss>\n<Bestiary Lore>\n</Bestiary Lore>\n", "params": [2000, 0, 30, 35, 35, 30, 35, 30]}, {"id": 202, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 301, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 305, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 35, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 20, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 50, "denominator": 1}, {"kind": 3, "dataId": 290, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 14, "dataId": 13, "value": 1}], "gold": 250, "name": "Goblin General", "note": "<Level: 7>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Battler: gobwar_0002_gold>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Size: 250, 250>\n<Bestiary Category: Rare>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [1500, 20, 55, 20, 20, 20, 20, 20]}, {"id": 203, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 102, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 103, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 50, "denominator": 1}, {"kind": 3, "dataId": 292, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 14, "dataId": 13, "value": 1}], "gold": 250, "name": "<PERSON><PERSON><PERSON>", "note": "<Level: 7>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Battler: golem_0003_orange>\n<Sideview Shadow Scale X: 800%>\n<Sideview Shadow Scale Y: 400%>\n<Sideview Size: 300, 300>\n<Bestiary Category: Rare>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [1800, 0, 100, 30, 50, 20, 30, 20]}, {"id": 204, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 110, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 111, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 184, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 50, "denominator": 1}, {"kind": 3, "dataId": 293, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 14, "dataId": 13, "value": 1}], "gold": 250, "name": "<PERSON><PERSON><PERSON>", "note": "<Level: 7>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Battler: golem_0002_ice>\n<Sideview Shadow Scale X: 800%>\n<Sideview Shadow Scale Y: 400%>\n<Sideview Size: 300, 300>\n<Bestiary Category: Rare>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [1800, 0, 100, 30, 50, 20, 30, 20]}, {"id": 205, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 131, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 39, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 321, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 50, "denominator": 1}, {"kind": 0, "dataId": 0, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 400, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 14, "dataId": 13, "value": 1}], "gold": 250, "name": "Crushrux", "note": "<Level: 18>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Battler: golem_0001_green>\n<Sideview Shadow Scale X: 800%>\n<Sideview Shadow Scale Y: 400%>\n<Sideview Size: 300, 300>\n<Bestiary Category: Rare>\n<Bestiary Battleback 2: Sand>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [4500, 0, 120, 60, 90, 20, 100, 40]}, {"id": 206, "actions": [{"skillId": 4, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 355, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1087, "rating": 6, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1088, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1089, "rating": 7, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1090, "rating": 8, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 500, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 61, "dataId": 0, "value": 1}], "gold": 1000, "name": "Convict Rook", "note": "<Level: 20>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Multi-Layer HP Gauge Layers: 3>\n<Sideview Shadow Scale X: 300%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Battler: MV evil-2 sheet>", "params": [10000, 0, 30, 30, 10, 30, 50, 30]}, {"id": 207, "actions": [{"skillId": 321, "rating": 1, "conditionType": 1, "conditionParam1": 1, "conditionParam2": 3}, {"skillId": 322, "rating": 5, "conditionType": 1, "conditionParam1": 1, "conditionParam2": 3}, {"skillId": 324, "rating": 1, "conditionType": 1, "conditionParam1": 2, "conditionParam2": 3}, {"skillId": 325, "rating": 5, "conditionType": 1, "conditionParam1": 2, "conditionParam2": 3}, {"skillId": 40, "rating": 5, "conditionType": 1, "conditionParam1": 3, "conditionParam2": 3}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 0, "dataId": 0, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 250, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}], "gold": 1500, "name": "Megalgos", "note": "<Level: 6>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Multi-Layer HP Gauge Layers: 2>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 300%>\n<Sideview Battler: sahuagin sheet large>\n<Sideview Size: 250, 250>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [2500, 0, 45, 45, 45, 45, 35, 45]}, {"id": 208, "actions": [{"skillId": 47, "rating": 5, "conditionType": 1, "conditionParam1": 1, "conditionParam2": 3}, {"skillId": 45, "rating": 5, "conditionType": 1, "conditionParam1": 2, "conditionParam2": 3}, {"skillId": 46, "rating": 5, "conditionType": 1, "conditionParam1": 3, "conditionParam2": 3}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 0, "dataId": 0, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 300, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}], "gold": 1500, "name": "Renault", "note": "<Level: 8>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Multi-Layer HP Gauge Layers: 3>\n<Sideview Shadow Scale X: 300%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Battler: renault-battler_large>\n<Sideview Size: 150, 175>", "params": [3000, 0, 35, 30, 35, 20, 35, 30]}, {"id": 209, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 47, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 49, "rating": 1, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 40, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 60, "name": "<PERSON><PERSON>", "note": "<Level: 5>\n<Level Variance: 0>\n<Item Drop 7 To 15: 2%>\n<Item Drop 23 To 39: 1%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: WARM_0007_ds-blu>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [400, 0, 20, 25, 20, 20, 20, 20]}, {"id": 210, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 41, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 349, "rating": 2, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 324, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 50, "denominator": 1}, {"kind": 0, "dataId": 0, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 350, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}], "gold": 2000, "name": "<PERSON><PERSON><PERSON><PERSON>", "note": "<Level: 14>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Battler: Werewolf_0004_brown>\n<Sideview Size: 150, 150>\n<Bestiary Category: Rare>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [2800, 0, 60, 30, 50, 20, 80, 50]}, {"id": 211, "actions": [{"skillId": 314, "rating": 6, "conditionType": 1, "conditionParam1": 3, "conditionParam2": 5}, {"skillId": 327, "rating": 5, "conditionType": 1, "conditionParam1": 1, "conditionParam2": 5}, {"skillId": 328, "rating": 5, "conditionType": 1, "conditionParam1": 1, "conditionParam2": 5}, {"skillId": 340, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 102, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 110, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 306, "battlerName": "Actor1_3", "dropItems": [{"kind": 3, "dataId": 297, "denominator": 1}, {"kind": 3, "dataId": 286, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 350, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}], "gold": 2000, "name": "Zythex", "note": "<Level: 15>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Multi-Layer HP Gauge Layers: 4>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Size: 150, 250>\n<Bestiary Lore>\n</Bestiary Lore>\n<Sideview Battler: zythex-battler>\n\n", "params": [3500, 0, 30, 50, 40, 50, 50, 50]}, {"id": 212, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 68, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 69, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 70, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 67, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Demon Az'k<PERSON>th", "note": "<Level: 80>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Multi-Layer HP Gauge Layers: 6>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Battler: demon>\n<Sideview Size: 200, 300>", "params": [1000, 0, 20, 20, 20, 20, 20, 20]}, {"id": 213, "actions": [{"skillId": 131, "rating": 5, "conditionType": 4, "conditionParam1": 54, "conditionParam2": 0}, {"skillId": 39, "rating": 5, "conditionType": 4, "conditionParam1": 54, "conditionParam2": 0}, {"skillId": 137, "rating": 5, "conditionType": 4, "conditionParam1": 55, "conditionParam2": 0}, {"skillId": 36, "rating": 5, "conditionType": 4, "conditionParam1": 55, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 51, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 400, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}], "gold": 3000, "name": "Sand Guardian", "note": "<Level: 18>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Multi-Layer HP Gauge Layers: 6>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Battler: specter_brown_large>\n<Sideview Size: 250, 250>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [4000, 0, 40, 40, 40, 40, 40, 40]}, {"id": 214, "actions": [{"skillId": 351, "rating": 5, "conditionType": 1, "conditionParam1": 2, "conditionParam2": 6}, {"skillId": 352, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 353, "rating": 5, "conditionType": 1, "conditionParam1": 4, "conditionParam2": 6}, {"skillId": 357, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 361, "rating": 5, "conditionType": 1, "conditionParam1": 6, "conditionParam2": 6}, {"skillId": 4, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 150, "rating": 3, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.5}, {"skillId": 149, "rating": 3, "conditionType": 2, "conditionParam1": 0.5, "conditionParam2": 1}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 2000, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}], "gold": 4000, "name": "Sarber<PERSON>", "note": "<Level: 25>\n<Level Variance: 0>\n<Item Drop 7 To 39: 30%>\n<Armor Drop 365 To 381: 20%>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 300%>\n<Sideview Battler: chimera_master>\n<Sideview Size: 150, 150>", "params": [5000, 0, 70, 50, 50, 50, 70, 50]}, {"id": 215, "actions": [{"skillId": 17, "rating": 1, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 110, "rating": 1, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 149, "rating": 1, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 356, "rating": 1, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 310, "rating": 1, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 341, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 362, "rating": 3, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.25}, {"skillId": 362, "rating": 2, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.5}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 3000, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}], "gold": 0, "name": "<PERSON><PERSON>", "note": "<Level: 20>\n<Level Variance: 0>\n<Item Drop 7 To 39: 30%>\n<Armor Drop 365 To 381: 20%>\n<Multi-Layer HP Gauge Layers: 7>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Size: 150, 200>\n<Sideview Battler: skelletonboss_master_sheet>", "params": [5000, 0, 40, 40, 40, 40, 40, 40]}, {"id": 216, "actions": [{"skillId": 376, "rating": 5, "conditionType": 4, "conditionParam1": 137, "conditionParam2": 0}, {"skillId": 150, "rating": 5, "conditionType": 4, "conditionParam1": 137, "conditionParam2": 0}, {"skillId": 377, "rating": 5, "conditionType": 4, "conditionParam1": 138, "conditionParam2": 0}, {"skillId": 149, "rating": 5, "conditionType": 4, "conditionParam1": 138, "conditionParam2": 0}, {"skillId": 375, "rating": 1, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 2, "value": 1}], "gold": 0, "name": "<PERSON><PERSON><PERSON><PERSON>", "note": "<Level: 40>\n<Level Variance: 0>\n<Multi-Layer HP Gauge Layers: 7>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: Kythera_sheet>\n<Sideview Size: 150, 150>", "params": [4000, 0, 10, 10, 10, 10, 20, 10]}, {"id": 217, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 372, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 374, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 373, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "<PERSON><PERSON><PERSON>", "note": "<Level: 50>\n<Level Variance: 0>\n<Multi-Layer HP Gauge Layers: 7>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: Varric_sheet>\n<Sideview Size: 150, 175>", "params": [4000, 0, 20, 20, 80, 20, 20, 20]}, {"id": 218, "actions": [{"skillId": 6, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 32, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 312, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 50, "denominator": 1}, {"kind": 3, "dataId": 288, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}], "gold": 250, "name": "Rotspore", "note": "<Level: 7>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Battler: boss-shroom_0002_dark>\n<Sideview Shadow Scale X: 800%>\n<Sideview Shadow Scale Y: 400%>\n<Sideview Size: 150, 150>\n<Bestiary Category: Rare>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [1800, 0, 50, 30, 80, 20, 30, 20]}, {"id": 219, "actions": [{"skillId": 7, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 41, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 349, "rating": 2, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 324, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 50, "denominator": 1}, {"kind": 3, "dataId": 287, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}], "gold": 250, "name": "Lycien", "note": "<Level: 7>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Battler: Werewolf_0004_blue>\n<Sideview Size: 150, 150>\n<Bestiary Category: Rare>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [1800, 0, 100, 30, 50, 20, 50, 20]}, {"id": 220, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 143, "rating": 3, "conditionType": 4, "conditionParam1": 110, "conditionParam2": 0}, {"skillId": 363, "rating": 5, "conditionType": 4, "conditionParam1": 111, "conditionParam2": 0}, {"skillId": 364, "rating": 5, "conditionType": 4, "conditionParam1": 111, "conditionParam2": 0}, {"skillId": 46, "rating": 3, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.5}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 500, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}, {"code": 14, "dataId": 37, "value": 1}], "gold": 10000, "name": "General <PERSON><PERSON>", "note": "<Level: 35>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Multi-Layer HP Gauge Layers: 3>\n<Sideview Shadow Scale X: 300%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Battler: stromgrave>\n<Sideview Size: 150, 175>", "params": [6500, 0, 70, 70, 30, 50, 50, 70]}, {"id": 221, "actions": [{"skillId": 180, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1070, "rating": 6, "conditionType": 1, "conditionParam1": 1, "conditionParam2": 3}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "<PERSON>", "note": "<Level: 30>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Multi-Layer HP Gauge Layers: 3>\n<Sideview Shadow Scale X: 200%>\n<Sideview Shadow Scale Y: 100%>\n<Sideview Battler: actor2-2_sheet>", "params": [3000, 0, 30, 20, 30, 20, 30, 20]}, {"id": 222, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 80, "rating": 6, "conditionType": 4, "conditionParam1": 99, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "<PERSON><PERSON><PERSON>", "note": "<Level: 25>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Multi-Layer HP Gauge Layers: 3>\n<Sideview Shadow Scale X: 300%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Battler: TowerKnight_0003_blue>\n<Sideview Size: 150, 175>", "params": [5000, 0, 10, 20, 10, 10, 20, 20]}, {"id": 223, "actions": [{"skillId": 118, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 368, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 366, "rating": 4, "conditionType": 4, "conditionParam1": 120, "conditionParam2": 0}, {"skillId": 369, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 370, "rating": 5, "conditionType": 6, "conditionParam1": 126, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 14, "dataId": 13, "value": 1}], "gold": 0, "name": "Arachno-Mech Body", "note": "<Level: 25>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Multi-Layer HP Gauge Layers: 3>\n<Sideview Shadow Scale X: 300%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Battler: destroyer grey>\n<Sideview Size: 150, 100>", "params": [5000, 0, 50, 50, 50, 50, 50, 50]}, {"id": 224, "actions": [{"skillId": 365, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 367, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 14, "dataId": 13, "value": 1}], "gold": 0, "name": "Arachno-Mech Cannons", "note": "<Level: 15>\n<Level Variance: 0>\n<Multi-Layer HP Gauge Layers: 3>\n<Sideview Shadow Scale X: 0%>\n<Sideview Shadow Scale Y: 0%>\n<Sideview Battler: Transparent>\n<Sideview Size: 150, 50>\n<ATB Field Gauge Face: ArachnoMechCannons, 0>\n<Multi-Layer HP Gauge Face: ArachnoMechCannons, 0>", "params": [600, 0, 30, 30, 30, 30, 30, 30]}, {"id": 225, "actions": [{"skillId": 94, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 118, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 14, "dataId": 13, "value": 1}], "gold": 0, "name": "Arachno-Mech Legs", "note": "<Level: 20>\n<Level Variance: 0>\n<Multi-Layer HP Gauge Layers: 3>\n<Sideview Shadow Scale X: 0%>\n<Sideview Shadow Scale Y: 0%>\n<Sideview Battler: Transparent>\n<Sideview Size: 150, 50>\n<ATB Field Gauge Face: ArachnoMechLegs, 0>\n<Multi-Layer HP Gauge Face: ArachnoMechLegs, 0>", "params": [1000, 0, 40, 40, 40, 40, 40, 40]}, {"id": 226, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 361, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 371, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 11, "dataId": 2, "value": 0.5}, {"code": 14, "dataId": 13, "value": 1}], "gold": 0, "name": "<PERSON><PERSON><PERSON>", "note": "<Level: 30>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Multi-Layer HP Gauge Layers: 3>\n<Sideview Shadow Scale X: 300%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Battler: dragon fire sheet>\n<Sideview Size: 150, 175>\n<Passive State: 122>", "params": [6000, 0, 60, 60, 60, 60, 60, 60]}, {"id": 227, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"skillId": 329, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Cryonix", "note": "<Level: 30>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Multi-Layer HP Gauge Layers: 3>\n<Sideview Shadow Scale X: 300%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Battler: dragon water sheet>\n<Sideview Size: 150, 175>", "params": [10000, 0, 10, 10, 10, 10, 10, 10]}, {"id": 228, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "<PERSON><PERSON><PERSON>", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 229, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}, {"skillId": 329, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Hydrosia", "note": "<Level: 30>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Multi-Layer HP Gauge Layers: 3>\n<Sideview Shadow Scale X: 300%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Battler: dragon water sheet>\n<Sideview Size: 150, 175>", "params": [10000, 0, 10, 10, 10, 10, 10, 10]}, {"id": 230, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Terrador", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 231, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "<PERSON><PERSON><PERSON><PERSON>", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 232, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Nyxara", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 233, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Valicor", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 234, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Blort Meatpunch", "note": "<Level: 30>\n<Level Variance: 0>\n<Sideview Battler: Goblin Brute A>\n<Sideview Size: 150, 175>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 200%>", "params": [99999, 0, 50, 10, 10, 10, 25, 10]}, {"id": 235, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "The Gobfather", "note": "<Level: 30>\n<Level Variance: 0>\n<Sideview Battler: goblin shaman1>\n<Sideview Size: 150, 175>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 200%>", "params": [99999, 0, 50, 10, 10, 10, 25, 10]}, {"id": 236, "actions": [{"skillId": 1059, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1072, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1065, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1074, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Nightmare King", "note": "<Level: 30>\n<Level Variance: 0>\n<Sideview Battler: king void>\n<Sideview Size: 150, 175>\n<Sideview Shadow Scale X: 200%>\n<Sideview Shadow Scale Y: 100%>", "params": [8000, 0, 30, 30, 30, 30, 50, 30]}, {"id": 237, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 143, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 47, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 61, "dataId": 0, "value": 1}], "gold": 0, "name": "<PERSON>", "note": "<Level: 30>\n<Level Variance: 0>\n<Sideview Battler: isaac-battler>\n<Sideview Size: 150, 150>\n<Sideview Shadow Scale X: 50%>\n<Sideview Shadow Scale Y: 50%>", "params": [8000, 0, 30, 30, 30, 30, 30, 30]}, {"id": 238, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1086, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1085, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 61, "dataId": 0, "value": 1}], "gold": 0, "name": "<PERSON><PERSON>", "note": "<Level: 30>\n<Level Variance: 0>\n<Sideview Battler: orman-battler>\n<Sideview Size: 150, 150>\n<Sideview Shadow Scale X: 50%>\n<Sideview Shadow Scale Y: 50%>", "params": [8000, 0, 30, 30, 30, 30, 30, 30]}, {"id": 239, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 102, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 110, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 118, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 61, "dataId": 0, "value": 1}], "gold": 0, "name": "<PERSON><PERSON>", "note": "<Level: 30>\n<Level Variance: 0>\n<Sideview Battler: Aiya_sheet_B>\n<Sideview Size: 150, 150>\n<Sideview Shadow Scale X: 50%>\n<Sideview Shadow Scale Y: 50%>", "params": [8000, 0, 30, 30, 30, 30, 30, 30]}, {"id": 240, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 149, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 61, "dataId": 0, "value": 1}], "gold": 0, "name": "<PERSON><PERSON><PERSON>", "note": "<Level: 30>\n<Level Variance: 0>\n<Sideview Battler: keldric-battler>\n<Sideview Size: 150, 150>\n<Sideview Shadow Scale X: 50%>\n<Sideview Shadow Scale Y: 50%>", "params": [8000, 0, 30, 30, 30, 30, 30, 30]}, {"id": 241, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 242, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 243, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 244, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 245, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 246, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 247, "actions": [{"skillId": 1078, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1079, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1080, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 1082, "rating": 6, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.66}, {"skillId": 1081, "rating": 6, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.33}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}, {"code": 14, "dataId": 12, "value": 1}, {"code": 23, "dataId": 4, "value": 0}, {"code": 23, "dataId": 5, "value": 9.99}], "gold": 0, "name": "<PERSON><PERSON><PERSON>", "note": "<Level: 9>\n<Level Variance: 0>\n<Item Drop 7 To 39: 10%>\n<Armor Drop 365 To 381: 20%>\n<Sideview Battler: keldric-battler>\n<Menu Portrait: keldric_full>\n<Sideview Shadow Scale X: 200%>", "params": [1, 0, 20, 20, 20, 20, 20, 20]}, {"id": 248, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 249, "actions": [{"skillId": 161, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 160, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 162, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 163, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 164, "rating": 5, "conditionType": 4, "conditionParam1": 170, "conditionParam2": 0}, {"skillId": 165, "rating": 5, "conditionType": 4, "conditionParam1": 171, "conditionParam2": 0}, {"skillId": 166, "rating": 5, "conditionType": 4, "conditionParam1": 172, "conditionParam2": 0}, {"skillId": 167, "rating": 5, "conditionType": 4, "conditionParam1": 173, "conditionParam2": 0}, {"skillId": 168, "rating": 5, "conditionType": 4, "conditionParam1": 174, "conditionParam2": 0}, {"skillId": 169, "rating": 6, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.5}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}], "gold": 0, "name": "<PERSON><PERSON><PERSON>", "note": "<Level: 50>\n<Multi-Layer HP Gauge Layers: 3>\n<Level Variance: 0>\n<Item Drop 7 To 39: 10%>\n<Armor Drop 365 To 381: 20%>\n<Sideview Battler: valadus_sheet>\n<Sideview Shadow Scale X: 200%>\n<Sideview Size: 150, 200>\n\n", "params": [6000, 0, 20, 20, 20, 20, 20, 10]}, {"id": 250, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "-----Extra Monsters", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 251, "actions": [{"skillId": 15, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 234, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 345, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 59, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 30, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.1}, {"code": 31, "dataId": 1, "value": 0}], "gold": 500, "name": "Gnome ", "note": "<Item Drop 7 To 39: 6%>\n<Armor Drop 365 To 381: 3%>\n<Sideview Shadow Scale X: 50%>\nsteamgnome brown\n<Sideview Battler: steamgnome brown>", "params": [400, 0, 20, 20, 20, 20, 10, 20]}, {"id": 252, "actions": [{"skillId": 306, "rating": 5, "conditionType": 1, "conditionParam1": 2, "conditionParam2": 3}, {"skillId": 33, "rating": 5, "conditionType": 1, "conditionParam1": 1, "conditionParam2": 3}, {"skillId": 150, "rating": 5, "conditionType": 1, "conditionParam1": 3, "conditionParam2": 3}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 5000, "name": "Mimic", "note": "<Level: 2>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Battler: cursed_chest3>\n<Bestiary Category: Rare>", "params": [1000, 0, 20, 40, 30, 40, 30, 30]}, {"id": 253, "actions": [{"skillId": 102, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 110, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 118, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 125, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 131, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 137, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 51, "denominator": 20}, {"kind": 1, "dataId": 52, "denominator": 2}, {"kind": 1, "dataId": 52, "denominator": 2}], "exp": 50, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 50, "name": "Posessed Book", "note": "<Sideview Shadow Scale X: 300%>\n<Sideview Shadow Scale X: 200%>\n<Battle UI Offset Y: +50>\n<Visual Hover Effect>\n Base: 20\n Speed: 20\n Rate: 5.0\n Death: floor\n</Visual Hover Effect>", "params": [300, 0, 20, 20, 25, 25, 20, 20]}, {"id": 254, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 345, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 355, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.1}, {"code": 31, "dataId": 1, "value": 0}], "gold": 600, "name": "<PERSON>gger", "note": "<Level: 7>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Battler: THFM_0000_DSBRWN>\n<Sideview Shadow Scale X: 200%>\n<Bestiary Category: Rare>\n<Bestiary Battleback 2: Forest>\n", "params": [500, 0, 25, 30, 30, 30, 50, 50]}, {"id": 255, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 345, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 348, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 355, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 3, "dataId": 277, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 150, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.1}, {"code": 31, "dataId": 1, "value": 0}], "gold": 800, "name": "Mugger <PERSON>", "note": "<Level: 8>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Battler: THFM_0002_DSRED>\n<Sideview Shadow Scale X: 200%>\n<Bestiary Category: Rare>\n<Bestiary Battleback 2: Forest>", "params": [1000, 0, 40, 40, 30, 30, 50, 60]}, {"id": 256, "actions": [{"skillId": 15, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 19, "denominator": 1}, {"kind": 1, "dataId": 20, "denominator": 1}, {"kind": 1, "dataId": 21, "denominator": 1}], "exp": 250, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 1000, "name": "Alchemist", "note": "<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Battler: MAGF_0005_DS-RED>\n<Sideview Shadow Scale X: 200%>\n<Bestiary Category: Rare>\n<Hide Multi-Layer HP Gauge>", "params": [100, 0, 10, 10, 10, 10, 0, 10]}, {"id": 257, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 321, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 37, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 80, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 120, "name": "<PERSON>", "note": "<Item Drop 7 To 10: 2%>\n<Item Drop 40 To 45: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.5%>\n<Armor Drop 365 To 373: 0.5%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: sahuagin sheet red>", "params": [600, 0, 30, 30, 40, 30, 30, 30]}, {"id": 258, "actions": [{"skillId": 7, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 14, "dataId": 1, "value": 1}], "gold": 0, "name": "<PERSON><PERSON>", "note": "<Level: 2>\n<Level Variance: 0>\n<Sideview Battler: orman-battler>\n<Sideview Shadow Scale X: 200%>\n<Hide in Bestiary>\n<Hide Multi-Layer HP Gauge>\n", "params": [1000, 0, 15, 20, 20, 20, 30, 20]}, {"id": 259, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 47, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 49, "rating": 1, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 60, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 100, "name": "Elite Paladin", "note": "<Level: 9>\n<Level Variance: 0>\n<Item Drop 7 To 15: 2%>\n<Item Drop 23 To 39: 1%>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: KNTM sheet BLU>\n", "params": [1000, 0, 45, 35, 40, 30, 30, 30]}, {"id": 260, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 335, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 253, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 337, "rating": 5, "conditionType": 4, "conditionParam1": 118, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 8, "value": 5}, {"code": 14, "dataId": 13, "value": 1}], "gold": 0, "name": "Dawnguard Heavy", "note": "<Level: 15>\n<Level Variance: 1>\nItem Drop 7 To 15: 0%\nItem Drop 23 To 39: 0%\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: knight_heavy>", "params": [2500, 0, 60, 50, 60, 50, 70, 60]}, {"id": 261, "actions": [{"skillId": 4, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 143, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 47, "rating": 4, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.66}, {"skillId": 46, "rating": 4, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.33}, {"skillId": 48, "rating": 5, "conditionType": 4, "conditionParam1": 118, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 8, "value": 5}, {"code": 14, "dataId": 13, "value": 1}], "gold": 0, "name": "Dawnguard Zealot", "note": "<Level: 16>\n<Level Variance: 1>\nItem Drop 7 To 15: 0%\nItem Drop 23 To 39: 0%\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: knight_zealot>", "params": [2500, 0, 60, 40, 70, 40, 70, 60]}, {"id": 262, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 49, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 336, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 338, "rating": 5, "conditionType": 4, "conditionParam1": 118, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 8, "value": 5}, {"code": 14, "dataId": 13, "value": 1}], "gold": 0, "name": "Dawnguard Cleric", "note": "<Level: 14>\n<Level Variance: 1>\nItem Drop 7 To 15: 0%\nItem Drop 23 To 39: 0%\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: knight_cleric>", "params": [2500, 0, 60, 30, 50, 30, 70, 60]}, {"id": 263, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 143, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 49, "rating": 5, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.75}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Sister <PERSON><PERSON><PERSON>", "note": "<Level: 20>\n<Level Variance: 0>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: Actor1-8 sheet>", "params": [100, 0, 15, 10, 15, 15, 10, 25]}, {"id": 264, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 355, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 73, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 72, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.15}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "<PERSON>ed the Silencer", "note": "<Level: 20>\n<Level Variance: 0>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: Actor3-5 sheet>", "params": [150, 0, 20, 10, 10, 10, 20, 30]}, {"id": 265, "actions": [{"skillId": 74, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 75, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 22, "dataId": 2, "value": 0.05}], "gold": 0, "name": "<PERSON><PERSON><PERSON>", "note": "<Level: 20>\n<Level Variance: 0>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: people2-8 dualweilding>", "params": [200, 0, 30, 10, 20, 10, 15, 50]}, {"id": 266, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 77, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 76, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "<PERSON><PERSON>", "note": "<Level: 20>\n<Level Variance: 0>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: people3-7 sheet>", "params": [250, 0, 20, 20, 40, 20, 15, 10]}, {"id": 267, "actions": [{"skillId": 4, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 79, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 80, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 78, "rating": 5, "conditionType": 2, "conditionParam1": 0, "conditionParam2": 0.75}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Champion <PERSON><PERSON><PERSON>", "note": "<Level: 20>\n<Level Variance: 0>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: <PERSON><PERSON><PERSON>>", "params": [300, 0, 30, 50, 20, 30, 20, 20]}, {"id": 268, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "Chillreaver Ayon", "note": "<Level: 20>\n<Level Variance: 0>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: chillreaver>", "params": [9999, 0, 30, 10, 10, 10, 20, 10]}, {"id": 269, "actions": [{"skillId": 376, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 150, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 377, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 149, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 2, "value": 1}], "gold": 0, "name": "<PERSON><PERSON><PERSON><PERSON>", "note": "<Level: 40>\n<Level Variance: 0>\n<Multi-Layer HP Gauge Layers: 7>\n<Sideview Shadow Scale X: 200%>\n<Sideview Battler: Kythera_sheet>\n<Sideview Size: 150, 150>", "params": [5000, 0, 10, 10, 10, 10, 60, 10]}, {"id": 270, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 355, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "<PERSON>", "note": "<Sideview Shadow Scale X: 200%>\n<Sideview Battler: darkelf_assassin_sheet>\n", "params": [1000, 0, 20, 20, 10, 20, 20, 20]}, {"id": 271, "actions": [{"skillId": 7, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 41, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 324, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 110, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 331, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 332, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 50, "denominator": 1}, {"kind": 3, "dataId": 287, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}, {"code": 11, "dataId": 3, "value": 0.5}], "gold": 250, "name": "Frostclaw Manticore", "note": "<Passive State: 104>\n<Level: 15>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Battler: manticore_ice>\n<Sideview Size: 150, 100>\n<Bestiary Category: Rare>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [4000, 0, 100, 40, 50, 20, 60, 20]}, {"id": 272, "actions": [{"skillId": 7, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 41, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 324, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 118, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 333, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 334, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 50, "denominator": 1}, {"kind": 3, "dataId": 287, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 63, "dataId": 1, "value": 1}, {"code": 14, "dataId": 13, "value": 1}, {"code": 11, "dataId": 4, "value": 0.5}], "gold": 250, "name": "Stormclaw Manticore", "note": "<Passive State: 104>\n<Level: 15>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 200%>\n<Sideview Battler: manticore_lit>\n<Sideview Size: 150, 100>\n<Bestiary Category: Rare>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [4000, 0, 100, 30, 50, 20, 70, 20]}, {"id": 273, "actions": [{"skillId": 131, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 39, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 326, "rating": 3, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 28, "rating": 4, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"kind": 1, "dataId": 50, "denominator": 1}, {"kind": 1, "dataId": 51, "denominator": 2}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 14, "dataId": 13, "value": 1}], "gold": 250, "name": "Sandworm", "note": "<Level: 30>\n<Level Variance: 0>\n<Item Drop 7 To 39: 20%>\n<Armor Drop 365 To 381: 10%>\n<Sideview Battler: rockworm_sand>\n<Sideview Shadow Scale X: 400%>\n<Sideview Shadow Scale Y: 300%>\n<Sideview Size: 150, 155>\n<Bestiary Category: Rare>\n<Bestiary Battleback 2: Sand>\n<Bestiary Lore>\n</Bestiary Lore>", "params": [3500, 0, 0, 40, 120, 40, 60, 50]}, {"id": 274, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 275, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 276, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 277, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 278, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 279, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 280, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 281, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 282, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 283, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 284, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 285, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 286, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 287, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 288, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 289, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 290, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 291, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 292, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 293, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 294, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 295, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 296, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 297, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 298, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 299, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "test", "note": "<Sideview Battler: WARM_0007_ds-blu>\n<Sideview Size: 150, 150>\n<Sideview Shadow Scale X: 200%>\n<Sideview Shadow Scale Y: 120%>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.5%>\n<Weapon Drop 2 To 36: 0.3%>\n<Armor Drop 2 To 156: 0.3%>\n<Hide from Bestiary>", "params": [999999, 0, 10, 0, 0, 0, 20, 0]}, {"id": 300, "actions": [{"skillId": 4, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}, {"skillId": 102, "rating": 5, "conditionType": 0, "conditionParam1": 0, "conditionParam2": 0}], "battlerHue": 0, "battlerName": "Actor1_3", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 300, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 1000, "name": "<PERSON>", "note": "<Random Variant>\n Angry: 5\n Tough: 5\n Agile: 5\n Proud: 5\n Normal: 80\n</Random Variant>\n<Sideview Battler: golem_magma>\n<Sideview Size: 200, 200>\n<Level: 50>\n<Sideview Shadow Scale X: 200%>\n<Sideview Shadow Scale Y: 120%>\n<Item Drop 7 To 10: 2%>\n<Item Drop 46 To 49: 5%>\n<Item Drop 11 To 15: 1%>\n<Item Drop 23 To 39: 0.5%>\n<Weapon Drop 2 To 36: 0.3%>\n<Armor Drop 2 To 156: 0.3%>\n<Hide from Bestiary>", "params": [5000, 0, 1, 0, 10, 0, 1, 0]}]