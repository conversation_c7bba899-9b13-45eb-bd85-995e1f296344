//=============================================================================
// GhostWorld_Visuals.js
//=============================================================================
/*:
 * @target MZ
 * @plugindesc v1.1 Ghost World visuals + Blurry Vignette (map). Desaturate, tint, noise, blur, and radial blur vignette.
 * <AUTHOR> (AI Assistant)
 * @help
 * This plugin provides two visual effects for Scene_Map:
 *
 * 1) Ghost World (post-processing stack)
 *    - Desaturate (via ColorMatrixFilter.saturate)
 *    - Cool screen-tint overlay
 *    - Subtle noise (NoiseFilter)
 *    - Soft blur (BlurFilter)
 *    - Optional animated tint flicker
 *
 * 2) Blurry Vignette (custom shader)
 *    - Sharp center, blur increases toward edges
 *    - Tunable radius, feather, strength, samples
 *
 * Effects only apply to the map visuals (base sprite), not UI windows.
 * Compatible with VisuStella; this plugin does not modify their code.
 *
 * Plugin Commands
 * - EnableGhostWorld / DisableGhostWorld
 * - EnableBlurryVignette / DisableBlurryVignette
 *
 * You can use both at the same time; filters are stacked automatically.
 *
 * @param Ghost Default Desaturate
 * @type number
 * @min 0
 * @max 1
 * @decimals 2
 * @desc 0 = normal color, 1 = fully desaturated. Maps to saturate(-amount).
 * @default 0.75
 *
 * @param Ghost Default Tint Color
 * @type color
 * @desc Screen-overlay tint color for a ghostly vibe.
 * @default #8FB4FF
 *
 * @param Ghost Default Tint Strength
 * @type number
 * @min 0
 * @max 1
 * @decimals 2
 * @desc Alpha for the tint overlay.
 * @default 0.22
 *
 * @param Ghost Default Noise
 * @type number
 * @min 0
 * @max 1
 * @decimals 2
 * @desc Noise intensity (PIXI NoiseFilter.noise).
 * @default 0.08
 *
 * @param Ghost Default Blur
 * @type number
 * @min 0
 * @max 8
 * @decimals 1
 * @desc Blur strength (PIXI BlurFilter strength). 0 disables blur.
 * @default 1.1
 *
 * @param Ghost Default Animate
 * @type boolean
 * @desc If true, gently animates tint strength for a subtle flicker.
 * @default true
 *
 * @param Ghost Default Color Mode
 * @type select
 * @option None
 * @option BlackAndWhite
 * @option Greyscale
 * @option Sepia
 * @option Negative
 * @option Kodachrome
 * @option Polaroid
 * @option Predator
 * @option Technicolor
 * @option Vintage
 * @option Browni
 * @option LSD
 * @option BGRSwap
 * @desc Optional creative color transform applied before desaturation.
 * @default Greyscale
 *
 * @param Ghost Default Hue
 * @type number
 * @min -360
 * @max 360
 * @desc Hue rotation in degrees (applied before desaturate). 0 to skip.
 * @default -15
 *
 * @param Ghost Default Contrast
 * @type number
 * @min -1
 * @max 1
 * @decimals 2
 * @desc Contrast adjustment (-1..1). 0 to skip.
 * @default 0.05
 *
 * @param Ghost Default Brightness
 * @type number
 * @min 0
 * @max 2
 * @decimals 2
 * @desc Brightness multiplier (0..2). 1 to skip.
 * @default 0.95
 *
 * @param Ghost Default Flicker Speed
 * @type number
 * @min 0
 * @max 10
 * @decimals 2
 * @desc Speed of tint flicker when Animate is true.
 * @default 0.15
 *
 * @param Vignette Default Radius
 * @type number
 * @min 0
 * @max 1
 * @decimals 2
 * @desc Clear radius around screen center in normalized units (0..1 of half-diagonal).
 * @default 0.24
 *
 * @param Vignette Default Feather
 * @type number
 * @min 0
 * @max 1
 * @decimals 2
 * @desc Width of the transition from clear to blur (normalized).
 * @default 0.25
 *
 * @param Vignette Default StrengthPx
 * @text Vignette Default Blur Strength (px)
 * @type number
 * @min 0
 * @max 16
 * @decimals 1
 * @desc Maximum blur sample offset in pixels at edges.
 * @default 8.0
 *
 * @param Vignette Default Samples
 * @type number
 * @min 4
 * @max 24
 * @desc Number of sampling taps for blur. Higher = smoother but slower.
 * @default 14
 *
 * @command EnableGhostWorld
 * @text Enable Ghost World
 * @desc Enable Ghost World visuals on the map.
 *
 * @arg Desaturate
 * @type number
 * @min 0
 * @max 1
 * @decimals 2
 * @default 0.4
 *
 * @arg TintColor
 * @text Tint Color
 * @type color
 * @default #88B0FF
 *
 * @arg TintStrength
 * @text Tint Strength
 * @type number
 * @min 0
 * @max 1
 * @decimals 2
 * @default 0.18
 *
 * @arg Noise
 * @type number
 * @min 0
 * @max 1
 * @decimals 2
 * @default 0.06
 *
 * @arg Blur
 * @type number
 * @min 0
 * @max 8
 * @decimals 1
 * @default 0.8
 *
 * @arg Animate
 * @type boolean
 * @default true
 *
 * @arg ColorMode
 * @text Color Mode
 * @type select
 * @option None
 * @option BlackAndWhite
 * @option Greyscale
 * @option Sepia
 * @option Negative
 * @option Kodachrome
 * @option Polaroid
 * @option Predator
 * @option Technicolor
 * @option Vintage
 * @option Browni
 * @option LSD
 * @option BGRSwap
 * @default None
 *
 * @arg Hue
 * @type number
 * @min -360
 * @max 360
 * @default 0
 *
 * @arg Contrast
 * @type number
 * @min -1
 * @max 1
 * @decimals 2
 * @default 0
 *
 * @arg Brightness
 * @type number
 * @min 0
 * @max 2
 * @decimals 2
 * @default 1
 *
 * @arg FlickerSpeed
 * @text Flicker Speed
 * @type number
 * @min 0
 * @max 10
 * @decimals 2
 * @default 0.25
 *
 * @command DisableGhostWorld
 * @text Disable Ghost World
 * @desc Disable Ghost World visuals on the map.
 *
 * @command EnableBlurryVignette
 * @text Enable Blurry Vignette
 * @desc Enables the blurry vignette effect on the map.
 *
 * @arg Radius
 * @type number
 * @min 0
 * @max 1
 * @decimals 2
 * @default 0.28
 *
 * @arg Feather
 * @type number
 * @min 0
 * @max 1
 * @decimals 2
 * @default 0.22
 *
 * @arg StrengthPx
 * @text Blur Strength (px)
 * @type number
 * @min 0
 * @max 16
 * @decimals 1
 * @default 6.0
 *
 * @arg Samples
 * @type number
 * @min 4
 * @max 24
 * @default 10
 *
 * @arg FollowPlayer
 * @text Center Follows Player
 * @type boolean
 * @default true
 *
 * @arg CenterOffsetX
 * @text Center Offset X (norm)
 * @type number
 * @decimals 3
 * @default 0
 *
 * @arg CenterOffsetY
 * @text Center Offset Y (norm)
 * @type number
 * @decimals 3
 * @default 0
 *
 * @arg ChromaticPx
 * @text Chromatic Aberration (px)
 * @type number
 * @min 0
 * @max 4
 * @decimals 2
 * @default 0.6
 *
 * @command EnableDistortion
 * @text Enable Distortion
 * @desc Enables a heat-haze displacement distortion overlay.
 *
 * @arg Strength
 * @type number
 * @min 0
 * @max 32
 * @decimals 1
 * @default 6
 *
 * @arg Speed
 * @type number
 * @min 0
 * @max 20
 * @decimals 2
 * @default 2.0
 *
 * @command DisableDistortion
 * @text Disable Distortion
 * @desc Disables the distortion overlay.
 *
 * @param Wave Default AmplitudeX
 * @type number
 * @min 0
 * @max 32
 * @decimals 2
 * @desc Horizontal wave amplitude in pixels.
 * @default 3.0
 *
 * @param Wave Default AmplitudeY
 * @type number
 * @min 0
 * @max 32
 * @decimals 2
 * @desc Vertical wave amplitude in pixels.
 * @default 0.0
 *
 * @param Wave Default Wavelength
 * @type number
 * @min 4
 * @max 1024
 * @desc Wave length in pixels (higher = slower oscillation across screen).
 * @default 120
 *
 * @param Wave Default Speed
 * @type number
 * @min 0
 * @max 10
 * @decimals 2
 * @desc Wave travel speed.
 * @default 1.0
 *
 * @param Wave Default OverlayAlpha
 * @type number
 * @min 0
 * @max 1
 * @decimals 2
 * @desc Opacity of the wavy overlay mix (0..1).
 * @default 0.35
 *
 * @command EnableWavyOverlay
 * @text Enable Wavy Overlay
 * @desc Chrono-like sine wave overlay across the screen.
 *
 * @arg AmplitudeX
 * @type number
 * @min 0
 * @max 32
 * @decimals 2
 * @default 3.0
 *
 * @arg AmplitudeY
 * @type number
 * @min 0
 * @max 32
 * @decimals 2
 * @default 0.0
 *
 * @arg Wavelength
 * @type number
 * @min 4
 * @max 1024
 * @default 120
 *
 * @arg Speed
 * @type number
 * @min 0
 * @max 10
 * @decimals 2
 * @default 1.0
 *
 * @arg OverlayAlpha
 * @type number
 * @min 0
 * @max 1
 * @decimals 2
 * @default 0.35
 *
 * @command DisableWavyOverlay
 * @text Disable Wavy Overlay
 * @desc Disables the sine wave overlay.
 *
 * @command DisableBlurryVignette
 * @text Disable Blurry Vignette
 * @desc Disables the blurry vignette effect on the map.
 */

(() => {
    const pluginName = 'GhostWorld_Visuals';

    const params = PluginManager.parameters(pluginName);
    const GHOST_DEFAULTS = {
        desaturate: Number(params['Ghost Default Desaturate'] || 0.75),
        tintColor: String(params['Ghost Default Tint Color'] || '#8FB4FF'),
        tintStrength: Number(params['Ghost Default Tint Strength'] || 0.22),
        noise: Number(params['Ghost Default Noise'] || 0.08),
        blur: Number(params['Ghost Default Blur'] || 1.1),
        animate: String(params['Ghost Default Animate'] || 'true') === 'true',
        colorMode: String(params['Ghost Default Color Mode'] || 'Greyscale'),
        hue: Number(params['Ghost Default Hue'] || -15),
        contrast: Number(params['Ghost Default Contrast'] || 0.05),
        brightness: Number(params['Ghost Default Brightness'] || 0.95),
        flickerSpeed: Number(params['Ghost Default Flicker Speed'] || 0.15),
    };
    const VIGN_DEFAULTS = {
        radius: Number(params['Vignette Default Radius'] || 0.24),
        feather: Number(params['Vignette Default Feather'] || 0.25),
        strengthPx: Number(params['Vignette Default StrengthPx'] || 8.0),
        samples: Math.max(4, Math.min(24, Number(params['Vignette Default Samples'] || 14))),
    };
    const DISTORT_DEFAULTS = {
        strength: 6,
        speed: 2.0,
    };
    const WAVE_DEFAULTS = {
        amplitudeX: Number(params['Wave Default AmplitudeX'] || 3.0),
        amplitudeY: Number(params['Wave Default AmplitudeY'] || 0.0),
        wavelength: Number(params['Wave Default Wavelength'] || 120),
        speed: Number(params['Wave Default Speed'] || 1.0),
        overlayAlpha: Number(params['Wave Default OverlayAlpha'] || 0.35),
    };

    // ---------------------------------------------------------------------
    // Game_System: store states
    // ---------------------------------------------------------------------
    const _Game_System_initialize = Game_System.prototype.initialize;
    Game_System.prototype.initialize = function() {
        _Game_System_initialize.call(this);
        this._ghostWorld = this._ghostWorld || {
            enabled: false,
            desaturate: GHOST_DEFAULTS.desaturate,
            tintColor: GHOST_DEFAULTS.tintColor,
            tintStrength: GHOST_DEFAULTS.tintStrength,
            noise: GHOST_DEFAULTS.noise,
            blur: GHOST_DEFAULTS.blur,
            animate: GHOST_DEFAULTS.animate,
            flickerSpeed: GHOST_DEFAULTS.flickerSpeed,
            _t: 0,
        };
        this._blurryVignette = this._blurryVignette || {
            enabled: false,
            radius: VIGN_DEFAULTS.radius,
            feather: VIGN_DEFAULTS.feather,
            strengthPx: VIGN_DEFAULTS.strengthPx,
            samples: VIGN_DEFAULTS.samples,
            followPlayer: true,
            centerOffsetX: 0,
            centerOffsetY: 0,
            chromaticPx: 0.6,
        };
        this._distortion = this._distortion || {
            enabled: false,
            strength: DISTORT_DEFAULTS.strength,
            speed: DISTORT_DEFAULTS.speed,
            t: 0,
        };
        this._wavyOverlay = this._wavyOverlay || {
            enabled: false,
            amplitudeX: WAVE_DEFAULTS.amplitudeX,
            amplitudeY: WAVE_DEFAULTS.amplitudeY,
            wavelength: WAVE_DEFAULTS.wavelength,
            speed: WAVE_DEFAULTS.speed,
            overlayAlpha: WAVE_DEFAULTS.overlayAlpha,
            t: 0,
        };
    };

    Game_System.prototype.enableGhostWorld = function(settings) {
        this._ghostWorld = Object.assign({}, this._ghostWorld, settings || {});
        this._ghostWorld.enabled = true;
    };
    Game_System.prototype.disableGhostWorld = function() {
        if (!this._ghostWorld) this.initialize();
        this._ghostWorld.enabled = false;
    };
    Game_System.prototype.ghostWorld = function() {
        if (!this._ghostWorld) this.initialize();
        return this._ghostWorld;
    };

    Game_System.prototype.enableBlurryVignette = function(settings) {
        this._blurryVignette = Object.assign({}, this._blurryVignette, settings || {});
        this._blurryVignette.enabled = true;
    };
    Game_System.prototype.disableBlurryVignette = function() {
        if (!this._blurryVignette) this.initialize();
        this._blurryVignette.enabled = false;
    };
    Game_System.prototype.blurryVignette = function() {
        if (!this._blurryVignette) this.initialize();
        return this._blurryVignette;
    };

    // ---------------------------------------------------------------------
    // Commands
    // ---------------------------------------------------------------------
    PluginManager.registerCommand(pluginName, 'EnableGhostWorld', args => {
        const settings = {
            desaturate: Number(args.Desaturate ?? GHOST_DEFAULTS.desaturate),
            tintColor: String(args.TintColor ?? GHOST_DEFAULTS.tintColor),
            tintStrength: Number(args.TintStrength ?? GHOST_DEFAULTS.tintStrength),
            noise: Number(args.Noise ?? GHOST_DEFAULTS.noise),
            blur: Number(args.Blur ?? GHOST_DEFAULTS.blur),
            animate: String(args.Animate ?? String(GHOST_DEFAULTS.animate)) === 'true',
            colorMode: String(args.ColorMode ?? GHOST_DEFAULTS.colorMode),
            hue: Number(args.Hue ?? GHOST_DEFAULTS.hue),
            contrast: Number(args.Contrast ?? GHOST_DEFAULTS.contrast),
            brightness: Number(args.Brightness ?? GHOST_DEFAULTS.brightness),
            flickerSpeed: Number(args.FlickerSpeed ?? GHOST_DEFAULTS.flickerSpeed),
        };
        $gameSystem.enableGhostWorld(settings);
        if (SceneManager._scene && SceneManager._scene._spriteset) {
            const sp = SceneManager._scene._spriteset;
            sp.refreshGhostWorld();
        }
    });
    PluginManager.registerCommand(pluginName, 'DisableGhostWorld', () => {
        $gameSystem.disableGhostWorld();
        if (SceneManager._scene && SceneManager._scene._spriteset) {
            SceneManager._scene._spriteset.refreshGhostWorld();
        }
    });

    PluginManager.registerCommand(pluginName, 'EnableBlurryVignette', args => {
        const settings = {
            radius: Number(args.Radius ?? VIGN_DEFAULTS.radius),
            feather: Number(args.Feather ?? VIGN_DEFAULTS.feather),
            strengthPx: Number(args.StrengthPx ?? VIGN_DEFAULTS.strengthPx),
            samples: Math.max(4, Math.min(24, Number(args.Samples ?? VIGN_DEFAULTS.samples))),
            followPlayer: String(args.FollowPlayer ?? 'true') === 'true',
            centerOffsetX: Number(args.CenterOffsetX ?? 0),
            centerOffsetY: Number(args.CenterOffsetY ?? 0),
            chromaticPx: Number(args.ChromaticPx ?? 0.6),
        };
        $gameSystem.enableBlurryVignette(settings);
        if (SceneManager._scene && SceneManager._scene._spriteset) {
            SceneManager._scene._spriteset.refreshBlurryVignette();
        }
    });
    PluginManager.registerCommand(pluginName, 'DisableBlurryVignette', () => {
        $gameSystem.disableBlurryVignette();
        if (SceneManager._scene && SceneManager._scene._spriteset) {
            SceneManager._scene._spriteset.refreshBlurryVignette();
        }
    });

    PluginManager.registerCommand(pluginName, 'EnableDistortion', args => {
        const settings = {
            strength: Number(args.Strength ?? DISTORT_DEFAULTS.strength),
            speed: Number(args.Speed ?? DISTORT_DEFAULTS.speed),
        };
        $gameSystem._distortion = Object.assign($gameSystem._distortion || {}, settings);
        $gameSystem._distortion.enabled = true;
        if (SceneManager._scene && SceneManager._scene._spriteset) {
            SceneManager._scene._spriteset.refreshDistortion();
        }
    });
    PluginManager.registerCommand(pluginName, 'DisableDistortion', () => {
        if (!$gameSystem._distortion) $gameSystem.initialize();
        $gameSystem._distortion.enabled = false;
        if (SceneManager._scene && SceneManager._scene._spriteset) {
            SceneManager._scene._spriteset.refreshDistortion();
        }
    });
    PluginManager.registerCommand(pluginName, 'EnableWavyOverlay', args => {
        const settings = {
            amplitudeX: Number(args.AmplitudeX ?? WAVE_DEFAULTS.amplitudeX),
            amplitudeY: Number(args.AmplitudeY ?? WAVE_DEFAULTS.amplitudeY),
            wavelength: Number(args.Wavelength ?? WAVE_DEFAULTS.wavelength),
            speed: Number(args.Speed ?? WAVE_DEFAULTS.speed),
            overlayAlpha: Number(args.OverlayAlpha ?? WAVE_DEFAULTS.overlayAlpha),
        };
        $gameSystem._wavyOverlay = Object.assign($gameSystem._wavyOverlay || {}, settings);
        $gameSystem._wavyOverlay.enabled = true;
        if (SceneManager._scene && SceneManager._scene._spriteset) {
            SceneManager._scene._spriteset.refreshWavyOverlay();
        }
    });
    PluginManager.registerCommand(pluginName, 'DisableWavyOverlay', () => {
        if (!$gameSystem._wavyOverlay) $gameSystem.initialize();
        $gameSystem._wavyOverlay.enabled = false;
        if (SceneManager._scene && SceneManager._scene._spriteset) {
            SceneManager._scene._spriteset.refreshWavyOverlay();
        }
    });

    // ---------------------------------------------------------------------
    // Utility
    // ---------------------------------------------------------------------
    function hexToPixiTint(hex) {
        if (typeof hex !== 'string') return 0x88B0FF;
        const cleaned = hex.replace('#', '');
        return parseInt(cleaned, 16);
    }

    // ---------------------------------------------------------------------
    // Custom Radial Blur Vignette Filter
    // ---------------------------------------------------------------------
    const FRAG = `
varying vec2 vTextureCoord;
uniform sampler2D uSampler;
uniform vec2 uResolution;
uniform float radius;
uniform float feather;
uniform float strengthPx;
uniform float samples;
 uniform vec2 centerUv;
 uniform float chromaPx;

float normDist(vec2 uv) {
  float d = distance(uv, centerUv);
  float halfDiag = length(vec2(0.5, 0.5));
  return d / halfDiag;
}

void main() {
  vec2 uv = vTextureCoord;
  vec4 baseCol = texture2D(uSampler, uv);
  float d = normDist(uv);
  float t = smoothstep(radius, radius + max(0.0001, feather), d);
  if (t <= 0.0001) { gl_FragColor = baseCol; return; }

  float px = strengthPx * t;
  vec2 texel = 1.0 / uResolution;
  int N = int(samples);
  vec4 acc = baseCol;
  float wsum = 1.0;
  float ang = 2.39996323;
  float rStep = (px) / float(max(1, N - 1));
  for (int i = 0; i < 24; i++) {
    if (i >= N) break;
    float fi = float(i+1);
    float r = fi * rStep;
    float a = fi * ang;
    vec2 dir = vec2(cos(a), sin(a));
    vec2 off = dir * r * texel;
    vec4 col = texture2D(uSampler, uv + off);
    float w = 1.0 - fi / float(N+1);
    acc += col * w;
    wsum += w;
  }
  vec4 blurCol = acc / wsum;
  // chromatic aberration
  vec2 dir = normalize(uv - centerUv);
  vec2 cOff = dir * (chromaPx * t) / uResolution;
  float rr = texture2D(uSampler, uv + cOff).r;
  float gg = texture2D(uSampler, uv).g;
  float bb = texture2D(uSampler, uv - cOff).b;
  vec4 chroma = vec4(rr, gg, bb, 1.0);
  blurCol = mix(blurCol, chroma, 0.35 * t);
  gl_FragColor = mix(baseCol, blurCol, clamp(t, 0.0, 1.0));
}
`;

    class RadialBlurVignetteFilter extends PIXI.Filter {
        constructor(radius, feather, strengthPx, samples) {
            super(undefined, FRAG, {
                uResolution: new Float32Array([Graphics.width, Graphics.height]),
                radius: radius,
                feather: feather,
                strengthPx: strengthPx,
                samples: samples,
                centerUv: new Float32Array([0.5, 0.5]),
                chromaPx: 0.6,
            });
            this._radius = radius;
            this._feather = feather;
            this._strengthPx = strengthPx;
            this._samples = samples;
        }
        setResolution(w, h) {
            this.uniforms.uResolution[0] = w;
            this.uniforms.uResolution[1] = h;
        }
        applySettings({ radius, feather, strengthPx, samples, centerUv, chromaticPx }) {
            if (radius != null) { this._radius = Number(radius); this.uniforms.radius = this._radius; }
            if (feather != null) { this._feather = Number(feather); this.uniforms.feather = this._feather; }
            if (strengthPx != null) { this._strengthPx = Number(strengthPx); this.uniforms.strengthPx = this._strengthPx; }
            if (samples != null) { this._samples = Math.max(4, Math.min(24, Number(samples))); this.uniforms.samples = this._samples; }
            if (centerUv) {
                this.uniforms.centerUv[0] = Number(centerUv[0]);
                this.uniforms.centerUv[1] = Number(centerUv[1]);
            }
            if (chromaticPx != null) { this.uniforms.chromaPx = Number(chromaticPx); }
        }
    }

    // ---------------------------------------------------------------------
    // Spriteset_Map: filter management
    // ---------------------------------------------------------------------
    const _Spriteset_Map_initialize = Spriteset_Map.prototype.initialize;
    Spriteset_Map.prototype.initialize = function() {
        _Spriteset_Map_initialize.call(this);
        // Only keep the wavy overlay; disable other effects
        this._gw_filters = null;
        this._gw_overlay = null;
        this._gw_applied = false;
        this._vignette = null;
        this._distortFilter = null;
        this._distortSprite = null;
        this._waveFilter = null;            // sine-wave vertex-less filter
        // Focus only on the sine overlay
        this.refreshWavyOverlay();
    };

    Spriteset_Map.prototype._activeFilters = function() {
        const list = [];
        // Only apply wave filter
        if (this._waveFilter) list.push(this._waveFilter);
        return list;
    };
    Spriteset_Map.prototype._applyActiveFilters = function() {
        const filters = this._activeFilters();
        this.filters = filters.length ? filters : null;
        // Ensure full-screen filter area on the spriteset
        if (!this.filterArea) this.filterArea = new PIXI.Rectangle(0, 0, Graphics.width, Graphics.height);
        else {
            this.filterArea.width = Graphics.width;
            this.filterArea.height = Graphics.height;
        }
    };

    // ---- Ghost World ----
    Spriteset_Map.prototype.refreshGhostWorld = function() {
        const state = $gameSystem.ghostWorld();
        if (state.enabled) {
            this._gwEnsureCreated();
            this._gwApply(state);
        } else {
            this._gwRemove();
        }
        this._applyActiveFilters();
    };

    Spriteset_Map.prototype._gwEnsureCreated = function() {
        if (this._gw_applied) return;
        this._gw_applied = true;
        const color = new PIXI.filters.ColorMatrixFilter();
        const noise = new PIXI.filters.NoiseFilter();
        const blur = new PIXI.filters.BlurFilter();
        this._gw_filters = { color, noise, blur };

        const overlay = new PIXI.Sprite(PIXI.Texture.WHITE);
        overlay.anchor.set(0);
        overlay.position.set(0, 0);
        overlay.width = Graphics.width;
        overlay.height = Graphics.height;
        overlay.blendMode = PIXI.BLEND_MODES.SCREEN;
        overlay.alpha = 0.0;
        this._gw_overlay = overlay;
        this.addChild(this._gw_overlay);
    };

    Spriteset_Map.prototype._gwRemove = function() {
        if (!this._gw_applied) return;
        this._gw_applied = false;
        if (this._gw_overlay) {
            this.removeChild(this._gw_overlay);
            this._gw_overlay.destroy({ children: false, baseTexture: false });
            this._gw_overlay = null;
        }
        this._gw_filters = null;
    };

    Spriteset_Map.prototype._gwApply = function(state) {
        if (!this._gw_filters) return;
        const { color, noise, blur } = this._gw_filters;

        // Creative color transforms
        color.reset();
        const mode = String(state.colorMode || 'None');
        const hue = Number(state.hue || 0);
        const contrast = Number(state.contrast || 0);
        const brightness = Number(state.brightness || 1);

        // Base modes
        switch (mode) {
            case 'BlackAndWhite': color.blackAndWhite(true); break;
            case 'Greyscale': color.greyscale(1, true); break;
            case 'Sepia': color.sepia(true); break;
            case 'Negative': color.negative(true); break;
            case 'Kodachrome': color.kodachrome(true); break;
            case 'Polaroid': color.polaroid(true); break;
            case 'Predator': color.predator(0.8, true); break;
            case 'Technicolor': color.technicolor(true); break;
            case 'Vintage': color.sepia(true); color.brightness(1.05, true); break;
            case 'Browni': color.browni(true); break;
            case 'LSD': color.lsd(true); break;
            case 'BGRSwap': color.toBGR(true); break;
            case 'None': default: break;
        }
        if (hue !== 0) color.hue(hue, true);
        if (contrast !== 0) color.contrast(contrast, true);
        if (brightness !== 1) color.brightness(brightness, true);
        const desat = Number(state.desaturate || 0);
        if (desat > 0) color.saturate(-desat, true);

        // Noise; animate subtly by frame
        noise.noise = Math.max(0, Math.min(1, Number(state.noise || 0)));
        noise.seed = (noise.seed || 0) + 0.01;

        // Blur with gentle breathing animation
        const baseB = Math.max(0, Number(state.blur || 0));
        const animB = baseB > 0 ? baseB * (1 + 0.05 * Math.sin(Graphics.frameCount * 0.05)) : 0;
        blur.blur = animB;
        blur.blurX = animB;
        blur.blurY = animB;

        // Tint overlay
        if (this._gw_overlay) {
            this._gw_overlay.tint = hexToPixiTint(state.tintColor);
            this._gw_overlay.alpha = Math.max(0, Math.min(1, Number(state.tintStrength || 0)));
            this._gw_overlay.width = Graphics.width;
            this._gw_overlay.height = Graphics.height;
        }
    };

    // ---- Blurry Vignette ----
    Spriteset_Map.prototype.refreshBlurryVignette = function() {
        const st = $gameSystem.blurryVignette();
        if (st.enabled) {
            if (!this._vignette) {
                this._vignette = new RadialBlurVignetteFilter(st.radius, st.feather, st.strengthPx, st.samples);
                this._vignette.setResolution(Graphics.width, Graphics.height);
            } else {
                this._vignette.applySettings(st);
                this._vignette.setResolution(Graphics.width, Graphics.height);
            }
        } else if (this._vignette) {
            this._vignette = null;
        }
        this._applyActiveFilters();
    };

    // ---- Distortion (heat haze) using DisplacementFilter with scrolling noise ----
    Spriteset_Map.prototype.refreshDistortion = function() {
        const st = $gameSystem._distortion || { enabled: false };
        if (st.enabled) {
            if (!this._distortFilter) {
                const gfx = new PIXI.Graphics();
                // Generate a simple noise-like texture (soft stripes) as displacement map
                // Using a RenderTexture for performance
                const rt = PIXI.RenderTexture.create({ width: 256, height: 256 });
                const sprite = new PIXI.Sprite(rt);
                this._distortSprite = sprite;
                const renderer = Graphics.app.renderer;
                const temp = new PIXI.Container();
                temp.addChild(gfx);
                for (let y = 0; y < 256; y += 8) {
                    const a = 0.5 + 0.5 * Math.sin(y * 0.1);
                    gfx.beginFill(PIXI.utils.rgb2hex([0.5 * a, 0.5 * a, 0.5]));
                    gfx.drawRect(0, y, 256, 8);
                    gfx.endFill();
                }
                renderer.render(temp, { renderTexture: rt, clear: true });
                temp.removeChildren();
                gfx.destroy(true);

                const disp = new PIXI.filters.DisplacementFilter(sprite);
                disp.scale.x = st.strength || 6;
                disp.scale.y = (st.strength || 6) * 0.5;
                this._distortFilter = disp;
                // place map above the base sprite so it scrolls
                sprite.anchor.set(0);
                sprite.position.set(0, 0);
                sprite.width = Graphics.width;
                sprite.height = Graphics.height;
                sprite.alpha = 0; // invisible map
                this.addChild(sprite);
            } else {
                this._distortFilter.scale.x = st.strength;
                this._distortFilter.scale.y = st.strength * 0.5;
            }
        } else if (this._distortFilter) {
            if (this._distortSprite) {
                this.removeChild(this._distortSprite);
                this._distortSprite.destroy({ children: false, baseTexture: true });
                this._distortSprite = null;
            }
            this._distortFilter = null;
        }
        this._applyActiveFilters();
    };

    // Update: keep overlay size and vignette resolution in sync; animate flicker
    const _Spriteset_Map_update = Spriteset_Map.prototype.update;
    Spriteset_Map.prototype.update = function() {
        _Spriteset_Map_update.call(this);
        // Only update wave filter uniforms
        // (time, amplitude, wavelength, resolution)
        if (this._waveFilter) {
            const st = $gameSystem._wavyOverlay || { speed: 0 };
            st.t = (st.t || 0) + (Number(st.speed) || 0) * 0.0166667; // ~per frame
            this._waveFilter.uniforms.time = st.t;
            this._waveFilter.uniforms.amplitude = new Float32Array([
                Number(st.amplitudeX) || 0,
                Number(st.amplitudeY) || 0,
            ]);
            this._waveFilter.uniforms.wavelength = Number(st.wavelength) || 120;
            this._waveFilter.uniforms.resolution = new Float32Array([Graphics.width, Graphics.height]);
            this._waveFilter.uniforms.overlayAlpha = Math.max(0, Math.min(1, Number(st.overlayAlpha) || 0));
        }
    };

    // ---- Wavy Overlay (Chrono-like) ----
    const WAVE_FRAG = `
varying vec2 vTextureCoord;
uniform sampler2D uSampler;
uniform vec2 resolution;
uniform vec2 amplitude; // px
uniform float wavelength; // px
uniform float time; // seconds-ish
uniform float overlayAlpha; // mix factor

void main() {
  vec2 uv = vTextureCoord;
  // Convert to pixel space for consistent amplitude scaling
  vec2 px = uv * resolution;
  float wx = amplitude.x * sin((px.y / wavelength) + time);
  float wy = amplitude.y * sin((px.x / wavelength) + time * 0.9);
  vec2 off = vec2(wx, wy) / resolution;
  vec4 waveCol = texture2D(uSampler, uv + off);
  gl_FragColor = vec4(waveCol.rgb, clamp(overlayAlpha, 0.0, 1.0));
}
`;

    Spriteset_Map.prototype.refreshWavyOverlay = function() {
        const st = $gameSystem._wavyOverlay || { enabled: false };
        if (st.enabled) {
            if (!this._waveFilter) this._waveFilter = new PIXI.Filter(undefined, WAVE_FRAG);
            // Initialize uniforms each refresh
            this._waveFilter.uniforms.resolution = new Float32Array([Graphics.width, Graphics.height]);
            this._waveFilter.uniforms.amplitude = new Float32Array([st.amplitudeX || 0, st.amplitudeY || 0]);
            this._waveFilter.uniforms.wavelength = Number(st.wavelength) || 120;
            this._waveFilter.uniforms.time = Number(st.t) || 0;
            this._waveFilter.uniforms.overlayAlpha = Math.max(0, Math.min(1, Number(st.overlayAlpha) || 0));
        } else {
            this._waveFilter = null;
        }
        this._applyActiveFilters();
    };
})();


