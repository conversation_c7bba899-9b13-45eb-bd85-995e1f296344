{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "tg-bbg-vlg-01-a", "battleback2Name": "", "bgm": {"name": "A_Peaceful_Town_Loop_Attempt", "pan": 0, "pitch": 100, "volume": 20}, "bgs": {"name": "IDA20-20Medium20Intensity", "pan": 0, "pitch": 100, "volume": 30}, "disableDashing": false, "displayName": "Haventown", "encounterList": [], "encounterStep": 30, "height": 40, "note": "<Zoom: 200%>", "parallaxLoopX": true, "parallaxLoopY": false, "parallaxName": "!Clouds", "parallaxShow": true, "parallaxSx": -2, "parallaxSy": -1, "scrollType": 0, "specifyBattleback": true, "tilesetId": 22, "width": 50, "data": [1649, 1649, 2577, 2578, 1649, 1649, 1649, 1649, 1649, 1649, 1649, 1649, 1650, 1658, 3376, 2856, 2816, 2854, 3362, 3382, 2832, 2816, 2840, 3384, 3352, 3344, 3344, 3348, 3372, 3372, 3382, 2860, 2064, 2072, 2860, 3384, 3372, 3352, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 1649, 1649, 2577, 2578, 1649, 1649, 1649, 1649, 1649, 1649, 1649, 1650, 1658, 3379, 3383, 1634, 1571, 1632, 3388, 2859, 2845, 2844, 2846, 2861, 3384, 3372, 3372, 3382, 2859, 2861, 1641, 1641, 2064, 2072, 1620, 2859, 2853, 3384, 3372, 3372, 3352, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 1657, 1657, 2577, 2578, 1657, 1657, 1657, 1657, 1657, 1657, 1657, 1658, 3379, 3383, 1641, 1642, 1571, 1640, 1641, 1641, 1641, 1641, 1641, 1641, 1641, 1641, 1641, 1641, 1641, 1627, 1649, 1649, 2577, 2578, 1640, 1641, 2857, 2838, 2836, 2852, 3384, 3372, 3352, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 3364, 3380, 2064, 2072, 2859, 2849, 2849, 2861, 3387, 3377, 3377, 3377, 3383, 1634, 1649, 1650, 1571, 1648, 1649, 1649, 1649, 1649, 1649, 1649, 5411, 5414, 1649, 1649, 1649, 1635, 1657, 1657, 2577, 2578, 1648, 1649, 1632, 2856, 2844, 2846, 2849, 2861, 3384, 3372, 3372, 3372, 3372, 3372, 3352, 3344, 3372, 3382, 2064, 2072, 1621, 1641, 1641, 1641, 1641, 1641, 1641, 1641, 1641, 1642, 1657, 1658, 1571, 1656, 1657, 1657, 5922, 5908, 5908, 5924, 5417, 5420, 1657, 1657, 1657, 1643, 3378, 3380, 2064, 2072, 1656, 1657, 1651, 1641, 1641, 1641, 1641, 1641, 1641, 1641, 1641, 1641, 1620, 2858, 3360, 3344, 2861, 2082, 2053, 2086, 1634, 1649, 1649, 1649, 1649, 1649, 1649, 1649, 1649, 1650, 2850, 2836, 2816, 2836, 2836, 2852, 5904, 5888, 5888, 5890, 5908, 5924, 2858, 3387, 3377, 3377, 3353, 3368, 2064, 2072, 3378, 3380, 1635, 1649, 1649, 1649, 1649, 1649, 1649, 1649, 1649, 1649, 1632, 2860, 3360, 3344, 2068, 2053, 2086, 3390, 1634, 1649, 1649, 1649, 1649, 1649, 1657, 1657, 1657, 1658, 2832, 2816, 2816, 2816, 2816, 2840, 5904, 5888, 5888, 5912, 5916, 5926, 2833, 2836, 2836, 2852, 3360, 3368, 2064, 2072, 3360, 3368, 1659, 1657, 1657, 1649, 1657, 1657, 1657, 1657, 1657, 1657, 1640, 1641, 3384, 3372, 2048, 2072, 3390, 1641, 1642, 1649, 1649, 1649, 1649, 1649, 1632, 2859, 2838, 2836, 2817, 2816, 2816, 2816, 2816, 2840, 5928, 5916, 5916, 5926, 2850, 2836, 2817, 2816, 2816, 2840, 3362, 3382, 2064, 2072, 3360, 3368, 1621, 1641, 1642, 1649, 2859, 2849, 2849, 2861, 1632, 2862, 1648, 1649, 1632, 2859, 2048, 2072, 1634, 1649, 1650, 5922, 5908, 5908, 5924, 1657, 1640, 1641, 2856, 2844, 2844, 2824, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2840, 3376, 2082, 2049, 2072, 3384, 3382, 1634, 1649, 1650, 1657, 5922, 5908, 5908, 5924, 1640, 1641, 1656, 1657, 1640, 1641, 2048, 2072, 1642, 1649, 1650, 5904, 5888, 5888, 5912, 2858, 1648, 1649, 1640, 1641, 1641, 2856, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3376, 2064, 2052, 2086, 2862, 1641, 1642, 1657, 1658, 2858, 5904, 5888, 5888, 5912, 1648, 1649, 1632, 2862, 1648, 1649, 2576, 2578, 1650, 1649, 1650, 5904, 5888, 5888, 5912, 2860, 1656, 1657, 1648, 1649, 1649, 1640, 1641, 2856, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2824, 2816, 2854, 3388, 2064, 2072, 2862, 1634, 1649, 1650, 2862, 3386, 2860, 5904, 5888, 5888, 5912, 1656, 1657, 1640, 1641, 1656, 1657, 2576, 2578, 1650, 1657, 1658, 5928, 5916, 5916, 5926, 3378, 3364, 3380, 1656, 1657, 1657, 1648, 1649, 1640, 1641, 2856, 2844, 2844, 2844, 2844, 2844, 2854, 1641, 1642, 1571, 1640, 1641, 2064, 2072, 1641, 1642, 1657, 1658, 3378, 3347, 3380, 5928, 5916, 5916, 5926, 3378, 3380, 1648, 1649, 1632, 2859, 2576, 2578, 1650, 2862, 3386, 2850, 2836, 2836, 2852, 3360, 3344, 3350, 3377, 3389, 2858, 1656, 1657, 1648, 1649, 1640, 1641, 1641, 1641, 1641, 1641, 1642, 1649, 1650, 1571, 1648, 1649, 2577, 2578, 1649, 1650, 3387, 3366, 3345, 3344, 3346, 3380, 2851, 2861, 3378, 3345, 3368, 1656, 1657, 1640, 1641, 2576, 2578, 1658, 3378, 3370, 2856, 2844, 2824, 2840, 3360, 3348, 3382, 2850, 2836, 2819, 2852, 3386, 1656, 1657, 1648, 1649, 1649, 1649, 1649, 1649, 1650, 1657, 1658, 1571, 1656, 1657, 2577, 2578, 1657, 1658, 2862, 3360, 3348, 3372, 3356, 3382, 2848, 3378, 3345, 3344, 3346, 3364, 3380, 1648, 1649, 2048, 2072, 2858, 3360, 3350, 3377, 3389, 2832, 2840, 3384, 3382, 2850, 2817, 2816, 2816, 2840, 3385, 3366, 3380, 1656, 1657, 1657, 1657, 1657, 1657, 1658, 3378, 3380, 2848, 3387, 3389, 2064, 2072, 2862, 3378, 3364, 3349, 3382, 2858, 3388, 2850, 2842, 3360, 3344, 3348, 3372, 3372, 3382, 1656, 1657, 2048, 2072, 2848, 3384, 3382, 2859, 2849, 2856, 2846, 2849, 2849, 2845, 2824, 2816, 2816, 2822, 2861, 3384, 3374, 3377, 3377, 3377, 3377, 3377, 3377, 3377, 3373, 3382, 2848, 2849, 2861, 2064, 2072, 3378, 3349, 3372, 3382, 2850, 2819, 2836, 2821, 2854, 3362, 3372, 3382, 2859, 2849, 2861, 2082, 2068, 2048, 2072, 2833, 2837, 2861, 2083, 2081, 3678, 2081, 2081, 2081, 2081, 2832, 2816, 2816, 2840, 5411, 5410, 5410, 5410, 5410, 5410, 5410, 5414, 2081, 2081, 2081, 2081, 3678, 2081, 2081, 2057, 2072, 3384, 3382, 2850, 2836, 2817, 2820, 2844, 2854, 3379, 3383, 2859, 2861, 2082, 2068, 2068, 2049, 2048, 2048, 2072, 2856, 2854, 2083, 2087, 3386, 2850, 2852, 3378, 3380, 2859, 2845, 2844, 2844, 2854, 5409, 5408, 5408, 5408, 5408, 5408, 5408, 5412, 2858, 3379, 3377, 3389, 2850, 2836, 2852, 2064, 2072, 2850, 2836, 2817, 2820, 2844, 2854, 3379, 3377, 3383, 2862, 2082, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2054, 2081, 2081, 2087, 3387, 3371, 2832, 2840, 3360, 3346, 3380, 7283, 7282, 7282, 7286, 5417, 5416, 5416, 5416, 5416, 5416, 5416, 5420, 2860, 3376, 2851, 2849, 2825, 2816, 2840, 3675, 3677, 2834, 2844, 2844, 2854, 3379, 3377, 3383, 2862, 2082, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 1641, 1641, 1641, 1641, 3388, 2832, 2840, 3384, 3356, 3382, 7281, 7280, 7280, 7284, 7282, 7282, 7282, 7282, 7282, 7282, 7282, 7286, 3378, 3370, 2848, 3386, 2832, 2816, 2840, 2064, 2072, 2860, 3387, 3377, 3367, 3383, 2082, 2068, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2576, 2578, 1649, 1649, 1649, 1649, 1651, 2856, 2826, 2852, 3376, 2862, 7281, 7280, 7280, 7284, 7288, 7288, 7288, 7288, 7288, 7288, 7288, 7292, 3360, 3368, 2860, 3388, 2832, 2816, 2840, 2088, 2058, 2084, 2859, 2861, 3388, 2082, 2053, 2076, 2056, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2576, 2578, 1657, 1657, 1657, 1657, 1635, 1632, 2832, 2840, 3385, 3381, 7281, 7280, 7280, 7284, 3378, 3364, 3380, 2850, 2836, 2837, 2861, 3378, 3345, 3350, 3389, 2850, 2821, 2844, 2846, 2861, 2088, 2058, 2068, 2068, 2068, 2053, 2086, 2858, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 3387, 3366, 3364, 3380, 1659, 1651, 2856, 2826, 2852, 3388, 7289, 7288, 7288, 7292, 3362, 3372, 3382, 2832, 2816, 2840, 3378, 3345, 3348, 3382, 2850, 2821, 2854, 3378, 3364, 3380, 2862, 2088, 2076, 2076, 2076, 2086, 2859, 2855, 2088, 2060, 2076, 2056, 2048, 2052, 2076, 2060, 2076, 2076, 2048, 2072, 2862, 3384, 3352, 3368, 2862, 1635, 1640, 2856, 2826, 2836, 2836, 2836, 2836, 2852, 3388, 2850, 2836, 2817, 2816, 2840, 3384, 3372, 3382, 2850, 2821, 2854, 3378, 3345, 3344, 3346, 3364, 3364, 3364, 3365, 3389, 1641, 1641, 1641, 1641, 2080, 1641, 2064, 2048, 2072, 1641, 2080, 1641, 1641, 2048, 2072, 1641, 2862, 3384, 3354, 3380, 1659, 1648, 1632, 2856, 2844, 2844, 2824, 2816, 2822, 2849, 2845, 2844, 2824, 2816, 2818, 2836, 2836, 2836, 2821, 2854, 3378, 3345, 3348, 3372, 3372, 3372, 3372, 3372, 3382, 1642, 1649, 1649, 1649, 1649, 2579, 1649, 2577, 2576, 2578, 1649, 2579, 1649, 1649, 2576, 2578, 1649, 1640, 2862, 3360, 3368, 2862, 1656, 1651, 1641, 3387, 3389, 2856, 2816, 2854, 3378, 3364, 3380, 2856, 2844, 2844, 2844, 2844, 2844, 2854, 3387, 3373, 3372, 3382, 5411, 5410, 5410, 5410, 5410, 5410, 5414, 1657, 1657, 1657, 1657, 2579, 1657, 2577, 2576, 2578, 1657, 2579, 1657, 1657, 2576, 2578, 1657, 1648, 1651, 3384, 3374, 3366, 3380, 1635, 1649, 1640, 1641, 1642, 1571, 1640, 3384, 3372, 3374, 3377, 3389, 1641, 1641, 1641, 1642, 5411, 5410, 5410, 5410, 5414, 5409, 5408, 5408, 5408, 5408, 5408, 5412, 3378, 3365, 3389, 2862, 2080, 3390, 2064, 2048, 2072, 3390, 2080, 3387, 3366, 2048, 2072, 2858, 1656, 1635, 1651, 2862, 3384, 3369, 1659, 1657, 1648, 1649, 1650, 1571, 1648, 1640, 1641, 1641, 1641, 1642, 1649, 1649, 1649, 1650, 5409, 5408, 5408, 5408, 5412, 5417, 5416, 5416, 5416, 5416, 5416, 5420, 3360, 3368, 2862, 2082, 2051, 2068, 2049, 2048, 2050, 2068, 2051, 2084, 3384, 2048, 2072, 2848, 3386, 1659, 1635, 1651, 2862, 3361, 3364, 3380, 1656, 1657, 1658, 1571, 1656, 1648, 1649, 1649, 1649, 1650, 1657, 1657, 1657, 1658, 5417, 5416, 5416, 5416, 5420, 7282, 7282, 7282, 7282, 7282, 7282, 7286, 3360, 3368, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2068, 2048, 2072, 2860, 3361, 3380, 1659, 1635, 1632, 3384, 3372, 3374, 3377, 3366, 3380, 2832, 2852, 1656, 1657, 1657, 1657, 1658, 2862, 3378, 3364, 3380, 7283, 7282, 7282, 7282, 7286, 7288, 7288, 7288, 7288, 7288, 7288, 7292, 3360, 3368, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2084, 3384, 3354, 3380, 1659, 1640, 1641, 1641, 1641, 2862, 3384, 3369, 2832, 2818, 2836, 2836, 2836, 2836, 2852, 3387, 3353, 3344, 3368, 7289, 7288, 7288, 7288, 7292, 3378, 3380, 2850, 2852, 3386, 2862, 3378, 3345, 3368, 2064, 2048, 2048, 2048, 2052, 2076, 2056, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2084, 3384, 3354, 3380, 1648, 1649, 1649, 1649, 1651, 2862, 3376, 2832, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3384, 3356, 3374, 3389, 2850, 2836, 2852, 3387, 3357, 3382, 2832, 2840, 3361, 3365, 3373, 3372, 3382, 2088, 2056, 2048, 2052, 2086, 3386, 2088, 2056, 2048, 2052, 2076, 2048, 2048, 2048, 2050, 2084, 3384, 3369, 1656, 1657, 1657, 1657, 1635, 1632, 3376, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3388, 2850, 2836, 2817, 2816, 2818, 2852, 3388, 2851, 2845, 2854, 3362, 3382, 2862, 1641, 1641, 1641, 2064, 2048, 2072, 3387, 3375, 3389, 2064, 2048, 2072, 3387, 2048, 2048, 2048, 2048, 2072, 2862, 3361, 3364, 3364, 3364, 3380, 1659, 1632, 3385, 3381, 2856, 2844, 2844, 2844, 2844, 2824, 2816, 2818, 2837, 2845, 2844, 2844, 2824, 2816, 2822, 2849, 2855, 3379, 3377, 3383, 2862, 1627, 1649, 1649, 1649, 2577, 2576, 2578, 1649, 1649, 1649, 2577, 2576, 2578, 1649, 2048, 2048, 2048, 2048, 2050, 2084, 3384, 3372, 3372, 3352, 3368, 2862, 1651, 2862, 3361, 3364, 3364, 3364, 3364, 3380, 2856, 2824, 2816, 2840, 3387, 3366, 3380, 2856, 2844, 2854, 3378, 3365, 3383, 2862, 1641, 1642, 1635, 1657, 1657, 1657, 2577, 2576, 2578, 1657, 1657, 1657, 2577, 2576, 2578, 1657, 2048, 2048, 2048, 2048, 2048, 2050, 2068, 2084, 2862, 3384, 3354, 3380, 1635, 1651, 3384, 3372, 3372, 3372, 3372, 3374, 3389, 2856, 2844, 2818, 2861, 3384, 3374, 3377, 3377, 3377, 3373, 3382, 2862, 1627, 1649, 1650, 1643, 3379, 3389, 2082, 2049, 2048, 2050, 2084, 2862, 2082, 2049, 2048, 2050, 2068, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2084, 2862, 3360, 3368, 1659, 1635, 1640, 1620, 2862, 1621, 1641, 1641, 1641, 1641, 1642, 1571, 1640, 1641, 1641, 1641, 1641, 1620, 2859, 2861, 1642, 1635, 1657, 1658, 3387, 3383, 2082, 2049, 2048, 2048, 2048, 2050, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2084, 3384, 3374, 3381, 1659, 1648, 1640, 1641, 1642, 1649, 1649, 1649, 1649, 1650, 1571, 1648, 1649, 1649, 1649, 1649, 1640, 1641, 1642, 1650, 1643, 3379, 3389, 2082, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2068, 2084, 3388, 2858, 1656, 1648, 1649, 1650, 1657, 1657, 1657, 1657, 1658, 1571, 1656, 1657, 1657, 1657, 1657, 1648, 1649, 1650, 1658, 3379, 3383, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2084, 2833, 2852, 1656, 1657, 1658, 2858, 3378, 3364, 3364, 3380, 2832, 2852, 3378, 3364, 3380, 2858, 1656, 1657, 1658, 3378, 3370, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3169, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3162, 3172, 3172, 3172, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3180, 3180, 3160, 3154, 3173, 3185, 3174, 3172, 3172, 3172, 3173, 3197, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3180, 3190, 0, 3192, 3180, 3180, 3180, 3177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3194, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3180, 3164, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3196, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3196, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3172, 3172, 3157, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3157, 3180, 3180, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3194, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3194, 0, 0, 0, 0, 0, 0, 3186, 3157, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3169, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3169, 3173, 3197, 0, 0, 3195, 3185, 3181, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3162, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3194, 0, 0, 0, 0, 0, 0, 3186, 3157, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3162, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3184, 0, 0, 0, 0, 0, 3186, 3157, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3162, 3188, 0, 0, 3194, 0, 0, 3186, 3172, 3172, 3178, 0, 0, 0, 0, 3186, 3157, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3182, 3185, 3185, 3167, 3185, 3185, 3181, 3180, 3160, 3154, 3172, 3172, 3172, 3172, 3157, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3184, 0, 0, 0, 0, 3192, 3180, 3180, 3180, 3180, 3180, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3169, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3194, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3176, 0, 0, 3194, 0, 0, 0, 0, 0, 0, 0, 0, 3194, 0, 0, 0, 0, 0, 3184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3162, 3188, 0, 3169, 3172, 3172, 3188, 0, 0, 0, 3186, 3172, 3155, 3188, 0, 0, 3187, 3185, 3191, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3182, 3185, 3181, 3180, 3160, 3154, 3172, 3173, 3185, 3181, 3180, 3160, 3154, 3173, 3185, 3191, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3180, 3180, 3177, 0, 0, 0, 3192, 3180, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 101, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 865, 0, 0, 0, 0, 0, 0, 696, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 629, 0, 631, 0, 0, 0, 0, 0, 109, 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 873, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 559, 0, 0, 0, 544, 638, 546, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 567, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 891, 841, 842, 891, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 696, 0, 0, 0, 0, 0, 0, 0, 858, 859, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 848, 849, 850, 851, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 536, 561, 0, 0, 0, 0, 0, 865, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 891, 0, 891, 892, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 865, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 873, 873, 0, 0, 0, 0, 0, 0, 0, 710, 0, 0, 0, 0, 0, 0, 0, 891, 891, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 629, 0, 631, 0, 0, 0, 873, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 891, 891, 891, 891, 0, 0, 0, 0, 0, 536, 537, 538, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 637, 0, 639, 0, 0, 0, 0, 841, 842, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 891, 891, 891, 891, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 848, 849, 850, 851, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 891, 891, 0, 584, 893, 0, 733, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 725, 689, 0, 0, 0, 0, 892, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 891, 0, 0, 741, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 558, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 536, 537, 538, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 536, 630, 538, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 841, 842, 0, 841, 842, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 841, 842, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 848, 849, 850, 851, 885, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 424, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 629, 0, 631, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 637, 0, 639, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 99, 100, 0, 0, 0, 0, 0, 0, 0, 0, 841, 842, 0, 841, 842, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 733, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 629, 0, 631, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 559, 556, 557, 558, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 637, 0, 639, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 725, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 683, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 513, 514, 515, 0, 531, 532, 533, 534, 535, 0, 0, 0, 0, 665, 666, 0, 0, 669, 670, 725, 0, 858, 859, 0, 665, 666, 556, 557, 558, 0, 734, 0, 0, 0, 0, 0, 0, 556, 557, 558, 0, 548, 549, 550, 551, 0, 0, 621, 622, 623, 521, 522, 523, 0, 539, 540, 541, 542, 543, 0, 0, 0, 0, 673, 674, 0, 0, 0, 0, 0, 86, 866, 867, 868, 673, 674, 564, 565, 566, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 566, 696, 556, 557, 558, 0, 0, 680, 536, 630, 538, 529, 530, 0, 0, 547, 548, 549, 550, 551, 0, 559, 0, 0, 0, 0, 0, 0, 0, 0, 872, 94, 866, 867, 876, 877, 0, 572, 573, 574, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 574, 0, 564, 565, 566, 0, 0, 0, 584, 585, 586, 537, 538, 0, 0, 0, 556, 557, 558, 696, 0, 567, 0, 700, 0, 0, 0, 0, 0, 98, 880, 873, 866, 867, 876, 0, 0, 488, 0, 0, 0, 0, 495, 0, 0, 597, 598, 599, 516, 0, 701, 0, 572, 573, 574, 0, 524, 0, 592, 593, 594, 545, 546, 0, 0, 0, 564, 565, 566, 0, 0, 0, 0, 0, 0, 733, 0, 0, 0, 106, 880, 873, 874, 875, 876, 0, 0, 496, 497, 490, 499, 500, 503, 0, 0, 605, 606, 607, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 600, 601, 602, 0, 0, 0, 0, 0, 572, 573, 574, 0, 0, 101, 102, 0, 0, 741, 0, 0, 0, 0, 880, 881, 882, 883, 884, 101, 102, 504, 505, 506, 506, 506, 511, 0, 0, 613, 614, 615, 0, 98, 0, 0, 858, 859, 0, 96, 97, 0, 608, 609, 610, 0, 0, 0, 705, 0, 86, 866, 867, 868, 0, 109, 110, 0, 0, 0, 0, 0, 0, 724, 888, 889, 891, 18, 19, 109, 110, 0, 287, 286, 277, 700, 0, 0, 0, 621, 622, 623, 0, 106, 0, 86, 866, 867, 868, 0, 105, 0, 0, 544, 569, 0, 0, 0, 0, 872, 94, 866, 867, 876, 877, 0, 0, 0, 0, 517, 518, 519, 0, 0, 0, 891, 891, 26, 27, 559, 696, 352, 287, 277, 286, 0, 0, 0, 0, 536, 630, 538, 0, 0, 872, 94, 866, 867, 876, 0, 710, 0, 0, 0, 0, 0, 0, 0, 0, 880, 873, 874, 875, 876, 885, 0, 0, 0, 516, 525, 526, 527, 0, 0, 0, 0, 0, 0, 0, 567, 0, 344, 277, 287, 278, 0, 0, 0, 0, 544, 638, 546, 0, 0, 880, 873, 874, 875, 876, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 880, 881, 882, 883, 884, 885, 578, 579, 0, 0, 544, 545, 546, 0, 559, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 681, 880, 881, 882, 883, 884, 0, 0, 0, 725, 0, 0, 0, 0, 0, 0, 888, 889, 2, 891, 892, 585, 586, 587, 588, 98, 0, 0, 0, 0, 567, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 700, 0, 616, 617, 618, 619, 888, 889, 0, 16, 17, 0, 559, 0, 0, 0, 0, 0, 0, 0, 0, 0, 891, 7, 891, 592, 593, 594, 595, 596, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 734, 683, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 624, 625, 626, 627, 0, 295, 0, 24, 25, 0, 567, 0, 0, 0, 716, 0, 0, 0, 0, 680, 0, 0, 0, 600, 601, 602, 603, 604, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 98, 0, 0, 0, 0, 0, 0, 0, 0, 632, 633, 634, 635, 0, 303, 0, 0, 0, 434, 435, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 608, 609, 610, 611, 612, 858, 859, 0, 725, 0, 0, 0, 99, 100, 0, 0, 0, 0, 106, 0, 0, 0, 681, 0, 0, 0, 0, 0, 564, 565, 566, 0, 0, 0, 0, 667, 442, 443, 0, 0, 0, 0, 0, 0, 0, 696, 0, 0, 0, 0, 683, 544, 545, 546, 865, 866, 867, 868, 0, 716, 0, 0, 107, 108, 0, 0, 0, 0, 724, 0, 0, 0, 689, 0, 0, 0, 0, 710, 572, 573, 574, 0, 0, 0, 0, 0, 0, 705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 710, 872, 873, 866, 867, 876, 877, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 559, 0, 0, 0, 517, 518, 519, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 559, 0, 0, 0, 0, 0, 0, 0, 880, 873, 866, 867, 876, 0, 0, 871, 870, 0, 871, 870, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 716, 567, 0, 0, 0, 525, 526, 527, 734, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 567, 0, 0, 0, 0, 0, 725, 0, 880, 873, 866, 867, 876, 0, 0, 879, 878, 0, 879, 878, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 544, 638, 546, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 724, 0, 0, 0, 516, 880, 873, 866, 867, 876, 0, 0, 874, 875, 0, 874, 875, 0, 0, 705, 465, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 880, 873, 874, 875, 876, 885, 0, 18, 19, 0, 18, 19, 0, 0, 0, 473, 0, 0, 531, 532, 533, 534, 535, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 101, 102, 0, 0, 0, 0, 0, 0, 880, 881, 882, 883, 884, 416, 417, 418, 27, 0, 26, 27, 0, 0, 0, 481, 0, 0, 539, 540, 541, 542, 543, 0, 0, 516, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 733, 109, 110, 0, 0, 0, 0, 0, 0, 0, 889, 98, 0, 892, 893, 425, 426, 683, 0, 414, 0, 0, 339, 0, 0, 0, 0, 547, 548, 549, 550, 551, 0, 0, 0, 0, 0, 559, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 741, 0, 0, 724, 597, 598, 599, 0, 0, 0, 0, 106, 0, 0, 0, 701, 0, 0, 0, 0, 696, 0, 0, 0, 0, 0, 0, 734, 556, 557, 558, 0, 0, 0, 0, 0, 0, 567, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 605, 606, 607, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 700, 0, 0, 0, 0, 0, 564, 565, 566, 0, 0, 682, 0, 0, 0, 0, 0, 0, 699, 0, 0, 0, 0, 0, 0, 698, 0, 0, 0, 0, 0, 0, 613, 614, 615, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 667, 0, 572, 573, 574, 0, 700, 0, 0, 0, 0, 101, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 597, 598, 599, 621, 622, 623, 0, 0, 0, 734, 0, 0, 0, 0, 710, 0, 0, 0, 578, 579, 0, 0, 0, 0, 0, 0, 0, 0, 871, 870, 0, 871, 870, 0, 0, 0, 109, 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 605, 606, 607, 536, 630, 538, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 584, 585, 586, 587, 588, 0, 0, 0, 83, 0, 0, 0, 879, 878, 0, 879, 878, 0, 0, 0, 560, 0, 0, 0, 0, 0, 0, 0, 0, 725, 0, 0, 0, 0, 613, 614, 615, 544, 638, 546, 0, 0, 0, 98, 0, 0, 0, 0, 0, 0, 592, 593, 594, 595, 596, 0, 0, 0, 91, 0, 0, 0, 874, 875, 0, 874, 875, 0, 0, 0, 568, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 621, 622, 623, 0, 0, 0, 0, 0, 0, 106, 0, 0, 0, 0, 99, 0, 600, 601, 602, 603, 604, 0, 0, 0, 0, 0, 0, 0, 18, 19, 0, 18, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 536, 630, 538, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 0, 608, 609, 610, 611, 612, 0, 0, 57, 0, 57, 0, 0, 26, 27, 0, 512, 513, 514, 515, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 544, 638, 546, 0, 0, 0, 0, 0, 0, 0, 516, 0, 0, 0, 0, 0, 567, 564, 565, 566, 696, 0, 0, 0, 0, 0, 0, 414, 716, 0, 0, 520, 521, 522, 523, 701, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 705, 0, 0, 98, 0, 0, 0, 0, 0, 0, 531, 532, 533, 534, 535, 572, 573, 574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 528, 529, 530, 0, 0, 0, 0, 0, 0, 0, 524, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 106, 0, 0, 0, 0, 696, 0, 539, 540, 541, 542, 543, 0, 0, 0, 0, 0, 0, 0, 0, 531, 532, 533, 534, 535, 0, 536, 537, 538, 0, 0, 0, 0, 0, 0, 725, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 665, 666, 733, 0, 0, 0, 0, 547, 548, 549, 550, 551, 0, 0, 0, 0, 0, 0, 0, 0, 539, 540, 541, 542, 543, 725, 544, 545, 546, 99, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 704, 673, 674, 741, 0, 0, 0, 0, 682, 556, 557, 558, 716, 0, 0, 0, 0, 0, 0, 0, 0, 547, 548, 549, 550, 551, 0, 0, 0, 0, 107, 108, 0, 0, 0, 0, 0, 559, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 566, 0, 700, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 558, 0, 0, 0, 0, 0, 680, 0, 0, 0, 0, 0, 0, 567, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 704, 0, 0, 0, 0, 572, 573, 574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 566, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 99, 100, 0, 0, 0, 0, 99, 100, 0, 572, 573, 574, 0, 0, 716, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 701, 0, 0, 0, 0, 0, 0, 559, 107, 559, 0, 0, 0, 0, 107, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 567, 0, 567, 0, 0, 0, 516, 0, 0, 0, 0, 0, 0, 725, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 225, 8, 17, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 21, "y": 7}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 6}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 1}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 40, "y": 6}, {"id": 5, "name": "EV005", "note": "<Compass Icon: 3152>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door2", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 5, 4, 16, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 14, "y": 22}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other2", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 27, "y": 26}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 4]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 3, 2]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 46]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\I[2048]\\\\V[124]G!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\I[2048]\\\\V[124]G!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["D", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 16]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 0, 0]}, {"code": 128, "indent": 1, "parameters": [365, 0, 0, 1, false]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 1, 0]}, {"code": 128, "indent": 2, "parameters": [366, 0, 0, 1, false]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 2, 0]}, {"code": 128, "indent": 3, "parameters": [367, 0, 0, 1, false]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 3, 0]}, {"code": 128, "indent": 4, "parameters": [368, 0, 0, 1, false]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 120, 0, 4, 0]}, {"code": 128, "indent": 5, "parameters": [369, 0, 0, 1, false]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 5, 0]}, {"code": 128, "indent": 6, "parameters": [370, 0, 0, 1, false]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 6, 0]}, {"code": 128, "indent": 7, "parameters": [371, 0, 0, 1, false]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 7, 0]}, {"code": 128, "indent": 8, "parameters": [372, 0, 0, 1, false]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 8, 0]}, {"code": 128, "indent": 9, "parameters": [373, 0, 0, 1, false]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 9, 0]}, {"code": 128, "indent": 10, "parameters": [374, 0, 0, 1, false]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 10, 0]}, {"code": 128, "indent": 11, "parameters": [375, 0, 0, 1, false]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 11, 0]}, {"code": 128, "indent": 12, "parameters": [376, 0, 0, 1, false]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 111, "indent": 12, "parameters": [1, 120, 0, 12, 0]}, {"code": 128, "indent": 13, "parameters": [377, 0, 0, 1, false]}, {"code": 357, "indent": 13, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 13, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 13, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 13, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 13, "parameters": []}, {"code": 411, "indent": 12, "parameters": []}, {"code": 111, "indent": 13, "parameters": [1, 120, 0, 13, 0]}, {"code": 128, "indent": 14, "parameters": [378, 0, 0, 1, false]}, {"code": 357, "indent": 14, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 14, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 14, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 14, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 14, "parameters": []}, {"code": 411, "indent": 13, "parameters": []}, {"code": 111, "indent": 14, "parameters": [1, 120, 0, 14, 0]}, {"code": 128, "indent": 15, "parameters": [379, 0, 0, 1, false]}, {"code": 357, "indent": 15, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 15, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 15, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 15, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 15, "parameters": []}, {"code": 411, "indent": 14, "parameters": []}, {"code": 111, "indent": 15, "parameters": [1, 120, 0, 15, 0]}, {"code": 128, "indent": 16, "parameters": [380, 0, 0, 1, false]}, {"code": 357, "indent": 16, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 16, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 16, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 16, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 16, "parameters": []}, {"code": 411, "indent": 15, "parameters": []}, {"code": 111, "indent": 16, "parameters": [1, 120, 0, 16, 0]}, {"code": 128, "indent": 17, "parameters": [381, 0, 0, 1, false]}, {"code": 357, "indent": 17, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 17, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 17, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 17, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 17, "parameters": []}, {"code": 412, "indent": 16, "parameters": []}, {"code": 0, "indent": 16, "parameters": []}, {"code": 412, "indent": 15, "parameters": []}, {"code": 0, "indent": 15, "parameters": []}, {"code": 412, "indent": 14, "parameters": []}, {"code": 0, "indent": 14, "parameters": []}, {"code": 412, "indent": 13, "parameters": []}, {"code": 0, "indent": 13, "parameters": []}, {"code": 412, "indent": 12, "parameters": []}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 6, "characterName": "!Chest", "direction": 8, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 8, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 4, "y": 23}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 119, 0, 60, 1]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_0_CoreEngine", "AudioChangeBgmVolume", "Audio: Change Current BGM Volume", {"volume:eval": "0"}]}, {"code": 657, "indent": 1, "parameters": ["Volume = 0"]}, {"code": 357, "indent": 1, "parameters": ["Wave 1/VisuMZ_0_CoreEngine", "AudioChangeBgsVolume", "Audio: Change Current BGS Volume", {"volume:eval": "90"}]}, {"code": 657, "indent": 1, "parameters": ["Volume = 90"]}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 250, "indent": 2, "parameters": [{"name": "Paralyze1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 2, "parameters": ["FilterControllerMZ", "createFilter", "createFilter", {"filterId": "Glitch", "filterType": "glitch", "filterTarget": "FullScreen", "targetIds": "[]", "positionReferenceTargetId": ""}]}, {"code": 657, "indent": 2, "parameters": ["filterId = Glitch"]}, {"code": 657, "indent": 2, "parameters": ["filterType = glitch"]}, {"code": 657, "indent": 2, "parameters": ["filterTarget = FullScreen"]}, {"code": 657, "indent": 2, "parameters": ["targetIds = []"]}, {"code": 657, "indent": 2, "parameters": ["Position Reference Target Id = "]}, {"code": 357, "indent": 2, "parameters": ["FilterControllerMZ", "enableFilter", "enableFilter", {"filterId": "Glitch", "activeness": "true"}]}, {"code": 657, "indent": 2, "parameters": ["filterId = Glitch"]}, {"code": 657, "indent": 2, "parameters": ["activeness = true"]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 357, "indent": 2, "parameters": ["FilterControllerMZ", "eraseFilter", "eraseFilter", {"filterId": "Glitch"}]}, {"code": 657, "indent": 2, "parameters": ["filterId = Glitch"]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 119, 0, 40, 1]}, {"code": 357, "indent": 2, "parameters": ["Wave 1/VisuMZ_0_CoreEngine", "AudioChangeBgmVolume", "Audio: Change Current BGM Volume", {"volume:eval": "30"}]}, {"code": 657, "indent": 2, "parameters": ["Volume = 30"]}, {"code": 357, "indent": 2, "parameters": ["Wave 1/VisuMZ_0_CoreEngine", "AudioChangeBgsVolume", "Audio: Change Current BGS Volume", {"volume:eval": "70"}]}, {"code": 657, "indent": 2, "parameters": ["Volume = 70"]}, {"code": 357, "indent": 2, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Dark Grey", "Duration:num": "0"}]}, {"code": 657, "indent": 2, "parameters": ["Color = Dark Grey"]}, {"code": 657, "indent": 2, "parameters": ["Duration = 0"]}, {"code": 111, "indent": 2, "parameters": [2, "B", 1]}, {"code": 250, "indent": 3, "parameters": [{"name": "Paralyze1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 3, "parameters": ["FilterControllerMZ", "createFilter", "createFilter", {"filterId": "Glitch", "filterType": "glitch", "filterTarget": "FullScreen", "targetIds": "[]", "positionReferenceTargetId": ""}]}, {"code": 657, "indent": 3, "parameters": ["filterId = Glitch"]}, {"code": 657, "indent": 3, "parameters": ["filterType = glitch"]}, {"code": 657, "indent": 3, "parameters": ["filterTarget = FullScreen"]}, {"code": 657, "indent": 3, "parameters": ["targetIds = []"]}, {"code": 657, "indent": 3, "parameters": ["Position Reference Target Id = "]}, {"code": 357, "indent": 3, "parameters": ["FilterControllerMZ", "enableFilter", "enableFilter", {"filterId": "Glitch", "activeness": "true"}]}, {"code": 657, "indent": 3, "parameters": ["filterId = Glitch"]}, {"code": 657, "indent": 3, "parameters": ["activeness = true"]}, {"code": 230, "indent": 3, "parameters": [10]}, {"code": 357, "indent": 3, "parameters": ["FilterControllerMZ", "eraseFilter", "eraseFilter", {"filterId": "Glitch"}]}, {"code": 657, "indent": 3, "parameters": ["filterId = Glitch"]}, {"code": 123, "indent": 3, "parameters": ["B", 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 119, 0, 20, 1]}, {"code": 357, "indent": 3, "parameters": ["Wave 1/VisuMZ_0_CoreEngine", "AudioChangeBgmVolume", "Audio: Change Current BGM Volume", {"volume:eval": "50"}]}, {"code": 657, "indent": 3, "parameters": ["Volume = 50"]}, {"code": 357, "indent": 3, "parameters": ["Wave 1/VisuMZ_0_CoreEngine", "AudioChangeBgsVolume", "Audio: Change Current BGS Volume", {"volume:eval": "50"}]}, {"code": 657, "indent": 3, "parameters": ["Volume = 50"]}, {"code": 357, "indent": 3, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Grey", "Duration:num": "0"}]}, {"code": 657, "indent": 3, "parameters": ["Color = Grey"]}, {"code": 657, "indent": 3, "parameters": ["Duration = 0"]}, {"code": 111, "indent": 3, "parameters": [2, "C", 1]}, {"code": 250, "indent": 4, "parameters": [{"name": "Paralyze1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 4, "parameters": ["FilterControllerMZ", "createFilter", "createFilter", {"filterId": "Glitch", "filterType": "glitch", "filterTarget": "FullScreen", "targetIds": "[]", "positionReferenceTargetId": ""}]}, {"code": 657, "indent": 4, "parameters": ["filterId = Glitch"]}, {"code": 657, "indent": 4, "parameters": ["filterType = glitch"]}, {"code": 657, "indent": 4, "parameters": ["filterTarget = FullScreen"]}, {"code": 657, "indent": 4, "parameters": ["targetIds = []"]}, {"code": 657, "indent": 4, "parameters": ["Position Reference Target Id = "]}, {"code": 357, "indent": 4, "parameters": ["FilterControllerMZ", "enableFilter", "enableFilter", {"filterId": "Glitch", "activeness": "true"}]}, {"code": 657, "indent": 4, "parameters": ["filterId = Glitch"]}, {"code": 657, "indent": 4, "parameters": ["activeness = true"]}, {"code": 230, "indent": 4, "parameters": [10]}, {"code": 357, "indent": 4, "parameters": ["FilterControllerMZ", "eraseFilter", "eraseFilter", {"filterId": "Glitch"}]}, {"code": 657, "indent": 4, "parameters": ["filterId = Glitch"]}, {"code": 123, "indent": 4, "parameters": ["C", 0]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 0, "y": 1}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door2", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 226, 8, 18, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 7, "y": 11}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 4]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 3, 2]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 46]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\I[2048]\\\\V[124]G!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\I[2048]\\\\V[124]G!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 210>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["D", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 16]}, {"code": 111, "indent": 0, "parameters": [1, 120, 0, 0, 0]}, {"code": 128, "indent": 1, "parameters": [365, 0, 0, 1, false]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 120, 0, 1, 0]}, {"code": 128, "indent": 2, "parameters": [366, 0, 0, 1, false]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 120, 0, 2, 0]}, {"code": 128, "indent": 3, "parameters": [367, 0, 0, 1, false]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 120, 0, 3, 0]}, {"code": 128, "indent": 4, "parameters": [368, 0, 0, 1, false]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 120, 0, 4, 0]}, {"code": 128, "indent": 5, "parameters": [369, 0, 0, 1, false]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 5, 0]}, {"code": 128, "indent": 6, "parameters": [370, 0, 0, 1, false]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 6, 0]}, {"code": 128, "indent": 7, "parameters": [371, 0, 0, 1, false]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 7, 0]}, {"code": 128, "indent": 8, "parameters": [372, 0, 0, 1, false]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 8, 0]}, {"code": 128, "indent": 9, "parameters": [373, 0, 0, 1, false]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 9, 0]}, {"code": 128, "indent": 10, "parameters": [374, 0, 0, 1, false]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 10, 0]}, {"code": 128, "indent": 11, "parameters": [375, 0, 0, 1, false]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 11, 0]}, {"code": 128, "indent": 12, "parameters": [376, 0, 0, 1, false]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 111, "indent": 12, "parameters": [1, 120, 0, 12, 0]}, {"code": 128, "indent": 13, "parameters": [377, 0, 0, 1, false]}, {"code": 357, "indent": 13, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 13, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 13, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 13, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 13, "parameters": []}, {"code": 411, "indent": 12, "parameters": []}, {"code": 111, "indent": 13, "parameters": [1, 120, 0, 13, 0]}, {"code": 128, "indent": 14, "parameters": [378, 0, 0, 1, false]}, {"code": 357, "indent": 14, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 14, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 14, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 14, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 14, "parameters": []}, {"code": 411, "indent": 13, "parameters": []}, {"code": 111, "indent": 14, "parameters": [1, 120, 0, 14, 0]}, {"code": 128, "indent": 15, "parameters": [379, 0, 0, 1, false]}, {"code": 357, "indent": 15, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 15, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 15, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 15, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 15, "parameters": []}, {"code": 411, "indent": 14, "parameters": []}, {"code": 111, "indent": 15, "parameters": [1, 120, 0, 15, 0]}, {"code": 128, "indent": 16, "parameters": [380, 0, 0, 1, false]}, {"code": 357, "indent": 16, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 16, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 16, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 16, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 16, "parameters": []}, {"code": 411, "indent": 15, "parameters": []}, {"code": 111, "indent": 16, "parameters": [1, 120, 0, 16, 0]}, {"code": 128, "indent": 17, "parameters": [381, 0, 0, 1, false]}, {"code": 357, "indent": 17, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 17, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 17, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 17, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 17, "parameters": []}, {"code": 412, "indent": 16, "parameters": []}, {"code": 0, "indent": 16, "parameters": []}, {"code": 412, "indent": 15, "parameters": []}, {"code": 0, "indent": 15, "parameters": []}, {"code": 412, "indent": 14, "parameters": []}, {"code": 0, "indent": 14, "parameters": []}, {"code": 412, "indent": 13, "parameters": []}, {"code": 0, "indent": 13, "parameters": []}, {"code": 412, "indent": 12, "parameters": []}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 6, "characterName": "!Chest", "direction": 8, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Chest", "direction": 8, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Compass Icon: 0>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 47, "y": 13}, {"id": 11, "name": "Fisherman", "note": "<Sprite Offset: -3, +7><Breathing Rate: 1%>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "NPC-Old Man1", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 357, "indent": 0, "parameters": ["CreepyDialogue", "enableCreepyDialogue", "Enable Creepy Dialogue", {}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["<PERSON>! We're so glad that you're finally back. I hope you"]}, {"code": 401, "indent": 0, "parameters": ["caught a lot of fish on your big adventure!"]}, {"code": 357, "indent": 0, "parameters": ["CreepyDialogue", "disableCreepyDialogue", "Disable Creepy Dialogue", {}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 36, "y": 20}, {"id": 12, "name": "EV012", "note": "<Breathing Rate: 1%>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Nature", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["<Ambience SFX: Cat>"]}, {"code": 408, "indent": 0, "parameters": ["<Ambience Interval: 1000>"]}, {"code": 408, "indent": 0, "parameters": ["<Ambience Distance: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<Ambience Volume: 75>"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 1, 0, 2, "Smores"]}, {"code": 401, "indent": 0, "parameters": ["Mew?"]}, {"code": 111, "indent": 0, "parameters": [8, 60]}, {"code": 101, "indent": 1, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 1, "parameters": ["Hey <PERSON><PERSON><PERSON>, you want a treat buddy?"]}, {"code": 250, "indent": 1, "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["Nature", 1, 0, 2, "Smores"]}, {"code": 401, "indent": 1, "parameters": ["Mrow!"]}, {"code": 101, "indent": 1, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 1, "parameters": ["Here you go!"]}, {"code": 101, "indent": 1, "parameters": ["", 1, 0, 2, ""]}, {"code": 401, "indent": 1, "parameters": ["You gave S<PERSON><PERSON> a \\i[2416]Cat Treat!"]}, {"code": 126, "indent": 1, "parameters": [60, 1, 0, 1]}, {"code": 213, "indent": 1, "parameters": [0, 4, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "CatShortPurrSound", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 1, 0, 2, ""]}, {"code": 401, "indent": 1, "parameters": ["<PERSON><PERSON><PERSON> loves you!"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 1, "parameters": ["Sorry <PERSON><PERSON><PERSON>, I don't have any treats today."]}, {"code": 213, "indent": 1, "parameters": [0, 5, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Cat", "volume": 30, "pitch": 80, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 20}, {"id": 13, "name": "EV013", "note": "<shadow><Breathing Rate: 1%>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "WARF_0015_ls-blue[VS8]", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["war f bust blue sml", 0, 0, 2, "Soldier"]}, {"code": 401, "indent": 0, "parameters": ["<PERSON>! It's so good to see you again! We"]}, {"code": 401, "indent": 0, "parameters": ["thought you may have perished on your journey."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 35}, {"id": 14, "name": "EV014", "note": "<Compass Icon: 3169>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door2", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 6, 4, 9, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 27, "y": 30}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Statue", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["A statue of the light champion, <PERSON><PERSON><PERSON>, who slew the dark"]}, {"code": 401, "indent": 0, "parameters": ["queen and drove back the elves."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 31}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "butterflys_dragonflies", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Hide Shadow>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 1, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 0, "walkAnime": true}], "x": 27, "y": 23}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "butterflys_dragonflies", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Hide Shadow>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 1, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 0, "walkAnime": true}], "x": 20, "y": 11}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door2", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 227, 8, 18, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 41, "y": 11}, {"id": 19, "name": "EV019", "note": "<Compass Icon: 3152>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Door02a", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 5, 13, 11, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 20, "y": 20}, {"id": 20, "name": "EV020", "note": "<Compass Icon: 3169>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 6, 13, 7, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 33, "y": 29}, {"id": 21, "name": "EV021", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Day", "Duration:num": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Color = Day"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\"]", "UpperLower:str": "both", "Duration:eval": "0", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Slow_Icons_Right", "SLOW: Flying Icons →", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"1\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"1200\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"left border\\\",\\\"spawnOffsetX:eval\\\":\\\"-100\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"255\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"InQuart\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"1.5\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"1\\\",\\\"totalPerPower:num\\\":\\\"1\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#000000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"0\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"false\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[\\\\\\\"pixelcloud1s\\\\\\\",\\\\\\\"pixelcloud2s\\\\\\\",\\\\\\\"pixelcloud3s\\\\\\\",\\\\\\\"pixelcloud4s\\\\\\\"]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"3\\\",\\\"speedVariance:eval\\\":\\\"0\\\",\\\"angle:eval\\\":\\\"0\\\",\\\"alignAngle:eval\\\":\\\"true\\\",\\\"angleVariance:eval\\\":\\\"0\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"true\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"1\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120…"]}, {"code": 111, "indent": 0, "parameters": [1, 119, 0, 40, 1]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_WeatherEffects", "Earth_ToxicGas", "EARTH: Toxic Gas", {"MainData": "", "powerTarget:eval": "9", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"2\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"1200\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"right 60%\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"30\\\",\\\"opacityVariance:num\\\":\\\"29\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"60\\\",\\\"scale:num\\\":\\\"0.1\\\",\\\"scaleVariance:num\\\":\\\"0.09\\\",\\\"scaleRatioX:num\\\":\\\"12.0\\\",\\\"scaleRatioY:num\\\":\\\"3.0\\\",\\\"totalMinimum:num\\\":\\\"20\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#000000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"0\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"16\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"16\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[\\\\\\\"160\\\\\\\",\\\\\\\"170\\\\\\\",\\\\\\\"180\\\\\\\",\\\\\\\"190\\\\\\\",\\\\\\\"200\\\\\\\"]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"true\\\",\\\"angleVariance:eval\\\":\\\"0\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0.25\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Power = 9"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 1, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 1, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Layer(s) = [\"2\"]"]}, {"code": 657, "indent": 1, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 1, "parameters": ["Customization = "]}, {"code": 657, "indent": 1, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_WeatherEffects", "Fire_SunBeams", "FIRE: Sunlight Beams", {"MainData": "", "powerTarget:eval": "4", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"2\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"240\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"left 50%\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"upper 10%\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"24\\\",\\\"opacityVariance:num\\\":\\\"16\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"60\\\",\\\"scale:num\\\":\\\"1.5\\\",\\\"scaleVariance:num\\\":\\\"1.4\\\",\\\"scaleRatioX:num\\\":\\\"1\\\",\\\"scaleRatioY:num\\\":\\\"0.02\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"10\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#ffffff\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"6\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"1\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"flatFlutter:eval\\\":\\\"false\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\",\\\"respawnCommonEventID:num\\\":\\\"0\\\",\\\"respawnDelayMin:eval\\\":\\\"0\\\",\\\"respawnDelayRngPerPower:eval\\\":\\\"0\\\",\\\"sparkleFinish:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"frozen\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"0\\\",\\\"speedVariance:eval\\\":\\\"0\\\",\\\"angle:eval\\\":\\\"300\\\",\\\"alignAngle:eval\\\":\\\"true\\\",\\\"angleVariance:eval\\\":\\\"0\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Power = 4"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 1, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 1, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Layer(s) = [\"2\"]"]}, {"code": 657, "indent": 1, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 1, "parameters": ["Customization = "]}, {"code": 657, "indent": 1, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"240…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 119, 0, 20, 1]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_Sootfall", "DARK: <PERSON><PERSON><PERSON>", {"MainData": "", "powerTarget:eval": "2", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"160\\\",\\\"opacityVariance:num\\\":\\\"20\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"1.0\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"10\\\",\\\"totalPerPower:num\\\":\\\"10\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#000000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"4\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"16\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"16\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"0.5\\\",\\\"speedVariance:eval\\\":\\\"0\\\",\\\"angle:eval\\\":\\\"220\\\",\\\"alignAngle:eval\\\":\\\"true\\\",\\\"angleVariance:eval\\\":\\\"15\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"2\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Power = 2"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 1, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 1, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 1, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 1, "parameters": ["Customization = "]}, {"code": 657, "indent": 1, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_WeatherEffects", "Wind_AutumnLeaves", "WIND: Autumn Leaves", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"1200\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"upper border\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"255\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"InQuart\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"0.20\\\",\\\"scaleVariance:num\\\":\\\"0.19\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#000000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"0\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"flatFlutter:eval\\\":\\\"true\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\",\\\"respawnCommonEventID:num\\\":\\\"0\\\",\\\"respawnDelayMin:eval\\\":\\\"0\\\",\\\"respawnDelayRngPerPower:eval\\\":\\\"+0\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"speed:eval\\\":\\\"2\\\",\\\"speedVariance:eval\\\":\\\"0\\\",\\\"angle:eval\\\":\\\"315\\\",\\\"alignAngle:eval\\\":\\\"true\\\",\\\"angleVariance:eval\\\":\\\"15\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+3\\\",\\\"spinSpeedVariance:eval\\\":\\\"1\\\",\\\"reverseSpin:eval\\\":\\\"true\\\",\\\"xSwayRange:eval\\\":\\\"2\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Power = 1"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 1, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 1, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 1, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 1, "parameters": ["Customization = "]}, {"code": 657, "indent": 1, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 65, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\"]", "UpperLower:str": "both", "Duration:eval": "0", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 241, "indent": 0, "parameters": [{"name": "DarkFantasyForestLoop2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "Forest_Fire", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Orange", "Duration:num": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Color = Orange"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Fire_Embers", "FIRE: Embers", {"MainData": "", "powerTarget:eval": "2", "duration:eval": "0", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"1\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"130\\\",\\\"opacityVariance:num\\\":\\\"30\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"2.0\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"30\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#ff8822\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"16\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"16\\\",\\\"blendMode:num\\\":\\\"3\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"2\\\",\\\"speedVariance:eval\\\":\\\"1\\\",\\\"angle:eval\\\":\\\"90\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"10\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"4\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 2"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"1\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Fire_FlameHaze", "FIRE: <PERSON>", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"2\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"16\\\",\\\"opacityVariance:num\\\":\\\"12\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"80\\\",\\\"scale:num\\\":\\\"2.0\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#f26c4f\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"3\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1.2\\\",\\\"speedVariance:eval\\\":\\\"0.3\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 5"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"2\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Fire_HeatClouds", "FIRE: Heat Clouds", {"MainData": "", "powerTarget:eval": "2", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"3\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"40\\\",\\\"opacityVariance:num\\\":\\\"20\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"80\\\",\\\"scale:num\\\":\\\"2\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#ff8822\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"3\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"90\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 2"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"3\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Fire_FlameWall", "FIRE: Flame Wall", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"4\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"90\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"64\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"0.75\\\",\\\"scaleVariance:num\\\":\\\"0.25\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.5\\\",\\\"totalMinimum:num\\\":\\\"10\\\",\\\"totalPerPower:num\\\":\\\"10\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#ff8822\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"32\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"32\\\",\\\"blendMode:num\\\":\\\"3\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"90\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"10\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"1\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"4\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"90\\…"]}, {"code": 357, "indent": 0, "parameters": ["FilterControllerMZ", "createFilter", "createFilter", {"filterId": "Heat waves", "filterType": "reflection-w", "filterTarget": "FullScreen", "targetIds": "[]", "positionReferenceTargetId": ""}]}, {"code": 657, "indent": 0, "parameters": ["filterId = Heat waves"]}, {"code": 657, "indent": 0, "parameters": ["filterType = reflection-w"]}, {"code": 657, "indent": 0, "parameters": ["filterTarget = FullScreen"]}, {"code": 657, "indent": 0, "parameters": ["targetIds = []"]}, {"code": 657, "indent": 0, "parameters": ["Position Reference Target Id = "]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "LightSpawnNewPlayerLockedLight", "SPAWN LIGHT: Create Light(s) on Player", {"LightSettings": "", "Settings:struct": "{\"General\":\"\",\"enabled:eval\":\"true\",\"Properties\":\"\",\"filename:str\":\"\",\"color:str\":\"#ffffff\",\"radius:num\":\"64\",\"intensity:num\":\"0.50\",\"Optional\":\"\",\"angle:num\":\"0\",\"rotateSpeed:num\":\"+0\",\"blendMode:num\":\"3\",\"opacity:num\":\"255\",\"Offsets\":\"\",\"offsetX:num\":\"+0\",\"offsetY:num\":\"+0\"}", "Behavior:struct": "{\"Blink\":\"\",\"blinkRate:num\":\"0.00\",\"blinkModifier:num\":\"-0.50\",\"Flicker\":\"\",\"flickerRate:num\":\"0.00\",\"flickerModifier:num\":\"-0.50\",\"Flash\":\"\",\"flashRate:num\":\"0.00\",\"flashModifier:num\":\"+0.50\",\"Flare\":\"\",\"flareRate:num\":\"0.00\",\"flareModifier:num\":\"+0.50\",\"Glow\":\"\",\"glowRate:num\":\"0.00\",\"glowSpeed:num\":\"0.10\",\"glowRng:eval\":\"true\",\"Pulse\":\"\",\"pulseRate:num\":\"0.00\",\"pulseSpeed:num\":\"0.10\",\"pulseRng:eval\":\"true\",\"Pattern\":\"\",\"patternName:str\":\"none\",\"pattern:str\":\"\",\"patternDelay:num\":\"6\"}", "SpawnSettings": "", "UpdateFunc:json": "\"// Declare Constants\\nconst data = arguments[0];\\nconst time = arguments[1];\\n\\n// Calculate Results\\nconst angle = time * 1.0;\\nconst radians = angle * Math.PI / 180.0;\\nconst distance = 0;  // Distance from Center\\nconst offsetX = 0;\\nconst offsetY = 0;\\nconst x = Math.cos(radians) * distance + offsetX;\\nconst y = Math.sin(radians) * distance + offsetY;\\n\\n// Return Results\\nreturn {\\n    x: x,\\n    y: y,\\n};\"", "InitialTime:eval": "0", "TotalSpawns:eval": "1", "TimeIncrement:eval": "+1", "ExpirationTimer:eval": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Light Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Settings = {\"General\":\"\",\"enabled:eval\":\"true\",\"Properties\"…"]}, {"code": 657, "indent": 0, "parameters": ["Behavior = {\"Blink\":\"\",\"blinkRate:num\":\"0.00\",\"blinkModifie…"]}, {"code": 657, "indent": 0, "parameters": ["Spawn Settings = "]}, {"code": 657, "indent": 0, "parameters": ["JS: Trajectory = \"// Declare Constants\\nconst data = argume…"]}, {"code": 657, "indent": 0, "parameters": ["Initial Time = 0"]}, {"code": 657, "indent": 0, "parameters": ["Total Spawns = 1"]}, {"code": 657, "indent": 0, "parameters": ["Time Increment = +1"]}, {"code": 657, "indent": 0, "parameters": ["Expiration Timer = 0"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Dark Grey", "Duration:num": "999"}]}, {"code": 657, "indent": 1, "parameters": ["Color = Dark Grey"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 999"]}, {"code": 230, "indent": 1, "parameters": [999]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_WeatherEffects", "Fire_Embers", "FIRE: Embers", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"1\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"130\\\",\\\"opacityVariance:num\\\":\\\"30\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"2.0\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"30\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#ff8822\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"16\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"16\\\",\\\"blendMode:num\\\":\\\"3\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"2\\\",\\\"speedVariance:eval\\\":\\\"1\\\",\\\"angle:eval\\\":\\\"90\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"10\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"4\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Power = 1"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 1, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 1, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Layer(s) = [\"1\"]"]}, {"code": 657, "indent": 1, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 1, "parameters": ["Customization = "]}, {"code": 657, "indent": 1, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120…"]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_Ashfall", "DARK: Ashfall", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"5\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"150\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"180\\\",\\\"opacityVariance:num\\\":\\\"40\\\",\\\"scale:num\\\":\\\"1.0\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"totalMinimum:num\\\":\\\"20\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#000000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"flatFlutter:eval\\\":\\\"true\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\",\\\"respawnCommonEventID:num\\\":\\\"0\\\",\\\"respawnDelayMin:eval\\\":\\\"0\\\",\\\"respawnDelayRngPerPower:eval\\\":\\\"+0\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"speed:eval\\\":\\\"2\\\",\\\"speedVariance:eval\\\":\\\"0\\\",\\\"angle:eval\\\":\\\"215\\\",\\\"alignAngle:eval\\\":\\\"true\\\",\\\"angleVariance:eval\\\":\\\"15\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"2\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Power = 5"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 1, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 1, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Layer(s) = [\"5\"]"]}, {"code": 657, "indent": 1, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 1, "parameters": ["Customization = "]}, {"code": 657, "indent": 1, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"150…"]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_SmokeClouds", "DARK: Smoke Clouds", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"6\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"false\\\",\\\"opacity:num\\\":\\\"200\\\",\\\"opacityVariance:num\\\":\\\"24\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"80\\\",\\\"scale:num\\\":\\\"1.0\\\",\\\"scaleVariance:num\\\":\\\"0.20\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"0.6\\\",\\\"totalMinimum:num\\\":\\\"0\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#00e1e1\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"90\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"2\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Power = 5"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 1, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 1, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Layer(s) = [\"6\"]"]}, {"code": 657, "indent": 1, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 1, "parameters": ["Customization = "]}, {"code": 657, "indent": 1, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"800…"]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_WeatherEffects", "Fire_FlameWall", "FIRE: Flame Wall", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"4\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"90\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"64\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"0.75\\\",\\\"scaleVariance:num\\\":\\\"0.25\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.5\\\",\\\"totalMinimum:num\\\":\\\"10\\\",\\\"totalPerPower:num\\\":\\\"10\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#ff8822\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"32\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"32\\\",\\\"blendMode:num\\\":\\\"3\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"90\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"10\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"1\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Power = 1"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 1, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 1, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Layer(s) = [\"4\"]"]}, {"code": 657, "indent": 1, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 1, "parameters": ["Customization = "]}, {"code": 657, "indent": 1, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"90\\…"]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Orange", "Duration:num": "999"}]}, {"code": 657, "indent": 1, "parameters": ["Color = Orange"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 999"]}, {"code": 230, "indent": 1, "parameters": [999]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_WeatherEffects", "BasicClearWeather", "BASIC: Clear Weather", {"Layer:arrayeval": "[\"6\"]", "UpperLower:str": "both", "Duration:eval": "120", "WaitForCompletion:eval": "false"}]}, {"code": 657, "indent": 1, "parameters": ["Layer(s) = [\"6\"]"]}, {"code": 657, "indent": 1, "parameters": ["Upper/Lower? = both"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 120"]}, {"code": 657, "indent": 1, "parameters": ["Wait For Completion? = false"]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_WeatherEffects", "Fire_Embers", "FIRE: Embers", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"1\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"130\\\",\\\"opacityVariance:num\\\":\\\"30\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"2.0\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"30\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#ff8822\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"16\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"16\\\",\\\"blendMode:num\\\":\\\"3\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"2\\\",\\\"speedVariance:eval\\\":\\\"1\\\",\\\"angle:eval\\\":\\\"90\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"10\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"4\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Power = 1"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 1, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 1, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Layer(s) = [\"1\"]"]}, {"code": 657, "indent": 1, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 1, "parameters": ["Customization = "]}, {"code": 657, "indent": 1, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"120…"]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_WeatherEffects", "Fire_FlameWall", "FIRE: Flame Wall", {"MainData": "", "powerTarget:eval": "9", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"4\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"90\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"64\\\",\\\"opacityVariance:num\\\":\\\"0\\\",\\\"opacityEasingType:str\\\":\\\"Linear\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"0.75\\\",\\\"scaleVariance:num\\\":\\\"0.25\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.5\\\",\\\"totalMinimum:num\\\":\\\"10\\\",\\\"totalPerPower:num\\\":\\\"10\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#ff8822\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"32\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"32\\\",\\\"blendMode:num\\\":\\\"3\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"1\\\",\\\"speedVariance:eval\\\":\\\"0.5\\\",\\\"angle:eval\\\":\\\"90\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"10\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"1\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Power = 9"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 1, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 1, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Layer(s) = [\"4\"]"]}, {"code": 657, "indent": 1, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 1, "parameters": ["Customization = "]}, {"code": 657, "indent": 1, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"90\\…"]}, {"code": 357, "indent": 1, "parameters": ["VisuMZ_2_WeatherEffects", "Dark_Ashfall", "DARK: Ashfall", {"MainData": "", "powerTarget:eval": "5", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"5\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"150\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"random\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"180\\\",\\\"opacityVariance:num\\\":\\\"40\\\",\\\"scale:num\\\":\\\"1.0\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"totalMinimum:num\\\":\\\"20\\\",\\\"totalPerPower:num\\\":\\\"20\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#000000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"2\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"true\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\",\\\"flatFlutter:eval\\\":\\\"true\\\",\\\"hueSwayRange:eval\\\":\\\"0\\\",\\\"hueSwaySpeed:eval\\\":\\\"0.01\\\",\\\"respawnCommonEventID:num\\\":\\\"0\\\",\\\"respawnDelayMin:eval\\\":\\\"0\\\",\\\"respawnDelayRngPerPower:eval\\\":\\\"+0\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"5\\\",\\\"speedVariance:eval\\\":\\\"0\\\",\\\"angle:eval\\\":\\\"90\\\",\\\"alignAngle:eval\\\":\\\"true\\\",\\\"angleVariance:eval\\\":\\\"15\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"false\\\",\\\"xSwayRange:eval\\\":\\\"2\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"0\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Power = 5"]}, {"code": 657, "indent": 1, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 1, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 1, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 1, "parameters": ["Layer(s) = [\"5\"]"]}, {"code": 657, "indent": 1, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 1, "parameters": ["Customization = "]}, {"code": 657, "indent": 1, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"150…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 0, "y": 0}, {"id": 22, "name": "EV022", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 119, "variableValid": true, "variableValue": 40}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapCameraZoom", "ZoomChange", "Zoom: Change Zoom", {"TargetScale:num": "1.01", "Duration:num": "360", "EasingType:str": "InOutSine"}]}, {"code": 657, "indent": 0, "parameters": ["Target Zoom Scale = 1.01"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 360"]}, {"code": 657, "indent": 0, "parameters": ["Easing Type = InOutSine"]}, {"code": 230, "indent": 0, "parameters": [360]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_4_MapCameraZoom", "ZoomChange", "Zoom: Change Zoom", {"TargetScale:num": "1.0", "Duration:num": "360", "EasingType:str": "InOutSine"}]}, {"code": 657, "indent": 0, "parameters": ["Target Zoom Scale = 1.0"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 360"]}, {"code": 657, "indent": 0, "parameters": ["Easing Type = InOutSine"]}, {"code": 230, "indent": 0, "parameters": [360]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 0, "y": 2}, {"id": 23, "name": "EV023", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Ambience SFX: Small%20Waterfall%20Loop%201>"]}, {"code": 408, "indent": 0, "parameters": ["<Ambience Interval: 50>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 31, "y": 14}, {"id": 24, "name": "EV024", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 466, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["But he was..\\! just\\! here."]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 466, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["What's happening??"]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 43, "y": 13}, {"id": 25, "name": "EV025", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 324, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 80]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\I[2048]\\\\V[124]G!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\I[2048]\\\\V[124]G!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Empty!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Empty!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 373, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 12, "y": 22}, {"id": 26, "name": "EV026", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 324, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 80]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\I[2048]\\\\V[124]G!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\I[2048]\\\\V[124]G!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Empty!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Empty!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 373, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 15, "y": 22}, {"id": 27, "name": "EV027", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 324, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 80]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\I[2048]\\\\V[124]G!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\I[2048]\\\\V[124]G!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Empty!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Empty!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 373, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 11, "y": 18}, {"id": 28, "name": "EV028", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 324, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 80]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\I[2048]\\\\V[124]G!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\I[2048]\\\\V[124]G!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Empty!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Empty!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 373, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 11, "y": 20}, {"id": 29, "name": "EV029", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 324, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 80]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\I[2048]\\\\V[124]G!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\I[2048]\\\\V[124]G!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Empty!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Empty!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 373, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 37, "y": 26}, {"id": 30, "name": "EV030", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 324, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Chest2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 36}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [120, 120, 0, 2, 0, 80]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(120) >= 0 && $gameVariables.value(120) <= 9"]}, {"code": 122, "indent": 1, "parameters": [124, 124, 0, 2, 100, 300]}, {"code": 125, "indent": 1, "parameters": [0, 1, 124]}, {"code": 357, "indent": 1, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\I[2048]\\\\V[124]G!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 1, "parameters": ["Text = \"<center>Found \\\\I[2048]\\\\V[124]G!\""]}, {"code": 657, "indent": 1, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 1, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameVariables.value(120) >= 10 && $gameVariables.value(120) <= 19"]}, {"code": 126, "indent": 2, "parameters": [7, 0, 0, 1]}, {"code": 357, "indent": 2, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 2, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 2, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 2, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameVariables.value(120) >= 20 && $gameVariables.value(120) <= 29"]}, {"code": 126, "indent": 3, "parameters": [10, 0, 0, 1]}, {"code": 357, "indent": 3, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 3, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 3, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 3, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameVariables.value(120) >= 30 && $gameVariables.value(120) <= 34"]}, {"code": 126, "indent": 4, "parameters": [11, 0, 0, 1]}, {"code": 357, "indent": 4, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 4, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 4, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 4, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(120) >= 35 && $gameVariables.value(120) <= 39"]}, {"code": 126, "indent": 5, "parameters": [12, 0, 0, 1]}, {"code": 357, "indent": 5, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 5, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 5, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 5, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 120, 0, 40, 0]}, {"code": 126, "indent": 6, "parameters": [23, 0, 0, 1]}, {"code": 357, "indent": 6, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 6, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 6, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 6, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 111, "indent": 6, "parameters": [1, 120, 0, 41, 0]}, {"code": 126, "indent": 7, "parameters": [25, 0, 0, 1]}, {"code": 357, "indent": 7, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 7, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 7, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 7, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 111, "indent": 7, "parameters": [1, 120, 0, 42, 0]}, {"code": 126, "indent": 8, "parameters": [26, 0, 0, 1]}, {"code": 357, "indent": 8, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 8, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 8, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 8, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 8, "parameters": []}, {"code": 411, "indent": 7, "parameters": []}, {"code": 111, "indent": 8, "parameters": [1, 120, 0, 43, 0]}, {"code": 126, "indent": 9, "parameters": [27, 0, 0, 1]}, {"code": 357, "indent": 9, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 9, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 9, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 9, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 9, "parameters": []}, {"code": 411, "indent": 8, "parameters": []}, {"code": 111, "indent": 9, "parameters": [1, 120, 0, 44, 0]}, {"code": 126, "indent": 10, "parameters": [28, 0, 0, 1]}, {"code": 357, "indent": 10, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 10, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 10, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 10, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 10, "parameters": []}, {"code": 411, "indent": 9, "parameters": []}, {"code": 111, "indent": 10, "parameters": [1, 120, 0, 45, 0]}, {"code": 126, "indent": 11, "parameters": [29, 0, 0, 1]}, {"code": 357, "indent": 11, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 11, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 11, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 11, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 11, "parameters": []}, {"code": 411, "indent": 10, "parameters": []}, {"code": 111, "indent": 11, "parameters": [1, 120, 0, 46, 0]}, {"code": 126, "indent": 12, "parameters": [30, 0, 0, 1]}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Found \\\\LastGainObj!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Found \\\\LastGainObj!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 411, "indent": 11, "parameters": []}, {"code": 357, "indent": 12, "parameters": ["Wave 2/VisuMZ_4_GabWindow", "GabTextOnly", "Gab: Text Only", {"Text:json": "\"<center>Empty!\"", "ForceGab:eval": "true", "Override:struct": "{\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimColor2:str\":\"\",\"Fade\":\"\",\"FadeRate:num\":\"\",\"FadeDirection:str\":\"\",\"Font\":\"\",\"FontName:str\":\"\",\"FontSize:num\":\"\",\"Position\":\"\",\"YLocation:num\":\"300\",\"ActorID:num\":\"0\",\"PartyIndex:num\":\"-1\",\"EnemyIndex:num\":\"-1\",\"EventID:num\":\"0\",\"OnDisplay\":\"\",\"BypassAntiRepeat:eval\":\"false\",\"SoundFilename:str\":\"\",\"OnDisplayJS:func\":\"\",\"OnFinish\":\"\",\"GabSwitch:num\":\"\",\"OnFinishJS:func\":\"\",\"Waiting\":\"\",\"WaitTime:num\":\"\",\"TimePerCharacter:num\":\"\"}"}]}, {"code": 657, "indent": 12, "parameters": ["Text = \"<center>Empty!\""]}, {"code": 657, "indent": 12, "parameters": ["Force Gab? = true"]}, {"code": 657, "indent": 12, "parameters": ["Optional Settings = {\"DimColor\":\"\",\"DimColor1:str\":\"\",\"DimC…"]}, {"code": 0, "indent": 12, "parameters": []}, {"code": 412, "indent": 11, "parameters": []}, {"code": 0, "indent": 11, "parameters": []}, {"code": 412, "indent": 10, "parameters": []}, {"code": 0, "indent": 10, "parameters": []}, {"code": 412, "indent": 9, "parameters": []}, {"code": 0, "indent": 9, "parameters": []}, {"code": 412, "indent": 8, "parameters": []}, {"code": 0, "indent": 8, "parameters": []}, {"code": 412, "indent": 7, "parameters": []}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 373, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 37, "y": 28}, null, {"id": 32, "name": "EV032", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "swimmingducks", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Hide Shadow>"]}, {"code": 108, "indent": 0, "parameters": ["<Disable Footprints>"]}, {"code": 108, "indent": 0, "parameters": ["<Ambience SFX: duck>"]}, {"code": 408, "indent": 0, "parameters": ["<Ambience SFX: duck-1>"]}, {"code": 408, "indent": 0, "parameters": ["<Ambience Interval: 500>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}], "x": 5, "y": 38}, {"id": 33, "name": "EV033", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "swimmingducks", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Hide Shadow>"]}, {"code": 108, "indent": 0, "parameters": ["<Disable Footprints>"]}, {"code": 108, "indent": 0, "parameters": ["<Ambience SFX: duck>"]}, {"code": 408, "indent": 0, "parameters": ["<Ambience SFX: duck-1>"]}, {"code": 408, "indent": 0, "parameters": ["<Ambience Interval: 500>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}], "x": 44, "y": 38}, {"id": 34, "name": "EV034", "note": "<shadow><Breathing Rate: 1%>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Blue Man1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["Hey <PERSON>!\\! How long have you been gone? Seems like it's"]}, {"code": 401, "indent": 0, "parameters": ["been years!"]}, {"code": 101, "indent": 0, "parameters": ["isaac-face", 0, 0, 2, "<PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Years??\\! Has it already been that long?"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 21}, {"id": 35, "name": "EV035", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 332, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 19}, {"id": 36, "name": "EV036", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 332, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 21}, {"id": 37, "name": "EV037", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 332, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 37, "y": 27}, {"id": 38, "name": "EV038", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 332, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 37, "y": 29}, {"id": 39, "name": "EV039", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 332, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 23}, {"id": 40, "name": "EV040", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 332, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 23}, {"id": 41, "name": "EV041", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 14}, {"id": 42, "name": "EV042", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 14}, {"id": 43, "name": "EV043", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 22}, {"id": 44, "name": "EV044", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 27}, {"id": 45, "name": "EV045", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 22}, {"id": 46, "name": "EV046", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 27}, {"id": 47, "name": "EV047", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 40, "y": 35}, {"id": 48, "name": "EV048", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 46, "y": 35}, {"id": 49, "name": "EV049", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 43, "y": 26}, {"id": 50, "name": "EV050", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 45, "y": 26}, {"id": 51, "name": "EV051", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 48, "y": 35}, {"id": 52, "name": "EV052", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 42, "y": 35}, {"id": 53, "name": "EV053", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 3}, {"id": 54, "name": "EV054", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 3}, {"id": 55, "name": "EV055", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 41, "y": 26}, {"id": 56, "name": "EV056", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 47, "y": 26}, null, {"id": 58, "name": "EV058", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 4}, {"id": 59, "name": "EV059", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 14}, {"id": 60, "name": "EV060", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 33, "y": 4}, {"id": 61, "name": "EV061", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 44, "y": 26}, {"id": 62, "name": "EV062", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 41, "y": 35}, {"id": 63, "name": "EV063", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 47, "y": 35}, {"id": 64, "name": "EV064", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 31, "y": 12}, {"id": 65, "name": "EV065", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 12}, {"id": 66, "name": "EV066", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 43, "y": 24}, {"id": 67, "name": "EV067", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 40, "y": 33}, {"id": 68, "name": "EV068", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 46, "y": 33}, {"id": 69, "name": "EV069", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$Patreon_lantern_pole", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Orange>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 300>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 65%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 85%>"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$Patreon_lantern_pole", "direction": 6, "pattern": 0, "characterIndex": 0}, "list": [{"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 7}, {"id": 70, "name": "EV070", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 201, "indent": 0, "parameters": [0, 214, 23, 38, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 16, "y": 1}, {"id": 71, "name": "EV071", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 201, "indent": 0, "parameters": [0, 214, 16, 2, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 23, "y": 39}, {"id": 72, "name": "EV072", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 45, "y": 24}, {"id": 73, "name": "EV073", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 42, "y": 33}, {"id": 74, "name": "EV074", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 48, "y": 33}, {"id": 75, "name": "EV075", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "birds", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Ambience SFX: Skylark>"]}, {"code": 408, "indent": 0, "parameters": ["<Ambience Interval: 500>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 18}, {"id": 76, "name": "EV076", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 44, "y": 24}, {"id": 77, "name": "EV077", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 41, "y": 33}, {"id": 78, "name": "EV078", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "butterflys_dragonflies", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Hide Shadow>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 1, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 0, "walkAnime": true}], "x": 38, "y": 15}, {"id": 79, "name": "EV079", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 47, "y": 33}, {"id": 80, "name": "EV080", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 41, "y": 24}, {"id": 81, "name": "EV081", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 0, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 47, "y": 24}, {"id": 82, "name": "EV082", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 2}, {"id": 83, "name": "EV083", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 33, "y": 2}, {"id": 84, "name": "EV084", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 10}, {"id": 85, "name": "EV085", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 20}, {"id": 86, "name": "EV086", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 25}, {"id": 87, "name": "EV087", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 10}, {"id": 88, "name": "EV088", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 20}, {"id": 89, "name": "EV089", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 25}, {"id": 90, "name": "EV090", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$Patreon_lantern_pole", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Orange>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 300>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 65%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 85%>"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$Patreon_lantern_pole", "direction": 8, "pattern": 0, "characterIndex": 0}, "list": [{"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 27, "y": 14}, {"id": 91, "name": "EV091", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$Patreon_lantern_pole", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Orange>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 300>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 65%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 85%>"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$Patreon_lantern_pole", "direction": 6, "pattern": 0, "characterIndex": 0}, "list": [{"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 29}, {"id": 92, "name": "EV092", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Signs", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 19}, {"id": 93, "name": "EV093", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "butterflys_dragonflies", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Hide Shadow>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 1, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 0, "walkAnime": true}], "x": 9, "y": 28}, {"id": 94, "name": "EV094", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Signs", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 29}, {"id": 95, "name": "EV095", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$Patreon_lantern_pole", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Orange>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 300>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 65%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 85%>"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$Patreon_lantern_pole", "direction": 8, "pattern": 0, "characterIndex": 0}, "list": [{"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 14}, null, {"id": 97, "name": "EV097", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 474, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 43, "y": 14}, null, null, {"id": 100, "name": "EV100", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Fall", "volume": 20, "pitch": 70, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 32, 4, 1, 0, 0]}, {"code": 357, "indent": 0, "parameters": ["MOG_Weather_EX", "weatherEXRemoveAll", "Remove All", {}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 20}, null, null]}