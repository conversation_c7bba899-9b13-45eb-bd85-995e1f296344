//=============================================================================
// EventSparkingEffect.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc (v1.0) Adds sparking effects to events to simulate damaged machines.
 * <AUTHOR> Name
 * @url https://yourwebsite.com
 *
 * @param sparkColor
 * @text Spark Color
 * @desc The color of the sparks (hex color code).
 * @default #FFFF00
 * @type string
 *
 * @param sparkSize
 * @text Spark Size
 * @desc The size of individual sparks.
 * @default 3
 * @type number
 * @min 1
 * @max 10
 *
 * @param sparkCount
 * @text Spark Count
 * @desc The number of sparks to generate.
 * @default 8
 * @type number
 * @min 1
 * @max 20
 *
 * @param sparkSpeed
 * @text Spark Speed
 * @desc The speed of spark movement.
 * @default 2
 * @type number
 * @min 0.5
 * @max 5
 * @decimals 1
 *
 * @param sparkLifetime
 * @text Spark Lifetime
 * @desc How long sparks last before disappearing (in frames).
 * @default 60
 * @type number
 * @min 20
 * @max 120
 *
 * @param sparkFlicker
 * @text Spark Flicker
 * @desc Whether sparks should flicker on and off.
 * @default true
 * @type boolean
 *
 * @help
 * ============================================================================
 * Event Sparking Effect Plugin
 * ============================================================================
 * 
 * This plugin adds a notetag system to make events appear to be sparking
 * like damaged machines. Perfect for creating atmosphere in sci-fi or
 * industrial settings.
 * 
 * ============================================================================
 * NOTETAGS
 * ============================================================================
 * 
 * Add this notetag to any event to enable sparking:
 * <spark>
 * 
 * You can also customize the sparking for individual events:
 * <spark:color=#FF0000,size=5,count=12,speed=3>
 * 
 * Available parameters:
 * - color: Hex color code (e.g., #FF0000 for red)
 * - size: Spark size (1-10)
 * - count: Number of sparks (1-20)
 * - speed: Movement speed (0.5-5.0)
 * - lifetime: Duration in frames (20-120)
 * - flicker: true/false
 * 
 * ============================================================================
 * EXAMPLES
 * ============================================================================
 * 
 * Basic sparking:
 * <spark>
 * 
 * Red sparks with custom settings:
 * <spark:color=#FF0000,size=4,count=10,speed=2.5>
 * 
 * Blue flickering sparks:
 * <spark:color=#0088FF,size=2,count=15,flicker=true>
 * 
 * ============================================================================
 * TERMS OF USE
 * ============================================================================
 * 
 * This plugin is free for both commercial and non-commercial use.
 * Attribution is appreciated but not required.
 * 
 * @license MIT
 */

(() => {
    'use strict';

    // Plugin parameters
    const pluginName = "EventSparkingEffect";
    const parameters = PluginManager.parameters(pluginName);
    
    const SPARK_COLOR = parameters['sparkColor'] || '#4080FF'; // Blue sparks
    const SPARK_SIZE = Number(parameters['sparkSize']) || 1.5; // Much smaller
    const SPARK_COUNT = Number(parameters['sparkCount']) || 4; // Fewer sparks
    const SPARK_SPEED = Number(parameters['sparkSpeed']) || 3; // Faster initial speed
    const SPARK_LIFETIME = Number(parameters['sparkLifetime']) || 30; // Shorter lifetime
    const SPARK_FLICKER = parameters['sparkFlicker'] === 'true';

    //=============================================================================
    // Electrical Arc Class
    //=============================================================================

    class ElectricalArc extends Sprite {
        constructor(x, y, settings) {
            super();
            this.x = x;
            this.y = y;
            this.life = 8 + Math.random() * 12; // Very short life (8-20 frames)
            this.maxLife = this.life;
            this.color = settings.color;
            this.visible = true;
            this._destroyed = false;

            // Arc properties
            this.length = 8 + Math.random() * 16; // 8-24 pixel length
            this.angle = Math.random() * Math.PI * 2; // Random direction
            this.thickness = 0.5 + Math.random() * 1; // Very thin lines
            this.segments = 3 + Math.floor(Math.random() * 4); // 3-6 segments for jagged look

            this.createArcBitmap();
            this.anchor.set(0.5, 0.5);
            this.blendMode = PIXI.BLEND_MODES.ADD;
        }

        createArcBitmap() {
            const size = Math.max(32, this.length * 2); // Canvas big enough for arc
            const bitmap = new Bitmap(size, size);
            const ctx = bitmap.context;
            const center = size / 2;

            // Convert hex color to RGB
            const hexColor = this.color.replace('#', '');
            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);

            // Draw jagged electrical arc
            ctx.strokeStyle = `rgba(255, 255, 255, 0.9)`; // Bright white core
            ctx.lineWidth = this.thickness;
            ctx.lineCap = 'round';

            // Create jagged path
            const startX = center - Math.cos(this.angle) * this.length / 2;
            const startY = center - Math.sin(this.angle) * this.length / 2;
            const endX = center + Math.cos(this.angle) * this.length / 2;
            const endY = center + Math.sin(this.angle) * this.length / 2;

            ctx.beginPath();
            ctx.moveTo(startX, startY);

            // Create jagged segments
            for (let i = 1; i < this.segments; i++) {
                const t = i / this.segments;
                const baseX = startX + (endX - startX) * t;
                const baseY = startY + (endY - startY) * t;

                // Add random jaggedness perpendicular to the arc
                const perpAngle = this.angle + Math.PI / 2;
                const jag = (Math.random() - 0.5) * 6; // Random offset
                const jagX = baseX + Math.cos(perpAngle) * jag;
                const jagY = baseY + Math.sin(perpAngle) * jag;

                ctx.lineTo(jagX, jagY);
            }

            ctx.lineTo(endX, endY);
            ctx.stroke();

            // Add colored glow
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, 0.6)`;
            ctx.lineWidth = this.thickness + 1;
            ctx.stroke();

            this.bitmap = bitmap;
        }

        update() {
            if (this._destroyed) return false;

            this.life--;

            // Very rapid flickering for electrical effect
            this.visible = Math.random() > 0.4; // Flickers 60% of the time

            // Fade out quickly
            const alpha = this.life / this.maxLife;
            this.opacity = alpha * 255;

            if (this.life <= 0) {
                this._destroyed = true;
                return false;
            }

            return true;
        }

        destroy(options) {
            this._destroyed = true;
            if (this.bitmap) {
                this.bitmap.destroy();
                this.bitmap = null;
            }
            super.destroy(options);
        }
    }

    //=============================================================================
    // Spark Class (Using Bitmap/Canvas approach like FF6 Airship)
    //=============================================================================

    class Spark extends Sprite {
        constructor(x, y, settings) {
            super();
            this.x = x;
            this.y = y;

            // More natural spark physics
            const angle = Math.random() * Math.PI * 2; // Random direction
            const speed = settings.speed * (0.5 + Math.random() * 0.5); // Variable speed
            this.vx = Math.cos(angle) * speed;
            this.vy = Math.sin(angle) * speed - 1; // Slight upward bias

            this.life = settings.lifetime * (0.7 + Math.random() * 0.6); // Variable lifetime
            this.maxLife = this.life;
            this.size = settings.size * (0.5 + Math.random() * 0.5); // Variable size
            this.color = settings.color;
            this.flicker = settings.flicker;
            this.visible = true;
            this._destroyed = false;

            // Physics properties for natural behavior
            this.gravity = 0.15; // Gravity effect
            this.airResistance = 0.96; // Air resistance
            this.sparkIntensity = 1.0; // Initial brightness

            // Create bitmap for the spark using canvas
            this.createSparkBitmap();
            this.anchor.set(0.5, 0.5);
            this.blendMode = PIXI.BLEND_MODES.ADD; // Additive blending for glow effect
        }

        createSparkBitmap() {
            // Much smaller spark - more realistic
            const size = Math.max(4, this.size * 2); // Smaller canvas
            const bitmap = new Bitmap(size, size);
            const ctx = bitmap.context;
            const center = size / 2;

            // Convert hex color to RGB for gradient
            const hexColor = this.color.replace('#', '');
            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);

            // Create tighter radial gradient for smaller, more intense spark
            const coreRadius = Math.max(0.5, this.size * 0.3);
            const glowRadius = Math.max(1, this.size * 0.8);

            const gradient = ctx.createRadialGradient(center, center, 0, center, center, glowRadius);
            gradient.addColorStop(0, `rgba(255, 255, 255, 1.0)`); // Bright white core
            gradient.addColorStop(0.2, `rgba(${r}, ${g}, ${b}, 0.9)`); // Color transition
            gradient.addColorStop(0.5, `rgba(${r}, ${g}, ${b}, 0.6)`); // Main color
            gradient.addColorStop(0.8, `rgba(${r}, ${g}, ${b}, 0.2)`); // Fade
            gradient.addColorStop(1, 'rgba(255,255,255,0)'); // Transparent edge

            // Draw the spark
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(center, center, glowRadius, 0, Math.PI * 2);
            ctx.fill();

            // Add very bright pinpoint core
            ctx.fillStyle = 'rgba(255,255,255,1.0)';
            ctx.beginPath();
            ctx.arc(center, center, coreRadius, 0, Math.PI * 2);
            ctx.fill();

            this.bitmap = bitmap;
        }

        update() {
            if (this._destroyed) return false;

            // Natural spark physics
            this.x += this.vx;
            this.y += this.vy;
            this.life--;

            // Apply gravity and air resistance for realistic movement
            this.vy += this.gravity; // Gravity pulls down
            this.vx *= this.airResistance; // Air resistance slows horizontal movement
            this.vy *= this.airResistance; // Air resistance slows vertical movement

            // Spark intensity fades over time
            this.sparkIntensity = this.life / this.maxLife;

            // Update opacity with more dramatic fade
            const lifeFactor = this.life / this.maxLife;
            const alpha = Math.pow(lifeFactor, 0.5); // Square root for more natural fade
            this.opacity = Math.max(0.05, alpha * 255);

            // More aggressive flickering for electrical effect
            if (this.flicker) {
                this.visible = Math.random() > 0.3; // More frequent flickering
            }

            // Random intensity variations for electrical crackling effect
            if (Math.random() < 0.1) {
                this.opacity *= (0.3 + Math.random() * 0.7); // Random brightness variations
            }

            if (this.life <= 0) {
                this._destroyed = true;
                return false;
            }

            return true;
        }

        destroy(options) {
            this._destroyed = true;
            if (this.bitmap) {
                this.bitmap.destroy();
                this.bitmap = null;
            }
            super.destroy(options);
        }
    }

    //=============================================================================
    // Event Spark Manager (attached to Scene_Map)
    //=============================================================================

    class EventSparkManager {
        constructor(scene) {
            this.scene = scene;
            this.sparkingEvents = new Map(); // eventId -> spark data
            this.sparkSprites = [];
            this.arcSprites = []; // Electrical arcs
            this.sparkTimer = 0;
            this.arcTimer = 0;
            this.sparkInterval = 15; // Base interval for sparks
            this.arcInterval = 8; // Base interval for arcs
            this.nextSparkDelay = this.getRandomizedInterval(this.sparkInterval);
            this.nextArcDelay = this.getRandomizedInterval(this.arcInterval);

            // Create spark layer as child of spriteset for automatic zoom compatibility
            this.sparkLayer = new Sprite();
            this.sparkLayer.z = 4; // Above characters but below UI

            // Add to spriteset instead of scene for automatic zoom scaling
            if (scene._spriteset) {
                scene._spriteset.addChild(this.sparkLayer);
            } else {
                scene.addChild(this.sparkLayer);
            }

            console.log('EventSparkingEffect: EventSparkManager created');
        }

        addSparkingEvent(eventId, settings) {
            this.sparkingEvents.set(eventId, {
                settings: settings,
                lastSparkTime: 0
            });
            console.log(`EventSparkingEffect: Added sparking event ${eventId}:`, settings);
        }

        removeSparkingEvent(eventId) {
            this.sparkingEvents.delete(eventId);
        }

        update() {
            // Update spark generation with randomized breaks
            this.sparkTimer++;
            if (this.sparkTimer >= this.nextSparkDelay) {
                this.sparkTimer = 0;
                this.updateSparks();
                // Set next randomized delay
                this.nextSparkDelay = this.getRandomizedInterval(this.sparkInterval);
            }

            // Update arc generation with randomized breaks
            this.arcTimer++;
            if (this.arcTimer >= this.nextArcDelay) {
                this.arcTimer = 0;
                this.updateArcs();
                // Set next randomized delay
                this.nextArcDelay = this.getRandomizedInterval(this.arcInterval);
            }

            // Update spark layer position to match spriteset zoom (VisuStella compatibility)
            this.updateSparkLayerPosition();

            // Update existing spark sprites
            this.sparkSprites = this.sparkSprites.filter(spark => {
                const alive = spark.update();
                if (!alive) {
                    this.sparkLayer.removeChild(spark);
                    spark.destroy();
                }
                return alive;
            });

            // Update existing arc sprites
            this.arcSprites = this.arcSprites.filter(arc => {
                const alive = arc.update();
                if (!alive) {
                    this.sparkLayer.removeChild(arc);
                    arc.destroy();
                }
                return alive;
            });
        }

        updateSparkLayerPosition() {
            // Match the spriteset's position and scale for VisuStella Camera Zoom compatibility
            const spriteset = this.scene._spriteset;
            if (!spriteset) return;

            // The spark layer should NOT inherit the spriteset's scale since we handle scaling per-spark
            // But it should follow the spriteset's position offset
            this.sparkLayer.x = 0; // Keep at origin since we calculate absolute positions
            this.sparkLayer.y = 0;
        }

        getRandomizedInterval(baseInterval) {
            // Create randomized breaks - sometimes much longer pauses
            const rand = Math.random();
            if (rand < 0.15) {
                // 15% chance of long break (2-4x longer)
                return baseInterval * (2 + Math.random() * 2);
            } else if (rand < 0.35) {
                // 20% chance of medium break (1.5-2x longer)
                return baseInterval * (1.5 + Math.random() * 0.5);
            } else {
                // 65% chance of normal timing with small variation
                return baseInterval * (0.7 + Math.random() * 0.6);
            }
        }

        updateSparks() {
            // Generate sparks for each sparking event
            this.sparkingEvents.forEach((data, eventId) => {
                const event = $gameMap.event(eventId);
                if (!event) return;

                // Get event sprite position (relative to spriteset)
                const sprite = this.scene._spriteset.findTargetSprite(event);
                if (!sprite) return;

                // Use sprite's local position since spark layer is child of spriteset
                // This automatically inherits zoom scaling from the spriteset
                const screenX = sprite.x;
                const screenY = sprite.y - sprite.height * 0.3; // Above the event

                // Generate sparks (no manual zoom scaling needed)
                this.generateSparksAt(screenX, screenY, data.settings);
            });
        }

        updateArcs() {
            // Generate electrical arcs for each sparking event
            this.sparkingEvents.forEach((data, eventId) => {
                const event = $gameMap.event(eventId);
                if (!event) return;

                // Get event sprite position (relative to spriteset)
                const sprite = this.scene._spriteset.findTargetSprite(event);
                if (!sprite) return;

                // Use sprite's local position since spark layer is child of spriteset
                const screenX = sprite.x;
                const screenY = sprite.y - sprite.height * 0.3; // Above the event

                // Generate electrical arcs
                this.generateArcsAt(screenX, screenY, data.settings);
            });
        }

        generateSparksAt(x, y, settings) {
            // Limit total sparks for performance
            if (this.sparkSprites.length >= 20) return; // Fewer total sparks

            // More intermittent behavior with longer breaks
            const burstChance = Math.random();
            let sparksToGenerate = 0;

            if (burstChance < 0.5) {
                sparksToGenerate = 0; // No sparks 50% of the time (more breaks)
            } else if (burstChance < 0.8) {
                sparksToGenerate = 1; // Single spark 30% of the time
            } else if (burstChance < 0.95) {
                sparksToGenerate = 2; // Two sparks 15% of the time
            } else {
                sparksToGenerate = Math.min(settings.count, 3); // Burst 5% of the time
            }

            for (let i = 0; i < sparksToGenerate; i++) {
                // Smaller, more concentrated spawn area
                const baseSpread = 16; // Smaller spread
                const offsetX = (Math.random() - 0.5) * baseSpread;
                const offsetY = (Math.random() - 0.5) * baseSpread;

                const spark = new Spark(x + offsetX, y + offsetY, settings);

                this.sparkLayer.addChild(spark);
                this.sparkSprites.push(spark);
            }
        }

        generateArcsAt(x, y, settings) {
            // Limit total arcs for performance
            if (this.arcSprites.length >= 15) return; // Fewer total arcs

            // More intermittent arcs with longer quiet periods
            const arcChance = Math.random();
            let arcsToGenerate = 0;

            if (arcChance < 0.6) {
                arcsToGenerate = 0; // No arcs 60% of the time (more breaks)
            } else if (arcChance < 0.9) {
                arcsToGenerate = 1; // Single arc 30% of the time
            } else {
                arcsToGenerate = 2; // Two arcs 10% of the time
            }

            for (let i = 0; i < arcsToGenerate; i++) {
                // Very small spawn area for arcs - they're more localized
                const baseSpread = 12;
                const offsetX = (Math.random() - 0.5) * baseSpread;
                const offsetY = (Math.random() - 0.5) * baseSpread;

                const arc = new ElectricalArc(x + offsetX, y + offsetY, settings);

                this.sparkLayer.addChild(arc);
                this.arcSprites.push(arc);
            }
        }

        destroy() {
            this.sparkSprites.forEach(spark => {
                spark.destroy();
            });
            this.sparkSprites = [];

            if (this.sparkLayer && this.sparkLayer.parent) {
                this.sparkLayer.parent.removeChild(this.sparkLayer);
            }
            this.sparkLayer.destroy();
        }
    }

    // Utility functions
    function parseSparkSettings(eventData) {
        if (!eventData || !eventData.note) return null;

        const note = eventData.note;

        // Check for both <spark> and <spark:...> tags
        if (!note.includes('<spark>') && !note.includes('<spark:')) {
            return null;
        }

        console.log('EventSparkingEffect: Spark tag found! Creating sparking effect');
        const settings = getDefaultSettings();

        // Parse custom parameters if they exist
        const match = note.match(/<spark:(.*?)>/);
        if (match) {
            const params = match[1].split(',');
            params.forEach(param => {
                const [key, value] = param.split('=');
                if (key && value) {
                    switch (key.trim()) {
                        case 'color':
                            settings.color = value.trim();
                            break;
                        case 'size':
                            settings.size = Number(value.trim());
                            break;
                        case 'count':
                            settings.count = Number(value.trim());
                            break;
                        case 'speed':
                            settings.speed = Number(value.trim());
                            break;
                        case 'lifetime':
                            settings.lifetime = Number(value.trim());
                            break;
                        case 'flicker':
                            settings.flicker = value.trim() === 'true';
                            break;
                    }
                }
            });
        }

        return settings;
    }

    function getDefaultSettings() {
        return {
            color: SPARK_COLOR,
            size: SPARK_SIZE, // Now 1.5 (much smaller)
            count: SPARK_COUNT, // Now 4 (fewer sparks)
            speed: SPARK_SPEED, // Now 3 (faster)
            lifetime: SPARK_LIFETIME, // Now 30 (shorter)
            flicker: SPARK_FLICKER
        };
    }

    //=============================================================================
    // Plugin Commands
    //=============================================================================

    PluginManager.registerCommand(pluginName, "ToggleSparking", args => {
        const eventId = Number(args.eventId);
        const enabled = args.enabled === 'true';
        
        const event = $gameMap.event(eventId);
        if (event) {
            if (enabled) {
                event.setMeta('spark', 'true');
            } else {
                event.setMeta('spark', 'false');
            }
            $gameMap.requestRefresh();
        }
    });

    //=============================================================================
    // Scene_Map Integration (Following FF6 Airship pattern)
    //=============================================================================

    const _Scene_Map_createSpriteset = Scene_Map.prototype.createSpriteset;
    Scene_Map.prototype.createSpriteset = function() {
        _Scene_Map_createSpriteset.call(this);

        // Create spark manager after spriteset is created (for VisuStella Camera Zoom compatibility)
        this._sparkManager = new EventSparkManager(this);

        console.log('EventSparkingEffect: Scene_Map spriteset created with spark manager');
    };

    const _Scene_Map_update = Scene_Map.prototype.update;
    Scene_Map.prototype.update = function() {
        _Scene_Map_update.call(this);

        // Update spark manager
        if (this._sparkManager) {
            this._sparkManager.update();
        }
    };

    const _Scene_Map_terminate = Scene_Map.prototype.terminate;
    Scene_Map.prototype.terminate = function() {
        // Clean up spark manager
        if (this._sparkManager) {
            this._sparkManager.destroy();
            this._sparkManager = null;
        }

        _Scene_Map_terminate.call(this);
    };

    //=============================================================================
    // Game_Event Override
    //=============================================================================

    const _Game_Event_initialize = Game_Event.prototype.initialize;
    Game_Event.prototype.initialize = function(mapId, eventId) {
        _Game_Event_initialize.call(this, mapId, eventId);
        this._sparkEnabled = this.hasSparkNotetag();

        if (this._sparkEnabled) {
            console.log('EventSparkingEffect: Event initialized with spark enabled, ID:', eventId);

            // Register with spark manager when scene is ready
            setTimeout(() => {
                if (SceneManager._scene && SceneManager._scene._sparkManager) {
                    const settings = parseSparkSettings($dataMap.events[eventId]);
                    if (settings) {
                        SceneManager._scene._sparkManager.addSparkingEvent(eventId, settings);
                    }
                }
            }, 100);
        }
    };

    Game_Event.prototype.hasSparkNotetag = function() {
        const eventData = $dataMap.events[this.eventId()];
        return eventData && eventData.note &&
               (eventData.note.includes('<spark>') || eventData.note.includes('<spark:'));
    };

    //=============================================================================
    // Spriteset_Map Helper Methods
    //=============================================================================

    Spriteset_Map.prototype.findTargetSprite = function(target) {
        return this._characterSprites.find(sprite => sprite._character === target);
    };

    //=============================================================================
    // Utility Functions
    //=============================================================================

    // Add a method to check if an event should spark
    Game_Event.prototype.shouldSpark = function() {
        return this._sparkEnabled;
    };

    // Debug function to check all events for spark tags
    window.checkSparkEvents = function() {
        console.log('EventSparkingEffect: Checking all events for spark tags...');
        if (!$dataMap || !$dataMap.events) {
            console.log('EventSparkingEffect: No map data available');
            return;
        }

        $dataMap.events.forEach((eventData, index) => {
            if (eventData && eventData.note &&
                (eventData.note.includes('<spark>') || eventData.note.includes('<spark:'))) {
                console.log(`EventSparkingEffect: Event ${index} has spark tag:`, eventData.note);
            }
        });
    };

    // Debug function to manually force spark creation
    window.forceCreateSparks = function() {
        console.log('EventSparkingEffect: Manually forcing spark creation...');
        if (SceneManager._scene && SceneManager._scene._sparkManager) {
            // Find all events with spark tags and add them
            $dataMap.events.forEach((eventData, index) => {
                if (eventData && eventData.note &&
                    (eventData.note.includes('<spark>') || eventData.note.includes('<spark:'))) {
                    const settings = parseSparkSettings(eventData);
                    if (settings) {
                        SceneManager._scene._sparkManager.addSparkingEvent(index, settings);
                        console.log(`Added sparking event ${index} to manager`);
                    }
                }
            });
        }
    };

    // Debug function to test spark visibility
    window.testSparks = function() {
        console.log('EventSparkingEffect: Testing spark creation...');
        if (SceneManager._scene && SceneManager._scene._sparkManager) {
            console.log('Spark manager found:', SceneManager._scene._sparkManager);
            console.log('Active sparking events:', SceneManager._scene._sparkManager.sparkingEvents);
            console.log('Current spark sprites:', SceneManager._scene._sparkManager.sparkSprites.length);
        }
    };

    // Call debug function when plugin loads
    console.log('EventSparkingEffect: Plugin loaded successfully');
    
    // Enhanced Scene_Map integration
    const _Scene_Map_start = Scene_Map.prototype.start;
    Scene_Map.prototype.start = function() {
        _Scene_Map_start.call(this);

        // Initialize sparking events after scene starts
        setTimeout(() => {
            if (typeof checkSparkEvents === 'function') {
                checkSparkEvents();
            }

            // Initialize all sparking events
            if (this._sparkManager && $dataMap && $dataMap.events) {
                $dataMap.events.forEach((eventData, index) => {
                    if (eventData && eventData.note &&
                        (eventData.note.includes('<spark>') || eventData.note.includes('<spark:'))) {
                        const settings = parseSparkSettings(eventData);
                        if (settings) {
                            this._sparkManager.addSparkingEvent(index, settings);
                            console.log(`EventSparkingEffect: Initialized sparking event ${index}`);
                        }
                    }
                });
            }
        }, 500);
    };

    // Also hook into map refresh to ensure sparks work after map changes
    const _Game_Map_refresh = Game_Map.prototype.refresh;
    Game_Map.prototype.refresh = function() {
        _Game_Map_refresh.call(this);

        // Refresh sparking events after map refresh
        setTimeout(() => {
            if (SceneManager._scene && SceneManager._scene._sparkManager) {
                // Re-initialize sparking events
                if ($dataMap && $dataMap.events) {
                    $dataMap.events.forEach((eventData, index) => {
                        if (eventData && eventData.note &&
                            (eventData.note.includes('<spark>') || eventData.note.includes('<spark:'))) {
                            const settings = parseSparkSettings(eventData);
                            if (settings) {
                                SceneManager._scene._sparkManager.addSparkingEvent(index, settings);
                            }
                        }
                    });
                }
            }
        }, 100);
    };

})();

