//=============================================================================
// EventSparkingEffect.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc (v1.0) Adds sparking effects to events to simulate damaged machines.
 * <AUTHOR> Name
 * @url https://yourwebsite.com
 *
 * @param sparkColor
 * @text Spark Color
 * @desc The color of the sparks (hex color code).
 * @default #FFFF00
 * @type string
 *
 * @param sparkSize
 * @text Spark Size
 * @desc The size of individual sparks.
 * @default 3
 * @type number
 * @min 1
 * @max 10
 *
 * @param sparkCount
 * @text Spark Count
 * @desc The number of sparks to generate.
 * @default 8
 * @type number
 * @min 1
 * @max 20
 *
 * @param sparkSpeed
 * @text Spark Speed
 * @desc The speed of spark movement.
 * @default 2
 * @type number
 * @min 0.5
 * @max 5
 * @decimals 1
 *
 * @param sparkLifetime
 * @text Spark Lifetime
 * @desc How long sparks last before disappearing (in frames).
 * @default 60
 * @type number
 * @min 20
 * @max 120
 *
 * @param sparkFlicker
 * @text Spark Flicker
 * @desc Whether sparks should flicker on and off.
 * @default true
 * @type boolean
 *
 * @help
 * ============================================================================
 * Event Sparking Effect Plugin
 * ============================================================================
 * 
 * This plugin adds a notetag system to make events appear to be sparking
 * like damaged machines. Perfect for creating atmosphere in sci-fi or
 * industrial settings.
 * 
 * ============================================================================
 * NOTETAGS
 * ============================================================================
 * 
 * Add this notetag to any event to enable sparking:
 * <spark>
 * 
 * You can also customize the sparking for individual events:
 * <spark:color=#FF0000,size=5,count=12,speed=3>
 * 
 * Available parameters:
 * - color: Hex color code (e.g., #FF0000 for red)
 * - size: Spark size (1-10)
 * - count: Number of sparks (1-20)
 * - speed: Movement speed (0.5-5.0)
 * - lifetime: Duration in frames (20-120)
 * - flicker: true/false
 * 
 * ============================================================================
 * EXAMPLES
 * ============================================================================
 * 
 * Basic sparking:
 * <spark>
 * 
 * Red sparks with custom settings:
 * <spark:color=#FF0000,size=4,count=10,speed=2.5>
 * 
 * Blue flickering sparks:
 * <spark:color=#0088FF,size=2,count=15,flicker=true>
 * 
 * ============================================================================
 * TERMS OF USE
 * ============================================================================
 * 
 * This plugin is free for both commercial and non-commercial use.
 * Attribution is appreciated but not required.
 * 
 * @license MIT
 */

(() => {
    'use strict';

    // Plugin parameters
    const pluginName = "EventSparkingEffect";
    const parameters = PluginManager.parameters(pluginName);
    
    const SPARK_COLOR = parameters['sparkColor'] || '#FFFF00';
    const SPARK_SIZE = Number(parameters['sparkSize']) || 3;
    const SPARK_COUNT = Number(parameters['sparkCount']) || 8;
    const SPARK_SPEED = Number(parameters['sparkSpeed']) || 2;
    const SPARK_LIFETIME = Number(parameters['sparkLifetime']) || 60;
    const SPARK_FLICKER = parameters['sparkFlicker'] === 'true';

    //=============================================================================
    // Spark Class (Pixi.js Graphics)
    //=============================================================================

    class Spark {
        constructor(x, y, settings) {
            this.x = x;
            this.y = y;
            this.vx = (Math.random() - 0.5) * settings.speed;
            this.vy = (Math.random() - 0.5) * settings.speed;
            this.life = settings.lifetime;
            this.maxLife = settings.lifetime;
            this.size = settings.size;
            this.color = this.hexToNumber(settings.color);
            this.flicker = settings.flicker;
            this.visible = true;
            
            // Create Pixi.js graphics for the spark
            this.graphics = new PIXI.Graphics();
            this.updateGraphics();
        }

        hexToNumber(hex) {
            return parseInt(hex.replace('#', ''), 16);
        }

        updateGraphics() {
            this.graphics.clear();
            if (!this.visible) return;

            const alpha = Math.max(0.3, this.life / this.maxLife); // Minimum alpha for visibility

            // Draw main spark body
            this.graphics.beginFill(this.color, alpha);
            this.graphics.drawCircle(0, 0, this.size);
            this.graphics.endFill();

            // Add bright white core for better visibility
            this.graphics.beginFill(0xFFFFFF, alpha * 0.8);
            this.graphics.drawCircle(0, 0, Math.max(1, this.size * 0.5));
            this.graphics.endFill();

            // Add outer glow
            this.graphics.lineStyle(1, this.color, alpha * 0.6);
            this.graphics.drawCircle(0, 0, this.size + 1);
        }

        update() {
            this.x += this.vx;
            this.y += this.vy;
            this.life--;
            
            if (this.flicker) {
                this.visible = Math.random() > 0.1;
            }
            
            // Update position and graphics
            this.graphics.x = this.x;
            this.graphics.y = this.y;
            this.updateGraphics();
            
            return this.life > 0;
        }

        destroy() {
            if (this.graphics && this.graphics.parent) {
                this.graphics.parent.removeChild(this.graphics);
            }
            this.graphics.destroy();
        }
    }

    //=============================================================================
    // Sparking Event Sprite
    //=============================================================================

    class SparkingEventSprite extends Sprite_Character {
        constructor(character) {
            super(character);
            this._sparks = [];
            this._sparkSettings = this.parseSparkSettings();
            this._sparkTimer = 0;
            this._sparkInterval = 3; // Generate new sparks every 3 frames
            this._sparkContainer = new PIXI.Container();

            // Add spark container as a child of this sprite
            this.addChild(this._sparkContainer);

            console.log('EventSparkingEffect: SparkingEventSprite created for event:', character.eventId());
            console.log('EventSparkingEffect: Spark settings:', this._sparkSettings);

            // Generate initial sparks if settings are valid
            if (this._sparkSettings) {
                this.generateSparks();
            }
        }

        parseSparkSettings() {
            const event = this._character.event();
            if (!event) {
                console.log('EventSparkingEffect: No event found');
                return null;
            }

            // Get the event data from the event list
            const eventData = $dataMap.events[event.eventId()];
            if (!eventData) {
                console.log('EventSparkingEffect: No event data found for ID:', event.eventId());
                return null;
            }

            const note = eventData.note;
            console.log('EventSparkingEffect: Event note:', note);

            // Check for both <spark> and <spark:...> tags
            if (!note || (!note.includes('<spark>') && !note.includes('<spark:'))) {
                console.log('EventSparkingEffect: No spark tag found in note');
                return null;
            }

            console.log('EventSparkingEffect: Spark tag found! Creating sparking effect');
            const settings = this.getDefaultSettings();

            // Parse custom parameters if they exist
            const match = note.match(/<spark:(.*?)>/);
            if (match) {
                const params = match[1].split(',');
                params.forEach(param => {
                    const [key, value] = param.split('=');
                    if (key && value) {
                        switch (key.trim()) {
                            case 'color':
                                settings.color = value.trim();
                                break;
                            case 'size':
                                settings.size = Number(value.trim());
                                break;
                            case 'count':
                                settings.count = Number(value.trim());
                                break;
                            case 'speed':
                                settings.speed = Number(value.trim());
                                break;
                            case 'lifetime':
                                settings.lifetime = Number(value.trim());
                                break;
                            case 'flicker':
                                settings.flicker = value.trim() === 'true';
                                break;
                        }
                    }
                });
            }

            return settings;
        }

        getDefaultSettings() {
            return {
                color: SPARK_COLOR,
                size: SPARK_SIZE,
                count: SPARK_COUNT,
                speed: SPARK_SPEED,
                lifetime: SPARK_LIFETIME,
                flicker: SPARK_FLICKER
            };
        }

        update() {
            super.update();
            this.updateSparks();
        }

        updateSparks() {
            // Only update sparks if we have valid settings
            if (!this._sparkSettings) return;

            // Update existing sparks
            this._sparks = this._sparks.filter(spark => {
                const alive = spark.update();
                if (!alive) {
                    spark.destroy();
                }
                return alive;
            });

            // Generate new sparks periodically
            this._sparkTimer++;
            if (this._sparkTimer >= this._sparkInterval) {
                this._sparkTimer = 0;
                this.generateSparks();
            }
        }

        generateSparks() {
            if (!this._sparkSettings) return;

            // Limit total sparks to prevent performance issues
            if (this._sparks.length >= this._sparkSettings.count * 3) return;

            // Position sparks around the center of the event
            const centerX = 0; // Relative to sprite center
            const centerY = -Math.max(this.height * 0.25, 12); // Slightly above center, minimum 12px
            const spreadX = Math.max(this.width * 0.6, 24); // Minimum 24px spread
            const spreadY = Math.max(this.height * 0.6, 24); // Minimum 24px spread

            // Generate fewer sparks per frame for better performance
            const sparksToGenerate = Math.min(this._sparkSettings.count, 3);

            for (let i = 0; i < sparksToGenerate; i++) {
                const x = centerX + (Math.random() - 0.5) * spreadX;
                const y = centerY + (Math.random() - 0.5) * spreadY;
                const spark = new Spark(x, y, this._sparkSettings);
                this._sparkContainer.addChild(spark.graphics);
                this._sparks.push(spark);
            }

            console.log(`EventSparkingEffect: Generated ${sparksToGenerate} sparks, total active: ${this._sparks.length}`);
        }

        destroy() {
            // Clean up sparks
            this._sparks.forEach(spark => spark.destroy());
            this._sparks = [];
            
            if (this._sparkContainer) {
                this._sparkContainer.destroy();
            }
            
            super.destroy();
        }
    }

    //=============================================================================
    // Plugin Commands
    //=============================================================================

    PluginManager.registerCommand(pluginName, "ToggleSparking", args => {
        const eventId = Number(args.eventId);
        const enabled = args.enabled === 'true';
        
        const event = $gameMap.event(eventId);
        if (event) {
            if (enabled) {
                event.setMeta('spark', 'true');
            } else {
                event.setMeta('spark', 'false');
            }
            $gameMap.requestRefresh();
        }
    });

    //=============================================================================
    // Spriteset_Map Override - IMPROVED VERSION
    //=============================================================================

    const _Spriteset_Map_createCharacters = Spriteset_Map.prototype.createCharacters;
    Spriteset_Map.prototype.createCharacters = function() {
        _Spriteset_Map_createCharacters.call(this);
        // Use requestAnimationFrame for better timing
        requestAnimationFrame(() => {
            this.createSparkingEventSprites();
        });
    };

    Spriteset_Map.prototype.createSparkingEventSprites = function() {
        console.log('EventSparkingEffect: Creating sparking event sprites');

        this._characterSprites.forEach((sprite, index) => {
            if (sprite._character && sprite._character.event) {
                const event = sprite._character.event();
                if (event && event.eventId) {
                    const eventData = $dataMap.events[event.eventId()];
                    // Check for both <spark> and <spark:...> tags
                    if (eventData && eventData.note &&
                        (eventData.note.includes('<spark>') || eventData.note.includes('<spark:'))) {
                        console.log('EventSparkingEffect: Found event with spark tag, ID:', event.eventId());

                        // Create new sparking sprite
                        const newSprite = new SparkingEventSprite(sprite._character);

                        // Copy important properties from the original sprite
                        newSprite.x = sprite.x;
                        newSprite.y = sprite.y;
                        newSprite.visible = sprite.visible;
                        newSprite.opacity = sprite.opacity;
                        newSprite.anchor = sprite.anchor;
                        newSprite.scale = sprite.scale;

                        // Replace the sprite in the character sprites array
                        this._characterSprites[index] = newSprite;

                        // Remove old sprite and add new one to the tilemap
                        if (sprite.parent) {
                            const parent = sprite.parent;
                            const spriteIndex = parent.children.indexOf(sprite);
                            parent.removeChild(sprite);
                            parent.addChildAt(newSprite, spriteIndex);
                        } else {
                            this._tilemap.addChild(newSprite);
                        }

                        console.log('EventSparkingEffect: Replaced sprite with sparking version');
                    }
                }
            }
        });
    };

    //=============================================================================
    // Game_Event Override
    //=============================================================================

    const _Game_Event_initialize = Game_Event.prototype.initialize;
    Game_Event.prototype.initialize = function(mapId, eventId) {
        _Game_Event_initialize.call(this, mapId, eventId);
        this._sparkEnabled = this.hasSparkNotetag();
        
        if (this._sparkEnabled) {
            console.log('EventSparkingEffect: Event initialized with spark enabled, ID:', eventId);
        }
    };

    Game_Event.prototype.hasSparkNotetag = function() {
        const eventData = $dataMap.events[this.eventId()];
        return eventData && eventData.note &&
               (eventData.note.includes('<spark>') || eventData.note.includes('<spark:'));
    };

    //=============================================================================
    // Utility Functions
    //=============================================================================

    // Add a method to check if an event should spark
    Game_Event.prototype.shouldSpark = function() {
        return this._sparkEnabled;
    };

    // Debug function to check all events for spark tags
    window.checkSparkEvents = function() {
        console.log('EventSparkingEffect: Checking all events for spark tags...');
        if (!$dataMap || !$dataMap.events) {
            console.log('EventSparkingEffect: No map data available');
            return;
        }

        $dataMap.events.forEach((eventData, index) => {
            if (eventData && eventData.note &&
                (eventData.note.includes('<spark>') || eventData.note.includes('<spark:'))) {
                console.log(`EventSparkingEffect: Event ${index} has spark tag:`, eventData.note);
            }
        });
    };

    // Debug function to manually force spark creation
    window.forceCreateSparks = function() {
        console.log('EventSparkingEffect: Manually forcing spark creation...');
        if (SceneManager._scene && SceneManager._scene._spriteset) {
            SceneManager._scene._spriteset.createSparkingEventSprites();
        }
    };

    // Debug function to test spark visibility
    window.testSparks = function() {
        console.log('EventSparkingEffect: Testing spark creation...');
        if (SceneManager._scene && SceneManager._scene._spriteset && SceneManager._scene._spriteset._characterSprites) {
            SceneManager._scene._spriteset._characterSprites.forEach((sprite, index) => {
                if (sprite instanceof SparkingEventSprite) {
                    console.log(`Found SparkingEventSprite at index ${index}:`, sprite);
                    sprite.generateSparks();
                }
            });
        }
    };

    // Call debug function when plugin loads
    console.log('EventSparkingEffect: Plugin loaded successfully');
    
    // Enhanced Scene_Map integration
    const _Scene_Map_start = Scene_Map.prototype.start;
    Scene_Map.prototype.start = function() {
        _Scene_Map_start.call(this);

        // Wait a bit for everything to initialize, then check events
        setTimeout(() => {
            if (typeof checkSparkEvents === 'function') {
                checkSparkEvents();
            }
            // Try to create sparking sprites again as a fallback
            if (this._spriteset && this._spriteset.createSparkingEventSprites) {
                this._spriteset.createSparkingEventSprites();
            }
        }, 1000);
    };

    // Also hook into map refresh to ensure sparks work after map changes
    const _Game_Map_refresh = Game_Map.prototype.refresh;
    Game_Map.prototype.refresh = function() {
        _Game_Map_refresh.call(this);

        // Refresh sparking sprites after map refresh
        setTimeout(() => {
            if (SceneManager._scene && SceneManager._scene._spriteset &&
                SceneManager._scene._spriteset.createSparkingEventSprites) {
                SceneManager._scene._spriteset.createSparkingEventSprites();
            }
        }, 100);
    };

})();

