//=============================================================================
// EventSparkingEffect.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc (v1.0) Adds sparking effects to events to simulate damaged machines.
 * <AUTHOR> Name
 * @url https://yourwebsite.com
 *
 * @param sparkColor
 * @text Spark Color
 * @desc The color of the sparks (hex color code).
 * @default #FFFF00
 * @type string
 *
 * @param sparkSize
 * @text Spark Size
 * @desc The size of individual sparks.
 * @default 3
 * @type number
 * @min 1
 * @max 10
 *
 * @param sparkCount
 * @text Spark Count
 * @desc The number of sparks to generate.
 * @default 8
 * @type number
 * @min 1
 * @max 20
 *
 * @param sparkSpeed
 * @text Spark Speed
 * @desc The speed of spark movement.
 * @default 2
 * @type number
 * @min 0.5
 * @max 5
 * @decimals 1
 *
 * @param sparkLifetime
 * @text Spark Lifetime
 * @desc How long sparks last before disappearing (in frames).
 * @default 60
 * @type number
 * @min 20
 * @max 120
 *
 * @param sparkFlicker
 * @text Spark Flicker
 * @desc Whether sparks should flicker on and off.
 * @default true
 * @type boolean
 *
 * @help
 * ============================================================================
 * Event Sparking Effect Plugin
 * ============================================================================
 * 
 * This plugin adds a notetag system to make events appear to be sparking
 * like damaged machines. Perfect for creating atmosphere in sci-fi or
 * industrial settings.
 * 
 * ============================================================================
 * NOTETAGS
 * ============================================================================
 * 
 * Add this notetag to any event to enable sparking:
 * <spark>
 * 
 * You can also customize the sparking for individual events:
 * <spark:color=#FF0000,size=5,count=12,speed=3>
 * 
 * Available parameters:
 * - color: Hex color code (e.g., #FF0000 for red)
 * - size: Spark size (1-10)
 * - count: Number of sparks (1-20)
 * - speed: Movement speed (0.5-5.0)
 * - lifetime: Duration in frames (20-120)
 * - flicker: true/false
 * 
 * ============================================================================
 * EXAMPLES
 * ============================================================================
 * 
 * Basic sparking:
 * <spark>
 * 
 * Red sparks with custom settings:
 * <spark:color=#FF0000,size=4,count=10,speed=2.5>
 * 
 * Blue flickering sparks:
 * <spark:color=#0088FF,size=2,count=15,flicker=true>
 * 
 * ============================================================================
 * TERMS OF USE
 * ============================================================================
 * 
 * This plugin is free for both commercial and non-commercial use.
 * Attribution is appreciated but not required.
 * 
 * @license MIT
 */

(() => {
    'use strict';

    // Plugin parameters
    const pluginName = "EventSparkingEffect";
    const parameters = PluginManager.parameters(pluginName);
    
    const SPARK_COLOR = parameters['sparkColor'] || '#FFFF00';
    const SPARK_SIZE = Number(parameters['sparkSize']) || 3;
    const SPARK_COUNT = Number(parameters['sparkCount']) || 8;
    const SPARK_SPEED = Number(parameters['sparkSpeed']) || 2;
    const SPARK_LIFETIME = Number(parameters['sparkLifetime']) || 60;
    const SPARK_FLICKER = parameters['sparkFlicker'] === 'true';

    //=============================================================================
    // Spark Class (Using Bitmap/Canvas approach like FF6 Airship)
    //=============================================================================

    class Spark extends Sprite {
        constructor(x, y, settings) {
            super();
            this.x = x;
            this.y = y;
            this.vx = (Math.random() - 0.5) * settings.speed;
            this.vy = (Math.random() - 0.5) * settings.speed;
            this.life = settings.lifetime;
            this.maxLife = settings.lifetime;
            this.size = settings.size;
            this.color = settings.color;
            this.flicker = settings.flicker;
            this.visible = true;
            this._destroyed = false;

            // Create bitmap for the spark using canvas
            this.createSparkBitmap();
            this.anchor.set(0.5, 0.5);
            this.blendMode = PIXI.BLEND_MODES.ADD; // Additive blending for glow effect
        }

        createSparkBitmap() {
            const size = Math.max(8, this.size * 4); // Larger canvas for glow effect
            const bitmap = new Bitmap(size, size);
            const ctx = bitmap.context;
            const center = size / 2;

            // Convert hex color to RGB for gradient
            const hexColor = this.color.replace('#', '');
            const r = parseInt(hexColor.substr(0, 2), 16);
            const g = parseInt(hexColor.substr(2, 2), 16);
            const b = parseInt(hexColor.substr(4, 2), 16);

            // Create radial gradient for glow effect
            const gradient = ctx.createRadialGradient(center, center, 0, center, center, center);
            gradient.addColorStop(0, `rgba(${r}, ${g}, ${b}, 1.0)`); // Core color
            gradient.addColorStop(0.3, `rgba(${r}, ${g}, ${b}, 0.8)`); // Still strong color
            gradient.addColorStop(0.7, `rgba(${r}, ${g}, ${b}, 0.3)`); // Fade
            gradient.addColorStop(1, 'rgba(255,255,255,0)'); // Transparent edge

            // Draw the spark glow
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(center, center, center, 0, Math.PI * 2);
            ctx.fill();

            // Add bright white core
            ctx.fillStyle = 'rgba(255,255,255,0.9)';
            ctx.beginPath();
            ctx.arc(center, center, Math.max(1, this.size), 0, Math.PI * 2);
            ctx.fill();

            this.bitmap = bitmap;
        }

        update() {
            if (this._destroyed) return false;

            this.x += this.vx;
            this.y += this.vy;
            this.life--;

            // Update opacity based on remaining life
            const alpha = Math.max(0.1, this.life / this.maxLife);
            this.opacity = alpha * 255;

            if (this.flicker) {
                this.visible = Math.random() > 0.2;
            }

            // Add some gravity and air resistance
            this.vy += 0.1; // Slight gravity
            this.vx *= 0.98; // Air resistance
            this.vy *= 0.98;

            if (this.life <= 0) {
                this._destroyed = true;
                return false;
            }

            return true;
        }

        destroy(options) {
            this._destroyed = true;
            if (this.bitmap) {
                this.bitmap.destroy();
                this.bitmap = null;
            }
            super.destroy(options);
        }
    }

    //=============================================================================
    // Event Spark Manager (attached to Scene_Map)
    //=============================================================================

    class EventSparkManager {
        constructor(scene) {
            this.scene = scene;
            this.sparkingEvents = new Map(); // eventId -> spark data
            this.sparkSprites = [];
            this.sparkTimer = 0;
            this.sparkInterval = 5; // Generate sparks every 5 frames

            // Create spark layer (similar to FF6 Airship approach)
            this.sparkLayer = new Sprite();
            this.sparkLayer.z = 4; // Above characters but below UI
            scene.addChild(this.sparkLayer);

            console.log('EventSparkingEffect: EventSparkManager created');
        }

        addSparkingEvent(eventId, settings) {
            this.sparkingEvents.set(eventId, {
                settings: settings,
                lastSparkTime: 0
            });
            console.log(`EventSparkingEffect: Added sparking event ${eventId}:`, settings);
        }

        removeSparkingEvent(eventId) {
            this.sparkingEvents.delete(eventId);
        }

        update() {
            this.sparkTimer++;
            if (this.sparkTimer >= this.sparkInterval) {
                this.sparkTimer = 0;
                this.updateSparks();
            }

            // Update existing spark sprites
            this.sparkSprites = this.sparkSprites.filter(spark => {
                const alive = spark.update();
                if (!alive) {
                    this.sparkLayer.removeChild(spark);
                    spark.destroy();
                }
                return alive;
            });
        }

        updateSparks() {
            // Generate sparks for each sparking event
            this.sparkingEvents.forEach((data, eventId) => {
                const event = $gameMap.event(eventId);
                if (!event) return;

                // Get event screen position
                const sprite = this.scene._spriteset.findTargetSprite(event);
                if (!sprite) return;

                const screenX = sprite.x;
                const screenY = sprite.y - sprite.height * 0.3; // Above the event

                // Generate sparks
                this.generateSparksAt(screenX, screenY, data.settings);
            });
        }

        generateSparksAt(x, y, settings) {
            // Limit total sparks for performance
            if (this.sparkSprites.length >= 50) return;

            const sparksToGenerate = Math.min(settings.count, 2); // Fewer per frame

            for (let i = 0; i < sparksToGenerate; i++) {
                const offsetX = (Math.random() - 0.5) * 32; // 32px spread
                const offsetY = (Math.random() - 0.5) * 32;

                const spark = new Spark(x + offsetX, y + offsetY, settings);
                this.sparkLayer.addChild(spark);
                this.sparkSprites.push(spark);
            }
        }

        destroy() {
            this.sparkSprites.forEach(spark => {
                spark.destroy();
            });
            this.sparkSprites = [];

            if (this.sparkLayer && this.sparkLayer.parent) {
                this.sparkLayer.parent.removeChild(this.sparkLayer);
            }
            this.sparkLayer.destroy();
        }
    }

    // Utility functions
    function parseSparkSettings(eventData) {
        if (!eventData || !eventData.note) return null;

        const note = eventData.note;

        // Check for both <spark> and <spark:...> tags
        if (!note.includes('<spark>') && !note.includes('<spark:')) {
            return null;
        }

        console.log('EventSparkingEffect: Spark tag found! Creating sparking effect');
        const settings = getDefaultSettings();

        // Parse custom parameters if they exist
        const match = note.match(/<spark:(.*?)>/);
        if (match) {
            const params = match[1].split(',');
            params.forEach(param => {
                const [key, value] = param.split('=');
                if (key && value) {
                    switch (key.trim()) {
                        case 'color':
                            settings.color = value.trim();
                            break;
                        case 'size':
                            settings.size = Number(value.trim());
                            break;
                        case 'count':
                            settings.count = Number(value.trim());
                            break;
                        case 'speed':
                            settings.speed = Number(value.trim());
                            break;
                        case 'lifetime':
                            settings.lifetime = Number(value.trim());
                            break;
                        case 'flicker':
                            settings.flicker = value.trim() === 'true';
                            break;
                    }
                }
            });
        }

        return settings;
    }

    function getDefaultSettings() {
        return {
            color: SPARK_COLOR,
            size: SPARK_SIZE,
            count: SPARK_COUNT,
            speed: SPARK_SPEED,
            lifetime: SPARK_LIFETIME,
            flicker: SPARK_FLICKER
        };
    }

    //=============================================================================
    // Plugin Commands
    //=============================================================================

    PluginManager.registerCommand(pluginName, "ToggleSparking", args => {
        const eventId = Number(args.eventId);
        const enabled = args.enabled === 'true';
        
        const event = $gameMap.event(eventId);
        if (event) {
            if (enabled) {
                event.setMeta('spark', 'true');
            } else {
                event.setMeta('spark', 'false');
            }
            $gameMap.requestRefresh();
        }
    });

    //=============================================================================
    // Scene_Map Integration (Following FF6 Airship pattern)
    //=============================================================================

    const _Scene_Map_createDisplayObjects = Scene_Map.prototype.createDisplayObjects;
    Scene_Map.prototype.createDisplayObjects = function() {
        _Scene_Map_createDisplayObjects.call(this);

        // Create spark manager (similar to FF6 Airship effect layers)
        this._sparkManager = new EventSparkManager(this);

        console.log('EventSparkingEffect: Scene_Map display objects created with spark manager');
    };

    const _Scene_Map_update = Scene_Map.prototype.update;
    Scene_Map.prototype.update = function() {
        _Scene_Map_update.call(this);

        // Update spark manager
        if (this._sparkManager) {
            this._sparkManager.update();
        }
    };

    const _Scene_Map_terminate = Scene_Map.prototype.terminate;
    Scene_Map.prototype.terminate = function() {
        // Clean up spark manager
        if (this._sparkManager) {
            this._sparkManager.destroy();
            this._sparkManager = null;
        }

        _Scene_Map_terminate.call(this);
    };

    //=============================================================================
    // Game_Event Override
    //=============================================================================

    const _Game_Event_initialize = Game_Event.prototype.initialize;
    Game_Event.prototype.initialize = function(mapId, eventId) {
        _Game_Event_initialize.call(this, mapId, eventId);
        this._sparkEnabled = this.hasSparkNotetag();

        if (this._sparkEnabled) {
            console.log('EventSparkingEffect: Event initialized with spark enabled, ID:', eventId);

            // Register with spark manager when scene is ready
            setTimeout(() => {
                if (SceneManager._scene && SceneManager._scene._sparkManager) {
                    const settings = parseSparkSettings($dataMap.events[eventId]);
                    if (settings) {
                        SceneManager._scene._sparkManager.addSparkingEvent(eventId, settings);
                    }
                }
            }, 100);
        }
    };

    Game_Event.prototype.hasSparkNotetag = function() {
        const eventData = $dataMap.events[this.eventId()];
        return eventData && eventData.note &&
               (eventData.note.includes('<spark>') || eventData.note.includes('<spark:'));
    };

    //=============================================================================
    // Spriteset_Map Helper Methods
    //=============================================================================

    Spriteset_Map.prototype.findTargetSprite = function(target) {
        return this._characterSprites.find(sprite => sprite._character === target);
    };

    //=============================================================================
    // Utility Functions
    //=============================================================================

    // Add a method to check if an event should spark
    Game_Event.prototype.shouldSpark = function() {
        return this._sparkEnabled;
    };

    // Debug function to check all events for spark tags
    window.checkSparkEvents = function() {
        console.log('EventSparkingEffect: Checking all events for spark tags...');
        if (!$dataMap || !$dataMap.events) {
            console.log('EventSparkingEffect: No map data available');
            return;
        }

        $dataMap.events.forEach((eventData, index) => {
            if (eventData && eventData.note &&
                (eventData.note.includes('<spark>') || eventData.note.includes('<spark:'))) {
                console.log(`EventSparkingEffect: Event ${index} has spark tag:`, eventData.note);
            }
        });
    };

    // Debug function to manually force spark creation
    window.forceCreateSparks = function() {
        console.log('EventSparkingEffect: Manually forcing spark creation...');
        if (SceneManager._scene && SceneManager._scene._sparkManager) {
            // Find all events with spark tags and add them
            $dataMap.events.forEach((eventData, index) => {
                if (eventData && eventData.note &&
                    (eventData.note.includes('<spark>') || eventData.note.includes('<spark:'))) {
                    const settings = parseSparkSettings(eventData);
                    if (settings) {
                        SceneManager._scene._sparkManager.addSparkingEvent(index, settings);
                        console.log(`Added sparking event ${index} to manager`);
                    }
                }
            });
        }
    };

    // Debug function to test spark visibility
    window.testSparks = function() {
        console.log('EventSparkingEffect: Testing spark creation...');
        if (SceneManager._scene && SceneManager._scene._sparkManager) {
            console.log('Spark manager found:', SceneManager._scene._sparkManager);
            console.log('Active sparking events:', SceneManager._scene._sparkManager.sparkingEvents);
            console.log('Current spark sprites:', SceneManager._scene._sparkManager.sparkSprites.length);
        }
    };

    // Call debug function when plugin loads
    console.log('EventSparkingEffect: Plugin loaded successfully');
    
    // Enhanced Scene_Map integration
    const _Scene_Map_start = Scene_Map.prototype.start;
    Scene_Map.prototype.start = function() {
        _Scene_Map_start.call(this);

        // Initialize sparking events after scene starts
        setTimeout(() => {
            if (typeof checkSparkEvents === 'function') {
                checkSparkEvents();
            }

            // Initialize all sparking events
            if (this._sparkManager && $dataMap && $dataMap.events) {
                $dataMap.events.forEach((eventData, index) => {
                    if (eventData && eventData.note &&
                        (eventData.note.includes('<spark>') || eventData.note.includes('<spark:'))) {
                        const settings = parseSparkSettings(eventData);
                        if (settings) {
                            this._sparkManager.addSparkingEvent(index, settings);
                            console.log(`EventSparkingEffect: Initialized sparking event ${index}`);
                        }
                    }
                });
            }
        }, 500);
    };

    // Also hook into map refresh to ensure sparks work after map changes
    const _Game_Map_refresh = Game_Map.prototype.refresh;
    Game_Map.prototype.refresh = function() {
        _Game_Map_refresh.call(this);

        // Refresh sparking events after map refresh
        setTimeout(() => {
            if (SceneManager._scene && SceneManager._scene._sparkManager) {
                // Re-initialize sparking events
                if ($dataMap && $dataMap.events) {
                    $dataMap.events.forEach((eventData, index) => {
                        if (eventData && eventData.note &&
                            (eventData.note.includes('<spark>') || eventData.note.includes('<spark:'))) {
                            const settings = parseSparkSettings(eventData);
                            if (settings) {
                                SceneManager._scene._sparkManager.addSparkingEvent(index, settings);
                            }
                        }
                    });
                }
            }
        }, 100);
    };

})();

