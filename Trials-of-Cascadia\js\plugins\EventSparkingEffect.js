//=============================================================================
// EventSparkingEffect.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc (v1.0) Adds sparking effects to events to simulate damaged machines.
 * <AUTHOR> Name
 * @url https://yourwebsite.com
 *
 * @param sparkColor
 * @text Spark Color
 * @desc The color of the sparks (hex color code).
 * @default #FFFF00
 * @type string
 *
 * @param sparkSize
 * @text Spark Size
 * @desc The size of individual sparks.
 * @default 3
 * @type number
 * @min 1
 * @max 10
 *
 * @param sparkCount
 * @text Spark Count
 * @desc The number of sparks to generate.
 * @default 8
 * @type number
 * @min 1
 * @max 20
 *
 * @param sparkSpeed
 * @text Spark Speed
 * @desc The speed of spark movement.
 * @default 2
 * @type number
 * @min 0.5
 * @max 5
 * @decimals 1
 *
 * @param sparkLifetime
 * @text Spark Lifetime
 * @desc How long sparks last before disappearing (in frames).
 * @default 60
 * @type number
 * @min 20
 * @max 120
 *
 * @param sparkFlicker
 * @text Spark Flicker
 * @desc Whether sparks should flicker on and off.
 * @default true
 * @type boolean
 *
 * @help
 * ============================================================================
 * Event Sparking Effect Plugin
 * ============================================================================
 * 
 * This plugin adds a notetag system to make events appear to be sparking
 * like damaged machines. Perfect for creating atmosphere in sci-fi or
 * industrial settings.
 * 
 * ============================================================================
 * NOTETAGS
 * ============================================================================
 * 
 * Add this notetag to any event to enable sparking:
 * <spark>
 * 
 * You can also customize the sparking for individual events:
 * <spark:color=#FF0000,size=5,count=12,speed=3>
 * 
 * Available parameters:
 * - color: Hex color code (e.g., #FF0000 for red)
 * - size: Spark size (1-10)
 * - count: Number of sparks (1-20)
 * - speed: Movement speed (0.5-5.0)
 * - lifetime: Duration in frames (20-120)
 * - flicker: true/false
 * 
 * ============================================================================
 * EXAMPLES
 * ============================================================================
 * 
 * Basic sparking:
 * <spark>
 * 
 * Red sparks with custom settings:
 * <spark:color=#FF0000,size=4,count=10,speed=2.5>
 * 
 * Blue flickering sparks:
 * <spark:color=#0088FF,size=2,count=15,flicker=true>
 * 
 * ============================================================================
 * TERMS OF USE
 * ============================================================================
 * 
 * This plugin is free for both commercial and non-commercial use.
 * Attribution is appreciated but not required.
 * 
 * @license MIT
 */

(() => {
    'use strict';

    // Plugin parameters
    const pluginName = "EventSparkingEffect";
    const parameters = PluginManager.parameters(pluginName);
    
    const SPARK_COLOR = parameters['sparkColor'] || '#FFFF00';
    const SPARK_SIZE = Number(parameters['sparkSize']) || 6;
    const SPARK_COUNT = Number(parameters['sparkCount']) || 12;
    const SPARK_SPEED = Number(parameters['sparkSpeed']) || 3;
    const SPARK_LIFETIME = Number(parameters['sparkLifetime']) || 45;
    const SPARK_FLICKER = parameters['sparkFlicker'] === 'true';

    //=============================================================================
    // Spark Class (Pixi.js Graphics)
    //=============================================================================

    class Spark {
        constructor(x, y, settings) {
            this.x = x;
            this.y = y;
            this.vx = (Math.random() - 0.5) * settings.speed;
            this.vy = (Math.random() - 0.5) * settings.speed;
            this.life = settings.lifetime;
            this.maxLife = settings.lifetime;
            this.size = settings.size;
            this.color = this.hexToNumber(settings.color);
            this.flicker = settings.flicker;
            this.visible = true;
            
            // Create Pixi.js graphics for the spark
            this.graphics = new PIXI.Graphics();
            this.updateGraphics();
        }

        hexToNumber(hex) {
            return parseInt(hex.replace('#', ''), 16);
        }

        updateGraphics() {
            this.graphics.clear();
            if (!this.visible) return;
            
            const alpha = this.life / this.maxLife;
            this.graphics.beginFill(this.color, alpha);
            this.graphics.drawCircle(0, 0, this.size);
            this.graphics.endFill();
            
            // Add white outline for better visibility
            this.graphics.lineStyle(1, 0xFFFFFF, alpha * 0.8);
            this.graphics.drawCircle(0, 0, this.size);
        }

        update() {
            this.x += this.vx;
            this.y += this.vy;
            this.life--;
            
            if (this.flicker) {
                this.visible = Math.random() > 0.1;
            }
            
            // Update position and graphics
            this.graphics.x = this.x;
            this.graphics.y = this.y;
            this.updateGraphics();
            
            return this.life > 0;
        }

        destroy() {
            if (this.graphics && this.graphics.parent) {
                this.graphics.parent.removeChild(this.graphics);
            }
            this.graphics.destroy();
        }
    }

    //=============================================================================
    // Sparking Event Sprite
    //=============================================================================

    class SparkingEventSprite extends Sprite_Character {
        constructor(character) {
            super(character);
            this._sparks = [];
            this._sparkSettings = this.parseSparkSettings();
            this._sparkTimer = 0;
            this._sparkInterval = 3; // Generate new sparks every 3 frames
            this._sparkContainer = new PIXI.Container();
            
            // Add spark container as a child of this sprite
            this.addChild(this._sparkContainer);
            
            console.log('EventSparkingEffect: SparkingEventSprite created for event:', character.eventId());
        }

        parseSparkSettings() {
            const event = this._character.event();
            if (!event) {
                console.log('EventSparkingEffect: No event found');
                return this.getDefaultSettings();
            }
            
            // Get the event data from the event list
            const eventData = $dataMap.events[event.eventId()];
            if (!eventData) {
                console.log('EventSparkingEffect: No event data found for ID:', event.eventId());
                return this.getDefaultSettings();
            }
            
            const note = eventData.note;
            console.log('EventSparkingEffect: Event note:', note);
            
            if (!note || !note.includes('<spark')) {
                console.log('EventSparkingEffect: No spark tag found in note');
                return this.getDefaultSettings();
            }
            
            console.log('EventSparkingEffect: Spark tag found! Creating sparking effect');
            const settings = this.getDefaultSettings();
            
            // Parse custom parameters if they exist
            const match = note.match(/<spark:(.*?)>/);
            if (match) {
                const params = match[1].split(',');
                params.forEach(param => {
                    const [key, value] = param.split('=');
                    if (key && value) {
                        switch (key.trim()) {
                            case 'color':
                                settings.color = value.trim();
                                break;
                            case 'size':
                                settings.size = Number(value.trim());
                                break;
                            case 'count':
                                settings.count = Number(value.trim());
                                break;
                            case 'speed':
                                settings.speed = Number(value.trim());
                                break;
                            case 'lifetime':
                                settings.lifetime = Number(value.trim());
                                break;
                            case 'flicker':
                                settings.flicker = value.trim() === 'true';
                                break;
                        }
                    }
                });
            }
            
            return settings;
        }

        getDefaultSettings() {
            return {
                color: SPARK_COLOR,
                size: SPARK_SIZE,
                count: SPARK_COUNT,
                speed: SPARK_SPEED,
                lifetime: SPARK_LIFETIME,
                flicker: SPARK_FLICKER
            };
        }

        update() {
            super.update();
            this.updateSparks();
        }

        updateSparks() {
            // Update existing sparks
            this._sparks = this._sparks.filter(spark => {
                const alive = spark.update();
                if (!alive) {
                    spark.destroy();
                }
                return alive;
            });
            
            // Generate new sparks periodically
            this._sparkTimer++;
            if (this._sparkTimer >= this._sparkInterval) {
                this._sparkTimer = 0;
                this.generateSparks();
            }
        }

        generateSparks() {
            if (!this._sparkSettings) return;
            
            // Position sparks around the center of the event
            const centerX = 0; // Relative to sprite center
            const centerY = -this.height * 0.25; // Slightly above center
            
            for (let i = 0; i < this._sparkSettings.count; i++) {
                const x = centerX + (Math.random() - 0.5) * this.width * 0.6;
                const y = centerY + (Math.random() - 0.5) * this.height * 0.6;
                const spark = new Spark(x, y, this._sparkSettings);
                this._sparkContainer.addChild(spark.graphics);
                this._sparks.push(spark);
            }
            
            console.log(`EventSparkingEffect: Generated ${this._sparkSettings.count} sparks, total active: ${this._sparks.length}`);
        }

        destroy() {
            // Clean up sparks
            this._sparks.forEach(spark => spark.destroy());
            this._sparks = [];
            
            if (this._sparkContainer) {
                this._sparkContainer.destroy();
            }
            
            super.destroy();
        }
    }

    //=============================================================================
    // Plugin Commands
    //=============================================================================

    PluginManager.registerCommand(pluginName, "ToggleSparking", args => {
        const eventId = Number(args.eventId);
        const enabled = args.enabled === 'true';
        
        const event = $gameMap.event(eventId);
        if (event) {
            if (enabled) {
                event.setMeta('spark', 'true');
            } else {
                event.setMeta('spark', 'false');
            }
            $gameMap.requestRefresh();
        }
    });

    //=============================================================================
    // Spriteset_Map Override - FIXED VERSION
    //=============================================================================

    const _Spriteset_Map_createCharacters = Spriteset_Map.prototype.createCharacters;
    Spriteset_Map.prototype.createCharacters = function() {
        _Spriteset_Map_createCharacters.call(this);
        this.createSparkingEventSprites();
    };

    Spriteset_Map.prototype.createSparkingEventSprites = function() {
        console.log('EventSparkingEffect: Creating sparking event sprites');
        
        // Wait a bit for all sprites to be properly created
        setTimeout(() => {
            this._characterSprites.forEach((sprite, index) => {
                if (sprite._character && sprite._character.event) {
                    const event = sprite._character.event();
                    if (event && event.eventId) {
                        const eventData = $dataMap.events[event.eventId()];
                        if (eventData && eventData.note && eventData.note.includes('<spark')) {
                            console.log('EventSparkingEffect: Found event with spark tag, ID:', event.eventId());
                            
                            // Create new sparking sprite
                            const newSprite = new SparkingEventSprite(sprite._character);
                            
                            // Copy important properties from the original sprite
                            newSprite.x = sprite.x;
                            newSprite.y = sprite.y;
                            newSprite.visible = sprite.visible;
                            newSprite.opacity = sprite.opacity;
                            
                            // Replace the sprite in the character sprites array
                            this._characterSprites[index] = newSprite;
                            
                            // Remove old sprite and add new one to the tilemap
                            if (sprite.parent) {
                                sprite.parent.removeChild(sprite);
                            }
                            this._tilemap.addChild(newSprite);
                            
                            console.log('EventSparkingEffect: Replaced sprite with sparking version');
                        }
                    }
                }
            });
        }, 100);
    };

    //=============================================================================
    // Game_Event Override
    //=============================================================================

    const _Game_Event_initialize = Game_Event.prototype.initialize;
    Game_Event.prototype.initialize = function(mapId, eventId) {
        _Game_Event_initialize.call(this, mapId, eventId);
        this._sparkEnabled = this.hasSparkNotetag();
        
        if (this._sparkEnabled) {
            console.log('EventSparkingEffect: Event initialized with spark enabled, ID:', eventId);
        }
    };

    Game_Event.prototype.hasSparkNotetag = function() {
        const eventData = $dataMap.events[this.eventId()];
        return eventData && eventData.note && eventData.note.includes('<spark');
    };

    //=============================================================================
    // Utility Functions
    //=============================================================================

    // Add a method to check if an event should spark
    Game_Event.prototype.shouldSpark = function() {
        return this._sparkEnabled;
    };

    // Debug function to check all events for spark tags
    window.checkSparkEvents = function() {
        console.log('EventSparkingEffect: Checking all events for spark tags...');
        if (!$dataMap || !$dataMap.events) {
            console.log('EventSparkingEffect: No map data available');
            return;
        }
        
        $dataMap.events.forEach((eventData, index) => {
            if (eventData && eventData.note && eventData.note.includes('<spark')) {
                console.log(`EventSparkingEffect: Event ${index} has spark tag:`, eventData.note);
            }
        });
    };

    // Call debug function when plugin loads
    console.log('EventSparkingEffect: Plugin loaded successfully');
    
    // Wait for the game to be ready before checking events
    const _Scene_Map_start = Scene_Map.prototype.start;
    Scene_Map.prototype.start = function() {
        _Scene_Map_start.call(this);
        
        // Wait a bit for everything to initialize, then check events
        setTimeout(() => {
            if (typeof checkSparkEvents === 'function') {
                checkSparkEvents();
            }
        }, 2000);
    };

})();

