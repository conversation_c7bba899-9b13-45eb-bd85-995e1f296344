{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "LRPG2_TheWanderingBard_noPerc_noMel_Loop", "pan": 0, "pitch": 100, "volume": 70}, "bgs": {"name": "Afternoon_Birds_01_Loop", "pan": 0, "pitch": 100, "volume": 10}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 40, "note": "<Zoom: 200%>\n<Fog 1 Settings>\nName: !fogwhite\nOpacity: 125\n<PERSON><PERSON>roll: +0.5\nMap Locked\nColor Tint: 155, 0, 255, 0\n</Fog 1 Settings>", "parallaxLoopX": true, "parallaxLoopY": false, "parallaxName": "!Clouds", "parallaxShow": true, "parallaxSx": -2, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 45, "width": 40, "data": [5519, 841, 842, 5519, 3376, 1634, 3360, 3368, 1634, 3384, 3369, 2880, 2864, 2864, 2868, 2902, 1659, 1651, 2860, 2088, 2056, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2052, 2076, 2056, 2048, 2052, 2076, 2056, 2048, 2048, 2048, 848, 840, 843, 851, 3361, 3364, 3345, 3346, 3380, 2862, 3376, 2904, 2872, 2868, 2902, 3378, 3380, 1659, 1632, 3386, 2088, 2056, 2048, 2048, 2048, 2048, 2048, 2048, 2052, 2076, 2086, 2862, 2064, 2048, 2072, 2862, 2088, 2076, 2056, 2048, 6196, 6196, 6196, 6212, 3384, 3352, 3348, 3372, 3369, 1634, 3361, 3380, 2880, 2888, 3379, 3373, 3354, 3380, 1632, 3361, 3380, 2088, 2056, 2048, 2048, 2048, 2052, 2076, 2086, 3387, 3389, 1627, 2481, 2480, 2482, 1651, 3387, 3389, 2088, 2056, 6204, 6204, 6204, 6214, 2906, 3384, 3369, 1634, 3376, 1634, 3362, 3382, 2880, 2888, 3388, 2906, 3360, 3368, 1651, 3384, 3374, 3389, 2088, 2056, 2052, 2076, 2086, 3390, 1641, 1641, 1627, 1635, 2481, 2480, 2482, 1635, 1651, 1641, 2862, 2088, 3364, 3380, 2907, 2897, 2895, 2901, 3388, 1634, 3376, 1634, 3376, 2898, 2865, 2870, 2897, 2903, 3360, 3368, 1635, 1651, 1641, 1641, 2862, 2088, 2086, 3387, 3389, 1627, 1649, 1649, 1635, 1643, 2064, 2048, 2072, 1659, 1635, 1649, 1651, 2859, 3344, 3346, 3365, 3377, 3389, 2908, 1641, 1627, 3361, 3365, 3383, 2880, 2868, 2902, 3378, 3364, 3345, 3368, 1659, 1635, 1649, 1649, 1651, 1641, 1641, 1641, 1627, 1635, 1657, 1657, 1643, 2082, 2049, 2048, 2050, 2084, 1659, 1657, 1635, 1632, 3344, 3348, 3382, 1641, 1627, 1563, 1657, 1643, 3360, 3368, 1634, 2880, 2888, 3378, 3345, 3348, 3372, 3374, 3389, 1659, 1657, 1657, 1635, 1649, 1649, 1649, 1635, 1643, 2082, 2068, 2068, 2049, 2048, 2048, 2048, 2050, 2068, 2084, 1659, 1632, 3348, 3382, 1627, 1657, 1643, 3378, 3364, 3364, 3345, 3368, 1634, 2880, 2888, 3360, 3344, 3368, 5519, 841, 842, 5519, 3378, 3380, 1659, 1657, 1657, 1657, 1643, 2082, 2049, 2048, 2052, 2076, 2056, 2048, 2052, 2076, 2056, 2050, 2084, 1632, 3368, 1634, 1643, 3378, 3364, 3345, 3348, 3372, 3372, 3382, 1627, 2880, 2888, 3384, 3352, 3368, 848, 840, 843, 851, 3360, 3346, 3364, 3364, 3364, 3365, 3389, 2088, 2076, 2076, 2086, 1627, 2481, 2480, 2482, 1651, 2088, 2056, 2072, 1632, 3368, 1634, 3378, 3345, 3348, 3372, 3382, 1627, 1560, 1562, 1635, 2880, 2866, 2900, 3384, 3369, 6210, 6196, 6196, 6212, 3360, 3344, 3348, 3372, 3372, 3382, 1641, 1641, 1641, 1641, 1627, 1635, 2481, 2480, 2482, 1635, 1651, 2088, 2086, 3387, 3368, 1634, 3360, 3348, 3382, 1641, 1627, 1635, 1560, 1562, 1643, 2880, 2864, 2866, 2900, 3388, 6216, 6204, 6204, 6214, 3360, 3348, 3382, 1641, 1641, 1627, 1649, 1649, 1649, 1649, 1635, 1635, 2481, 2480, 2482, 1635, 1635, 1632, 5519, 841, 3382, 1627, 3362, 3382, 1627, 1649, 1635, 1643, 1560, 1562, 2898, 2865, 2864, 2864, 2866, 2885, 2897, 2903, 3378, 3364, 3349, 3382, 1627, 1649, 1649, 1635, 1649, 1649, 1649, 1649, 1635, 1643, 2064, 2048, 2072, 1659, 1635, 1632, 848, 840, 1634, 1643, 3376, 1634, 1635, 1657, 1643, 3378, 3365, 3389, 2882, 2892, 2872, 2868, 2892, 2902, 3387, 3377, 3373, 3372, 3382, 1627, 1635, 1649, 1649, 1635, 1657, 1657, 1657, 1657, 1643, 2082, 2049, 2048, 2050, 2084, 1659, 1632, 6210, 6196, 1627, 3378, 3370, 1634, 1643, 3378, 3364, 3345, 3368, 2899, 2903, 3386, 2882, 2902, 3379, 3389, 1641, 1641, 1641, 1641, 1627, 1635, 1635, 1657, 1657, 1643, 2082, 2068, 2068, 2068, 2068, 2049, 2048, 2048, 2048, 2050, 2084, 1632, 6216, 6204, 1643, 3360, 3368, 1634, 3378, 3345, 3344, 3344, 3368, 2896, 3387, 3383, 2908, 3387, 3383, 1634, 1649, 1649, 1649, 1649, 1635, 1635, 1643, 2082, 2068, 2068, 2049, 2048, 2048, 2052, 2076, 2076, 2076, 2056, 2048, 2048, 2072, 1651, 3387, 3366, 3364, 3345, 3368, 1634, 3360, 3344, 3344, 3348, 3382, 2896, 1634, 1560, 1561, 1562, 1640, 1642, 1649, 1649, 1649, 1649, 1635, 1643, 2082, 2049, 2048, 2048, 2048, 2048, 2052, 2086, 2898, 2884, 2900, 2088, 2056, 2048, 2072, 1635, 1651, 3384, 3344, 3348, 3382, 1627, 3384, 3372, 3372, 3382, 2907, 2903, 1627, 1560, 1561, 1562, 1648, 1650, 1625, 1626, 1649, 1649, 1643, 2091, 2057, 2048, 2048, 2048, 2052, 2076, 2086, 2898, 2865, 2864, 2866, 2900, 2088, 2056, 2072, 1635, 1635, 1632, 3348, 3382, 1627, 1643, 5519, 841, 842, 5519, 3390, 1627, 1635, 1560, 1561, 1562, 1656, 1658, 2858, 1634, 3379, 3389, 2899, 2909, 2064, 2048, 2048, 2048, 2072, 2907, 2886, 2865, 2864, 2864, 2864, 2866, 2900, 2064, 2072, 1659, 1635, 1632, 3368, 1634, 1643, 3386, 848, 840, 843, 851, 1634, 1635, 1643, 3390, 2906, 3378, 3364, 3380, 2848, 1634, 3376, 2898, 2890, 2091, 2057, 2048, 2048, 2048, 2050, 2084, 2904, 2872, 2864, 2864, 2864, 2868, 2902, 2064, 2050, 2084, 1659, 1632, 3368, 1634, 3387, 3371, 6210, 6196, 6196, 6212, 1634, 1643, 3390, 2899, 2903, 3362, 3372, 3382, 2860, 1627, 3388, 2880, 2888, 3386, 2088, 2056, 2048, 2048, 2048, 2050, 2084, 2904, 2892, 2872, 2868, 2902, 2082, 2049, 2048, 2054, 2093, 3378, 3346, 3380, 2862, 3376, 6216, 6204, 6204, 6214, 1634, 3386, 2899, 2903, 3379, 3383, 1641, 1641, 1627, 1643, 2898, 2869, 2902, 3363, 3389, 2088, 2076, 2056, 2048, 2048, 2050, 2068, 2084, 2904, 2902, 2082, 2049, 2048, 2052, 2086, 3378, 3345, 3344, 3368, 1634, 3361, 3364, 3364, 3365, 3389, 1627, 3376, 2896, 3379, 3383, 1627, 1657, 1657, 1643, 2898, 2869, 2902, 3379, 3383, 1641, 1641, 2862, 2088, 2076, 2076, 2056, 2048, 2050, 2068, 2068, 2049, 2052, 2076, 2086, 1632, 3360, 3344, 3344, 3368, 1634, 3360, 3344, 3348, 3382, 1627, 1635, 3376, 2896, 3388, 1634, 1643, 3387, 3389, 2898, 2869, 2902, 3387, 3383, 1627, 1657, 1657, 1651, 1641, 1641, 2862, 2088, 2076, 2056, 2048, 2048, 2052, 2086, 3378, 3380, 1651, 3384, 3352, 3344, 3368, 1634, 3384, 3352, 3368, 1634, 1635, 1643, 3388, 2908, 1641, 1627, 3390, 2898, 2884, 2869, 2902, 1641, 1641, 1627, 1643, 3387, 3389, 1659, 1657, 1657, 1651, 1641, 1641, 2088, 2056, 2052, 2086, 1632, 3360, 3368, 1635, 1651, 3384, 3344, 3346, 3380, 2862, 3384, 3382, 1634, 1643, 2862, 1627, 1563, 1657, 1643, 2898, 2865, 2868, 2902, 1627, 1657, 1657, 1643, 5519, 841, 842, 5519, 3378, 3380, 1659, 1657, 1657, 1651, 2064, 2072, 1641, 1632, 3360, 3368, 1659, 1635, 1632, 3344, 3344, 3346, 3380, 2859, 2861, 1627, 2862, 1627, 1643, 2906, 3390, 2899, 2893, 2892, 2902, 1627, 1643, 3378, 3364, 3380, 848, 840, 843, 851, 3360, 3346, 3364, 3364, 3380, 1659, 2481, 2482, 1657, 1651, 3384, 3374, 3381, 1659, 1632, 3372, 3372, 3372, 3374, 3377, 3381, 1635, 1634, 1643, 2094, 2883, 2897, 2903, 1627, 1560, 1562, 1643, 3378, 3345, 3344, 3368, 6210, 6196, 6196, 6212, 3360, 3344, 3344, 3344, 3350, 3389, 2064, 2050, 2084, 1659, 1651, 1641, 3385, 3381, 1632, 5458, 5458, 5458, 5458, 5462, 3361, 3364, 3380, 2907, 2886, 2890, 3390, 1627, 1643, 1560, 1562, 3387, 3373, 3352, 3344, 3368, 6216, 6204, 6204, 6214, 3360, 3344, 3344, 3344, 3368, 2082, 2049, 2048, 2050, 2084, 1659, 1657, 1651, 3385, 3366, 5456, 5456, 5456, 5456, 5460, 3384, 3352, 3346, 3380, 2904, 2889, 1634, 1643, 2907, 2897, 2886, 2884, 2900, 3384, 3372, 3354, 3380, 2906, 3378, 3364, 3345, 3348, 3372, 3372, 3382, 2088, 2056, 2048, 2048, 2050, 2068, 2084, 1659, 1632, 3360, 5464, 5464, 5464, 5464, 5468, 1634, 3360, 3344, 3368, 1634, 2896, 1634, 3378, 3364, 3380, 2904, 2872, 2866, 2884, 2900, 3384, 3382, 2896, 3384, 3352, 3344, 3368, 5519, 841, 842, 5519, 2064, 2048, 2048, 2048, 2048, 2050, 2084, 1632, 3360, 6196, 6196, 6196, 6196, 6212, 1634, 3360, 3344, 3368, 1634, 2896, 1634, 3360, 3344, 3346, 3380, 2904, 2892, 2872, 2866, 2884, 2884, 2867, 2900, 3360, 3344, 3368, 848, 840, 843, 851, 2088, 2056, 2048, 2048, 2048, 2048, 2072, 1632, 3360, 6204, 6204, 6204, 6204, 6214, 1634, 3360, 3344, 3368, 1634, 2896, 1634, 3384, 3352, 3344, 3346, 3364, 3380, 2904, 2892, 2872, 2864, 2864, 2888, 3384, 3372, 3369, 6210, 6196, 6196, 6212, 3386, 2064, 2048, 2048, 2048, 2052, 2086, 3378, 3345, 3364, 3364, 3364, 3365, 3389, 1627, 3360, 3348, 3382, 1627, 2896, 3390, 2862, 3384, 3356, 3372, 3372, 3374, 3377, 3389, 2904, 2872, 2864, 2866, 2884, 2900, 3388, 6216, 6204, 6204, 6214, 3376, 2064, 2048, 2048, 2048, 2072, 1632, 3360, 3344, 3344, 3344, 3344, 3368, 1634, 1635, 3360, 3368, 1634, 1635, 2881, 2900, 3386, 2862, 3376, 5459, 5458, 5458, 5458, 5458, 5462, 2904, 2892, 2892, 2892, 2894, 2897, 2897, 2903, 3378, 3365, 3383, 2064, 2048, 2048, 2048, 2072, 1632, 3360, 3344, 3344, 3348, 3372, 3382, 1627, 1643, 3360, 3368, 1634, 1635, 2880, 2888, 3376, 1634, 3388, 5457, 5456, 5456, 5456, 5456, 5460, 3387, 3377, 3377, 3377, 3366, 3364, 3364, 3364, 3345, 3368, 2082, 2049, 2048, 2048, 2048, 2072, 1632, 3360, 3344, 3348, 3382, 1641, 1627, 1635, 3387, 3373, 3382, 1627, 1635, 2880, 2888, 3361, 3380, 2862, 5465, 5464, 5464, 5464, 5464, 5468, 1625, 1625, 2859, 2861, 3384, 3372, 3372, 3352, 3344, 3368, 2064, 2048, 2048, 2048, 2048, 2072, 1632, 3360, 3344, 3382, 1627, 1649, 1635, 1643, 1641, 1641, 1627, 1635, 1632, 2880, 2888, 3360, 3346, 3380, 1625, 1625, 1625, 1625, 3378, 3364, 3365, 3377, 3377, 3381, 1625, 2859, 2861, 3384, 3372, 3382, 2064, 2048, 2048, 2048, 2048, 2048, 3387, 3373, 3372, 1627, 1635, 1657, 1643, 1627, 1649, 1649, 1635, 1635, 1632, 2880, 2888, 3384, 3352, 3346, 3364, 3364, 3364, 3364, 3345, 3344, 3368, 1625, 1625, 3361, 3364, 3364, 3380, 1625, 1625, 1625, 2088, 2076, 2048, 5123, 5122, 5122, 5122, 5122, 5126, 1635, 1643, 3386, 1634, 1635, 1649, 1649, 1635, 1635, 3390, 2880, 2866, 2900, 3384, 3352, 3344, 3344, 3344, 3344, 3344, 3344, 3346, 3364, 3364, 3345, 3344, 3344, 3350, 3377, 3377, 3377, 3377, 3389, 2048, 5121, 5120, 5120, 5120, 5120, 5124, 1643, 3378, 3370, 1627, 1635, 1649, 1649, 1635, 3386, 2898, 2865, 2864, 2866, 2900, 3360, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 3344, 3368, 1625, 1625, 1625, 1625, 1625, 3386, 5129, 5128, 5128, 5128, 5128, 5132, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3427, 3425, 3429, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3426, 3418, 0, 3433, 3437, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3432, 3422, 3437, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 665, 666, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 673, 674, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3427, 3425, 3429, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3427, 3431, 0, 3433, 3429, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2187, 2177, 2177, 2177, 2189, 3435, 3414, 3418, 0, 0, 0, 3409, 3428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3432, 3417, 0, 0, 0, 3410, 3430, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3433, 3425, 3414, 3413, 3431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3432, 3430, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3531, 3521, 3521, 3533, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3435, 3414, 3428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3432, 3430, 0, 3531, 3521, 3521, 3533, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3434, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3435, 3423, 3437, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3531, 3521, 3521, 3533, 0, 0, 3531, 3521, 3521, 3533, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 661, 662, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 669, 670, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 721, 667, 0, 721, 0, 0, 0, 0, 0, 0, 734, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 929, 0, 0, 932, 0, 0, 0, 721, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 683, 0, 710, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 677, 0, 0, 0, 0, 0, 0, 0, 574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 701, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 677, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 932, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 720, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 720, 0, 0, 573, 574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 683, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 720, 732, 0, 0, 721, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 720, 556, 557, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 724, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 677, 0, 0, 912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 677, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 733, 920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 677, 0, 0, 0, 0, 0, 0, 0, 0, 730, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 556, 557, 558, 920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 732, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 683, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 667, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 454, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 720, 0, 0, 0, 0, 0, 0, 721, 0, 0, 0, 721, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 462, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 698, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 573, 0, 0, 0, 0, 0, 0, 556, 557, 558, 0, 0, 0, 0, 0, 0, 677, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 932, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 717, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 719, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 721, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 730, 731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 706, 0, 0, 730, 0, 0, 0, 0, 0, 0, 721, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 732, 0, 0, 677, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 913, 914, 915, 916, 925, 559, 0, 706, 0, 0, 706, 0, 0, 0, 556, 557, 558, 0, 710, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 921, 922, 923, 924, 925, 567, 531, 532, 533, 534, 535, 0, 0, 0, 564, 565, 566, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 705, 0, 0, 0, 0, 0, 0, 0, 0, 20, 21, 0, 0, 933, 690, 539, 540, 541, 542, 543, 0, 0, 0, 572, 573, 574, 0, 0, 698, 0, 0, 0, 0, 0, 0, 0, 0, 0, 683, 0, 0, 0, 0, 0, 0, 0, 706, 0, 0, 28, 29, 0, 0, 0, 0, 547, 548, 549, 550, 551, 0, 0, 0, 0, 705, 0, 898, 899, 0, 559, 706, 0, 0, 0, 0, 0, 677, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 717, 0, 0, 719, 0, 0, 0, 0, 0, 556, 557, 558, 0, 0, 0, 0, 0, 699, 905, 906, 907, 908, 567, 0, 0, 0, 0, 724, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 566, 721, 0, 0, 0, 559, 912, 913, 906, 907, 916, 917, 0, 0, 0, 717, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 898, 533, 534, 535, 0, 0, 0, 0, 572, 573, 732, 0, 0, 0, 0, 567, 920, 913, 906, 907, 916, 925, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 905, 906, 541, 542, 543, 0, 0, 0, 0, 698, 677, 740, 0, 0, 0, 0, 0, 920, 913, 914, 915, 916, 925, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 912, 913, 906, 549, 550, 551, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 690, 920, 921, 922, 923, 924, 925, 0, 0, 0, 0, 719, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 920, 913, 906, 557, 558, 0, 0, 698, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 928, 929, 0, 18, 19, 933, 724, 683, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 920, 913, 906, 565, 566, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 699, 0, 0, 26, 559, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 920, 913, 914, 532, 533, 534, 535, 0, 898, 899, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 698, 567, 0, 0, 0, 0, 0, 0, 0, 675, 676, 0, 0, 0, 0, 0, 0, 0, 0, 920, 921, 922, 540, 541, 542, 543, 905, 906, 907, 908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 677, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 928, 929, 6, 548, 549, 550, 551, 913, 906, 907, 916, 917, 0, 0, 0, 0, 0, 710, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 556, 557, 558, 920, 913, 906, 907, 916, 925, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 719, 677, 564, 565, 566, 920, 913, 906, 907, 916, 925, 0, 0, 0, 0, 0, 0, 0, 0, 0, 675, 676, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 572, 573, 574, 920, 913, 906, 907, 916, 925, 0, 0, 0, 0, 0, 0, 0, 698, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 578, 0, 732, 0, 920, 913, 914, 915, 916, 925, 0, 0, 0, 0, 0, 0, 0, 0, 0, 667, 719, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 584, 585, 586, 719, 740, 0, 920, 921, 922, 923, 924, 925, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 474, 0, 0, 0, 0, 0, 592, 593, 594, 677, 0, 690, 928, 929, 18, 19, 932, 933, 0, 699, 0, 0, 0, 0, 677, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 600, 601, 602, 513, 514, 515, 0, 0, 26, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 898, 899, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 608, 609, 610, 521, 522, 523, 732, 0, 698, 690, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 578, 579, 0, 905, 906, 907, 908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 728, 729, 0, 564, 565, 529, 530, 0, 740, 0, 0, 677, 0, 0, 0, 0, 0, 0, 0, 0, 0, 584, 585, 586, 587, 588, 913, 906, 907, 916, 917, 0, 705, 0, 0, 0, 0, 0, 0, 720, 0, 0, 0, 572, 573, 537, 538, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 698, 0, 0, 592, 593, 594, 595, 596, 913, 906, 907, 916, 925, 733, 0, 0, 0, 0, 0, 0, 0, 0, 0, 734, 0, 0, 0, 545, 546, 0, 721, 0, 706, 0, 0, 710, 0, 0, 0, 0, 0, 0, 0, 600, 601, 602, 603, 604, 913, 914, 915, 916, 925, 741, 0, 898, 899, 0, 0, 0, 0, 0, 661, 662, 531, 532, 533, 0, 710, 0, 677, 732, 731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 608, 609, 610, 611, 612, 921, 922, 923, 924, 925, 677, 905, 906, 907, 908, 0, 0, 0, 0, 669, 670, 539, 540, 541, 721, 0, 0, 0, 740, 0, 730, 578, 579, 0, 0, 0, 0, 0, 0, 0, 0, 564, 565, 566, 928, 929, 0, 6, 932, 933, 912, 913, 906, 907, 916, 917, 0, 0, 0, 0, 740, 547, 548, 549, 0, 0, 0, 0, 0, 584, 585, 586, 587, 588, 0, 0, 0, 0, 0, 0, 0, 572, 573, 574, 724, 0, 0, 14, 452, 453, 920, 913, 906, 907, 916, 925, 0, 0, 0, 0, 0, 0, 556, 557, 0, 0, 0, 0, 0, 592, 593, 594, 595, 596, 0, 0, 0, 512, 513, 514, 515, 0, 699, 0, 0, 0, 0, 690, 460, 461, 920, 913, 906, 907, 916, 925, 0, 0, 0, 0, 0, 0, 564, 565, 0, 0, 0, 0, 0, 600, 601, 602, 603, 604, 0, 0, 0, 520, 521, 522, 523, 0, 0, 0, 0, 0, 0, 0, 0, 0, 920, 913, 914, 915, 916, 925, 0, 0, 0, 0, 0, 0, 572, 732, 19, 18, 19, 0, 0, 608, 609, 610, 611, 612, 0, 0, 0, 528, 529, 530, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 920, 921, 922, 923, 924, 925, 0, 0, 0, 0, 0, 0, 0, 740, 27, 26, 27, 0, 0, 0, 564, 565, 566, 0, 0, 0, 0, 536, 537, 538, 698, 0, 0, 559, 0, 0, 0, 0, 0, 0, 928, 929, 0, 18, 19, 933, 0, 0, 0, 0, 0, 720, 0, 0, 0, 0, 717, 559, 0, 0, 572, 573, 574, 0, 0, 0, 721, 544, 545, 546, 0, 0, 0, 567, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 27, 0, 0, 0, 0, 0, 0, 0, 0, 698, 0, 0, 0, 567, 0, 0, 0, 559, 0, 0, 0, 0, 677, 721, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 690, 0, 706, 0, 724, 0, 0, 732, 0, 567, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 677, 0, 0, 0, 0, 0, 0, 0, 732, 0, 667, 0, 0, 0, 0, 740, 0, 0, 0, 720, 0, 0, 0, 0, 512, 513, 514, 515, 0, 728, 729, 732, 0, 730, 731, 0, 0, 705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 740, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 699, 0, 0, 0, 683, 520, 521, 522, 523, 0, 0, 724, 740, 0, 0, 0, 0, 730, 731, 690, 0, 0, 0, 0, 0, 0, 0, 720, 699, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 528, 529, 530, 0, 0, 0, 0, 720, 531, 532, 533, 534, 535, 719, 0, 0, 0, 730, 731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 690, 0, 0, 0, 0, 0, 720, 0, 0, 0, 0, 677, 536, 537, 538, 698, 0, 728, 729, 0, 539, 540, 541, 542, 543, 731, 719, 0, 698, 0, 0, 728, 0, 0, 0, 0, 0, 0, 0, 733, 0, 0, 0, 0, 0, 720, 677, 0, 0, 0, 0, 0, 544, 545, 546, 728, 729, 0, 667, 0, 547, 548, 549, 550, 551, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "<sway_top>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$Glowing_tree", "direction": 8, "pattern": 2, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 1, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 31, "y": 18}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 33, "y": 8}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 2, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 8}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 2, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 34, "y": 8}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 2, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 31, "y": 26}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 2, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 26}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 2, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 4}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 2, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 34, "y": 4}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 33, "y": 4}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 2, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 34, "y": 2}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 4, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 33, "y": 2}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 2, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 2}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 2, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 31, "y": 25}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!waterfall_animation", "direction": 2, "pattern": 2, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 25}, {"id": 15, "name": "EV015", "note": "<Compass Icon: 3169>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door1", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 334, 11, 18, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 2, "y": 3}, {"id": 16, "name": "EV016", "note": "<Compass Icon: 3169>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door1", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<aura:purple>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Magic4", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, ""]}, {"code": 401, "indent": 0, "parameters": ["The door is blocked by some sort of force field."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door1", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 336, 3, 18, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 17, "y": 10}, {"id": 17, "name": "EV017", "note": "<Compass Icon: 3169>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Label: ◄>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 201, "indent": 0, "parameters": [0, 338, 13, 6, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 8, "y": 16}, {"id": 18, "name": "EV018", "note": "<Compass Icon: 3169>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door1", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 335, 3, 16, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 22, "y": 27}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 357, "indent": 0, "parameters": ["VisuMZ_2_LightingEffects", "OverlayChangeToPreset", "OVERLAY: Change to Preset Color", {"Color:str": "Night", "Duration:num": "0"}]}, {"code": 657, "indent": 0, "parameters": ["Color = Night"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 0"]}, {"code": 357, "indent": 0, "parameters": ["Wave 5/VisuMZ_2_HorrorEffects", "MapColorCreate", "Map: Color Effect Create", {"type:str": "Polaroid"}]}, {"code": 657, "indent": 0, "parameters": ["Effect Type = Polaroid"]}, {"code": 357, "indent": 0, "parameters": ["Wave 5/VisuMZ_2_HorrorEffects", "MapNoiseRemove", "Map: Noise Remove", {}]}, {"code": 357, "indent": 0, "parameters": ["VisuMZ_2_WeatherEffects", "Medium_Icons_Left", "MEDIUM: Flying Icons ←", {"MainData": "", "powerTarget:eval": "1", "duration:eval": "60", "WaitForCompletion:eval": "false", "LayerData": "", "Layer:arrayeval": "[\"1\"]", "UpperLower:str": "upper", "Customization": "", "Custom:struct": "{\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"720\\\",\\\"lifespanVariance:num\\\":\\\"0\\\",\\\"spawnLocationX:str\\\":\\\"right 30%\\\",\\\"spawnOffsetX:eval\\\":\\\"+0\\\",\\\"spawnLocationY:str\\\":\\\"random\\\",\\\"spawnOffsetY:eval\\\":\\\"+0\\\",\\\"mapBound:eval\\\":\\\"true\\\",\\\"opacity:num\\\":\\\"20\\\",\\\"opacityVariance:num\\\":\\\"15\\\",\\\"opacityEasingType:str\\\":\\\"InQuart\\\",\\\"opacityFadeInTime:num\\\":\\\"16\\\",\\\"scale:num\\\":\\\"0.1\\\",\\\"scaleVariance:num\\\":\\\"0\\\",\\\"scaleRatioX:num\\\":\\\"1.0\\\",\\\"scaleRatioY:num\\\":\\\"1.0\\\",\\\"totalMinimum:num\\\":\\\"10\\\",\\\"totalPerPower:num\\\":\\\"10\\\"}\",\"dimmer:struct\":\"{\\\"color:str\\\":\\\"#000000\\\",\\\"opacityMinimum:num\\\":\\\"0\\\",\\\"opacityPerPower:num\\\":\\\"0\\\"}\",\"image:struct\":\"{\\\"generated:eval\\\":\\\"false\\\",\\\"generatedWeight:num\\\":\\\"1\\\",\\\"icons:arraynum\\\":\\\"[]\\\",\\\"iconsWeight:num\\\":\\\"1\\\",\\\"pictures:arraystr\\\":\\\"[\\\\\\\"ghost_face\\\\\\\"]\\\",\\\"picturesWeight:num\\\":\\\"1\\\",\\\"blendMode:num\\\":\\\"0\\\",\\\"hueVariations:arraynum\\\":\\\"[]\\\",\\\"toneVariations:arrayeval\\\":\\\"[\\\\\\\"[155,0,255,0]\\\\\\\"]\\\"}\",\"flags:struct\":\"{\\\"alwaysVisiblePlayer:eval\\\":\\\"false\\\"}\",\"trajectory:struct\":\"{\\\"type:str\\\":\\\"straight\\\",\\\"lockedID:eval\\\":\\\"0\\\",\\\"lockedOffsetX:eval\\\":\\\"+0\\\",\\\"lockedOffsetY:eval\\\":\\\"+0\\\",\\\"speed:eval\\\":\\\"2\\\",\\\"speedVariance:eval\\\":\\\"1\\\",\\\"angle:eval\\\":\\\"180\\\",\\\"alignAngle:eval\\\":\\\"false\\\",\\\"angleVariance:eval\\\":\\\"0\\\",\\\"angleOffset:eval\\\":\\\"+0\\\",\\\"angleArc:eval\\\":\\\"+0\\\",\\\"angleSwayRange:eval\\\":\\\"0\\\",\\\"angleSwaySpeed:eval\\\":\\\"0.01\\\",\\\"spinSpeed:eval\\\":\\\"+0\\\",\\\"spinSpeedVariance:eval\\\":\\\"0\\\",\\\"reverseSpin:eval\\\":\\\"true\\\",\\\"xSwayRange:eval\\\":\\\"0\\\",\\\"xSwaySpeed:eval\\\":\\\"0.01\\\",\\\"ySwayRange:eval\\\":\\\"1\\\",\\\"ySwaySpeed:eval\\\":\\\"0.01\\\"}\"}"}]}, {"code": 657, "indent": 0, "parameters": ["Main Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Power = 1"]}, {"code": 657, "indent": 0, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 0, "parameters": ["Wait For Completion? = false"]}, {"code": 657, "indent": 0, "parameters": ["Layer Settings = "]}, {"code": 657, "indent": 0, "parameters": ["Layer(s) = [\"1\"]"]}, {"code": 657, "indent": 0, "parameters": ["Upper/Lower? = upper"]}, {"code": 657, "indent": 0, "parameters": ["Customization = "]}, {"code": 657, "indent": 0, "parameters": ["Custom Settings = {\"sprite:struct\":\"{\\\"lifespan:num\\\":\\\"720…"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 0}, {"id": 20, "name": "EV020", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$Big_Swamp_shrine", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 400>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 10%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 50%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 22}, {"id": 21, "name": "EV021", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$Patreon_lantern_pole", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 37}, {"id": 22, "name": "EV022", "note": "<Sprite Offset Y: -96>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 466, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 31, "y": 19}, {"id": 23, "name": "EV023", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$Big_Swamp_shrine", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 400>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 10%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 50%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 7}, {"id": 24, "name": "EV024", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$Big_Swamp_shrine", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: Cyan>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 400>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 10%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 50%>"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 31}, {"id": 25, "name": "EV025", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [11, "pageup", 1]}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 357, "indent": 2, "parameters": ["VisuMZ_4_MapCameraZoom", "ZoomChange", "Zoom: Change Zoom", {"TargetScale:num": "1.5", "Duration:num": "60", "EasingType:str": "Linear"}]}, {"code": 657, "indent": 2, "parameters": ["Target Zoom Scale = 1.5"]}, {"code": 657, "indent": 2, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 2, "parameters": ["Easing Type = Linear"]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 357, "indent": 2, "parameters": ["VisuMZ_4_MapCameraZoom", "ZoomChange", "Zoom: Change Zoom", {"TargetScale:num": "1.0", "Duration:num": "60", "EasingType:str": "Linear"}]}, {"code": 657, "indent": 2, "parameters": ["Target Zoom Scale = 1.0"]}, {"code": 657, "indent": 2, "parameters": ["Duration = 60"]}, {"code": 657, "indent": 2, "parameters": ["Easing Type = Linear"]}, {"code": 123, "indent": 2, "parameters": ["A", 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 0, "y": 1}, {"id": 26, "name": "EV026", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Ghost", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: White>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 45>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 50%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: 0, +12>"]}, {"code": 108, "indent": 0, "parameters": ["<aura: white>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["You... are you... alive? Please, you must help me!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["I have been here for a millennium. My spirit seeks closure."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["Please, help me find my family heirloom, an ivory hairpin."]}, {"code": 401, "indent": 0, "parameters": ["It was my mother's, given to me on my wedding day."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["It must be in one of these houses, but I cannot cross the"]}, {"code": 401, "indent": 0, "parameters": ["threshold into these houses. They are cursed you see..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["Thresholds are barriers between worlds. The dead cannot"]}, {"code": 401, "indent": 0, "parameters": ["cross them."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 1, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": true, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<aura: white>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["The cherry tree taught me that even the most beautiful"]}, {"code": 401, "indent": 0, "parameters": ["things must fall."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["I clung to these beads, thinking they would save me."]}, {"code": 401, "indent": 0, "parameters": ["But salvation lies in release."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["Thank you for completing my meditation. I can return to the sea now."]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 30}, {"id": 27, "name": "EV027", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Ghost", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: White>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 45>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 50%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: 0, +12>"]}, {"code": 108, "indent": 0, "parameters": ["<aura: white>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["You... are you... alive? Please, you must help me!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["I have been here for a millennium. My spirit seeks closure."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["Please, help me find my family heirloom, an ivory hairpin."]}, {"code": 401, "indent": 0, "parameters": ["It was my mother's, given to me on my wedding day."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["It must be in one of these houses, but I cannot cross the"]}, {"code": 401, "indent": 0, "parameters": ["threshold into these houses. They are cursed you see..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["Thresholds are barriers between worlds. The dead cannot"]}, {"code": 401, "indent": 0, "parameters": ["cross them."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 1, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": true, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<aura: white>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["The cherry tree taught me that even the most beautiful"]}, {"code": 401, "indent": 0, "parameters": ["things must fall."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["I clung to these beads, thinking they would save me."]}, {"code": 401, "indent": 0, "parameters": ["But salvation lies in release."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["Thank you for completing my meditation. I can return to the sea now."]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 18}, {"id": 28, "name": "EV028", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC-Ghost", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Radial Light Color: White>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Radius: 45>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Intensity: 1%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Opacity: 50%>"]}, {"code": 408, "indent": 0, "parameters": ["<Radial Light Offset: 0, +12>"]}, {"code": 108, "indent": 0, "parameters": ["<aura: white>"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["Halt! Who goes there? Identify yourself! You don't look"]}, {"code": 401, "indent": 0, "parameters": ["familiar..."]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["Please, spirit. We mean you no harm."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["You are alive? But how can you be here?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["Please... I've stood guard here for longer than I can"]}, {"code": 401, "indent": 0, "parameters": ["remember. The monsters never seem to stop coming."]}, {"code": 101, "indent": 0, "parameters": ["aiya-face", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["But there are no monsters out here."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["They hide inside the houses, where we cannot tread."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["There was a portrait I kept, of my family. It's in one of"]}, {"code": 401, "indent": 0, "parameters": ["the houses. But I cannot leave my post. Please find it and"]}, {"code": 401, "indent": 0, "parameters": ["bring it to me."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 1, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": true, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["Yes... that's her. I've held on for so long to her memory."]}, {"code": 401, "indent": 0, "parameters": ["But now I can let go, and finally return to the Darkness."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "<PERSON><PERSON>"]}, {"code": 401, "indent": 0, "parameters": ["But why do you want to return to the Darkness?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["The Light divides. Memories echo into silence."]}, {"code": 401, "indent": 0, "parameters": ["The Darkness dissolves barriers, makes us whole once again."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2, "Ghost"]}, {"code": 401, "indent": 0, "parameters": ["Thank you for freeing me from this curse. I only hope that"]}, {"code": 401, "indent": 0, "parameters": ["you can escape this place as well."]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 11}, {"id": 29, "name": "EV029", "note": "<Compass Icon: 3169>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Fantasy_door1", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"repeat": false, "skippable": false, "wait": true, "list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 337, 3, 16, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 28, "y": 32}]}